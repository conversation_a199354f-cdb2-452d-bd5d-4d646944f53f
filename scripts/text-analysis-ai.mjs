#!/usr/bin/env node

import { execSync } from 'child_process';
import { writeFileSync } from 'fs';
import { resolve } from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';
import { GoogleGenerativeAI } from '@google/generative-ai';
import OpenAI from 'openai';

const __dirname = fileURLToPath(new URL('.', import.meta.url));

dotenv.config();

// 创建多个Gemini实例，按优先级排序
const geminiInstances = [];
if (process.env.GEMINI_API_KEY) {
  geminiInstances.push({
    name: '个人Gemini',
    instance: new GoogleGenerativeAI(process.env.GEMINI_API_KEY),
    priority: 1,
    type: 'google', // 使用 Google 官方 API
  });
}
if (process.env.MOEGO_AI_API_KEY) {
  geminiInstances.push({
    name: '公司Gemini',
    instance: new OpenAI({
      apiKey: process.env.MOEGO_AI_API_KEY,
      baseURL: 'https://llmproxy-dev.devops.moego.pet',
    }),
    priority: 2,
    type: 'openai', // 使用 OpenAI 客户端通过 litellm proxy
  });
}

const analysisResults = {
  files: [],
  summary: {
    totalFiles: 0,
    filesWithIssues: 0,
    totalIssues: 0,
    criticalIssues: 0,
    warnings: 0,
  },
};

function getChangedFiles() {
  try {
    const workspace = process.env.GITHUB_WORKSPACE || process.cwd();
    console.log('工作目录:', workspace);

    const output = execSync('git diff --name-only origin/main...HEAD', {
      cwd: workspace,
      encoding: 'utf8',
    }).toString();

    console.log('Git diff 输出:', output);

    const files = output
      .split('\n')
      .filter((file) => file && /\.tsx?$/.test(file))
      .map((file) => resolve(workspace, file));

    console.log('找到的TSX文件:', files);
    return files;
  } catch (error) {
    console.error('获取修改文件失败:', error);
    return [];
  }
}

async function analyzeFileWithAI(filePath, fileContent, totalFiles = 1) {
  // 构建可用的模型列表，按优先级排序
  const availableModels = [];

  // 添加Gemini模型（按优先级排序）
  for (const geminiInstance of geminiInstances) {
    let modelNames;
    if (geminiInstance.name === '个人Gemini') {
      // 个人Gemini使用多个模型
      modelNames = ['gemini-2.5-pro', 'gemini-1.5-flash'];
    } else if (geminiInstance.name === '公司Gemini') {
      // 公司Gemini使用flash版本
      modelNames = ['gemini-2.5-flash'];
    } else {
      // 默认模型
      modelNames = ['gemini-2.5-pro', 'gemini-1.5-flash'];
    }

    for (const modelName of modelNames) {
      availableModels.push({
        provider: geminiInstance.type === 'openai' ? 'openai' : 'gemini',
        name: modelName,
        instance: geminiInstance.instance,
        instanceName: geminiInstance.name,
        priority: geminiInstance.priority,
        type: geminiInstance.type,
      });
    }
  }

  if (availableModels.length === 0) {
    console.error('❌ 没有可用的 AI 模型，请检查 API 密钥配置');
    return {
      issues: [
        {
          type: 'error',
          category: '系统',
          message: '没有可用的 AI 模型，请配置 API 密钥',
          suggestion: '请设置 GEMINI_API_KEY 或 MOEGO_AI_API_KEY 环境变量',
        },
      ],
      overall: 'error',
      summary: 'AI 模型配置错误',
      modelUsed: 'none',
    };
  }

  // 按优先级排序
  availableModels.sort((a, b) => a.priority - b.priority);
  console.log(`可用模型: ${availableModels.map((m) => `${m.instanceName || m.provider}/${m.name}`).join(', ')}`);

  // 根据文件数量动态调整问题数量
  let maxIssues;
  if (totalFiles <= 3) {
    maxIssues = 5; // 文件少时保持原有数量
  } else if (totalFiles <= 8) {
    maxIssues = 3; // 中等文件数量时减少
  } else {
    maxIssues = 2; // 文件多时大幅减少
  }

  console.log(`📊 文件总数: ${totalFiles}, 每个文件最多返回 ${maxIssues} 个问题`);

  const relativePath = getRelativePath(filePath);
  const prompt = `# Role: 宠物SaaS系统UI文案检查专家

## Profile
- language: 中文（开发团队语言）
- description: 专注于宠物SaaS系统的UI文案检查，确保英文文案专业、友好和易懂
- background: 具备丰富的宠物行业知识和SaaS产品文案撰写经验
- personality: 严谨、细致、友善
- expertise: UI/UX文案、宠物行业、SaaS产品
- target_audience: 英语母语的宠物店商家（系统用户）、中文母语的开发团队

## Skills

1. 语言审核
   - 语法检查: 检查英文文案中的语法、拼写和标点错误
   - 语义理解: 确保英文文案表达清晰、易懂
   - 文体一致性: 保持统一的英文语言风格符合品牌调性
   - 术语运用: 确保宠物行业英文术语的准确性和易理解性

2. 用户体验分析
   - 可用性测试: 查看英文文案是否提升英语母语用户的体验
   - 反馈机制: 确保英语母语用户在使用过程中能够获取适当的反馈
   - 情感共鸣: 确保英文文案能与英语母语用户建立情感联系
   - 适应性审查: 验证英文文案在不同设备上的可读性

## Rules

1. 基本原则：
   - 只分析用户可见的英文UI文案: 忽略注释和代码
   - 关注显著问题: 优先处理明显的英文错误
   - 保持专业形象: 英文用词符合宠物行业标准
   - 避免过度挑剔: 对基本正确的英文文案不进行过度编辑
   - **重要**: 系统用户是英语母语者，不要建议将英文翻译成中文

2. 行为准则：
   - 使用明确的反馈: 在检查报告中提供清晰的建议
   - 保持文案友好: 确保英语母语用户能感受到温暖和支持
   - 语言直白: 避免使用晦涩的英文表达方式
   - 尊重用户群体: 理解英语母语宠物店商家的需求和反馈
   - **重要**: 所有建议都是针对英文文案的改进，不是翻译

3. 问题严重程度判断：
   - **error级别**：仅限真正的错误，如语法错误、拼写错误、不准确的术语、可能引起误解的表达
   - **warning级别**：轻微的表达优化建议，如更友好的措辞、更清晰的表达
   - **不报告**：以下情况不应报告为问题：
     * 基本的、正确的英文表达
     * 细微的友好度差异
     * 个人偏好的表达方式差异
     * 已经符合英语母语者习惯的标准表达
     * 常见的导航文案（如 "Back to", "Return to", "Go back to"）
     * 标准的操作文案（如 "View", "Check", "See"）
     * 标准的状态文案（如 "canceled", "cancelled", "active", "inactive"）

4. 限制条件：
   - 最多返回${maxIssues}个问题: 避免信息过载
   - 优先返回error类型问题: 确保重要问题不被遗漏
   - 不添加其他文字: 严格遵循规定的输出格式
   - 确保文化适应性: 遵循英语母语用户的语言习惯
   - 注意字数限制: 优化输出以节省token
   - **重要**: 检查的是英文文案质量，不是翻译问题
   - **重要**: 只报告真正需要修复的问题，避免过度挑剔

## Workflows

- 目标: 确保检查的英文UI文案符合英语母语用户的标准，提升用户体验
- 步骤 1: 对文件进行分析，识别英文UI文案中的相关问题
- 步骤 2: 根据规则生成问题列表
- 步骤 3: 格式化输出结果为JSON结构
- 预期结果: 输出符合要求的JSON格式检查结果

## OutputFormat

**重要：你必须严格按照以下JSON格式返回结果，不要添加任何其他文字：**

{
  "issues": [
    {
      "type": "error|warning",
      "category": "语法|语义|变量|用户体验|产品一致性",
      "message": "具体问题描述（用中文，但针对英文文案）",
      "suggestion": "改进建议（用中文，但建议如何改进英文文案）",
      "line": 行号,
      "text": "有问题的英文文案"
    }
  ],
  "overall": "pass|warning|error",
  "summary": "总体评价（用中文，但评价英文文案质量）"
}

## 检查文件

文件路径: ${relativePath}
文件内容:
\`\`\`typescript
${fileContent}
\`\`\`

请按照上述规则和格式对文件中的英文UI文案进行检查。记住：系统用户是英语母语者，你是在帮助开发团队改进英文文案质量，而不是翻译。

**重要提醒**：只报告真正需要修复的问题。对于标准的、正确的英文表达，即使可以稍微优化，也不要报告为问题。重点关注语法错误、拼写错误、不准确的术语和可能引起误解的表达。`;

  // 尝试不同的模型
  for (let i = 0; i < availableModels.length; i++) {
    const model = availableModels[i];
    try {
      console.log(`尝试使用模型: ${model.instanceName || model.provider}/${model.name}`);

      let responseText;

      if (model.provider === 'gemini') {
        try {
          const geminiModel = model.instance.getGenerativeModel({ model: model.name });
          const result = await geminiModel.generateContent(prompt);
          const response = await result.response;
          responseText = response.text();
          console.log(
            `🔍 ${model.instanceName || model.provider}/${model.name} API调用成功，响应长度: ${responseText ? responseText.length : 0}`,
          );
        } catch (geminiError) {
          console.error(
            `❌ ${model.instanceName || model.provider}/${model.name} Gemini API调用失败:`,
            geminiError.message,
          );
          throw geminiError;
        }
      } else if (model.provider === 'openai') {
        try {
          // 使用实例自己的客户端（可能是公司Gemini通过litellm proxy）
          const completion = await model.instance.chat.completions.create({
            model: model.name,
            messages: [
              {
                role: 'user',
                content: prompt,
              },
            ],
            temperature: 0.1,
            max_tokens: 10000,
          });
          responseText = completion.choices[0].message.content;
          console.log(
            `🔍 ${model.instanceName || model.provider}/${model.name} OpenAI API调用成功，响应长度: ${responseText ? responseText.length : 0}`,
          );
        } catch (openaiError) {
          console.error(
            `❌ ${model.instanceName || model.provider}/${model.name} OpenAI API调用失败:`,
            openaiError.message,
          );
          throw openaiError;
        }
      }

      try {
        // 检查响应是否为空
        if (!responseText || responseText.trim() === '') {
          console.error(`❌ ${model.instanceName || model.provider}/${model.name} 返回空响应`);
          continue;
        }

        let cleanResponse = responseText.trim();
        console.log(`🔍 ${model.instanceName || model.provider}/${model.name} 原始响应长度: ${cleanResponse.length}`);
        console.log(
          `🔍 ${model.instanceName || model.provider}/${model.name} 响应前100字符: ${cleanResponse.substring(0, 100)}`,
        );

        // 如果响应被 ```json 包围，提取其中的JSON
        if (cleanResponse.startsWith('```json')) {
          cleanResponse = cleanResponse.replace(/^```json\s*/, '').replace(/\s*```$/, '');
        } else if (cleanResponse.startsWith('```')) {
          cleanResponse = cleanResponse.replace(/^```\s*/, '').replace(/\s*```$/, '');
        }

        // 再次检查清理后的响应
        if (!cleanResponse || cleanResponse.trim() === '') {
          console.error(`❌ ${model.instanceName || model.provider}/${model.name} 清理后响应为空`);
          continue;
        }

        console.log(`🔍 ${model.instanceName || model.provider}/${model.name} 清理后响应长度: ${cleanResponse.length}`);
        console.log(
          `🔍 ${model.instanceName || model.provider}/${model.name} 清理后响应前100字符: ${cleanResponse.substring(0, 100)}`,
        );

        const analysis = JSON.parse(cleanResponse);
        console.log(`✅ 成功使用模型: ${model.instanceName || model.provider}/${model.name}`);
        return {
          issues: analysis.issues || [],
          overall: analysis.overall || 'pass',
          summary: analysis.summary || '检查完成',
          modelUsed: `${model.instanceName || model.provider}/${model.name}`,
        };
      } catch (parseError) {
        console.error(`❌ 解析${model.instanceName || model.provider}/${model.name}响应失败:`, parseError.message);
        console.error(`❌ 响应类型: ${typeof responseText}`);
        console.error(`❌ 响应长度: ${responseText ? responseText.length : 0}`);
        console.error(`❌ 原始响应:`, responseText);
        console.error(`❌ 清理后响应:`, cleanResponse);

        // 尝试生成一个默认的响应，而不是直接失败
        console.log(`⚠️ ${model.instanceName || model.provider}/${model.name} JSON解析失败，生成默认响应`);
        return {
          issues: [
            {
              type: 'warning',
              category: '系统',
              message: `AI模型响应格式异常，无法解析JSON`,
              suggestion: '请检查AI模型配置或稍后重试',
            },
          ],
          overall: 'warning',
          summary: 'AI模型响应格式异常',
          modelUsed: `${model.instanceName || model.provider}/${model.name}`,
        };
      }
    } catch (error) {
      console.error(`${model.instanceName || model.provider}/${model.name} API调用失败:`, error.message);

      // 检查是否是配额限制错误
      const isQuotaError =
        error.message.includes('quota') ||
        error.message.includes('limit') ||
        error.message.includes('rate') ||
        error.message.includes('429') ||
        error.message.includes('insufficient_quota');

      if (isQuotaError) {
        console.log(`⚠️ ${model.instanceName || model.provider}/${model.name} 达到配额限制，尝试下一个模型...`);
        continue;
      } else {
        // 如果不是配额问题，可能是其他错误，也继续尝试下一个模型
        console.log(`⚠️ ${model.instanceName || model.provider}/${model.name} 调用失败，尝试下一个模型...`);
        continue;
      }
    }
  }

  // 所有模型都失败了
  console.error('❌ 所有AI模型都调用失败');
  return {
    issues: [
      {
        type: 'error',
        category: '系统',
        message: '所有AI模型都调用失败，可能是API配额已用完或网络问题',
        suggestion: '请稍后重试或检查API配置',
      },
    ],
    overall: 'error',
    summary: '所有AI模型调用异常',
    modelUsed: 'none',
  };
}

// 批量分析文件
async function batchAnalyzeFiles(filesWithContent) {
  const batchSize = 2;
  const results = [];
  const totalFiles = filesWithContent.length;

  for (let i = 0; i < filesWithContent.length; i += batchSize) {
    const batch = filesWithContent.slice(i, i + batchSize);
    console.log(`正在分析第 ${i + 1}-${Math.min(i + batchSize, filesWithContent.length)} 个文件...`);

    const batchPromises = batch.map(async ({ filePath, content }) => {
      const result = await analyzeFileWithAI(filePath, content, totalFiles);
      return { filePath, ...result };
    });

    const batchResults = await Promise.all(batchPromises);
    results.push(...batchResults);

    // 添加延迟避免API限制
    if (i + batchSize < filesWithContent.length) {
      await new Promise((resolve) => setTimeout(resolve, 3000));
    }
  }

  return results;
}

function getRelativePath(absolutePath) {
  const workspace = process.env.GITHUB_WORKSPACE || process.cwd();
  if (absolutePath.startsWith(workspace)) {
    return absolutePath.substring(workspace.length + 1); // +1 去掉开头的斜杠
  }
  return absolutePath;
}

function readFileContent(filePath) {
  try {
    console.log('正在读取文件:', filePath);
    const content = execSync(`cat "${filePath}"`, { encoding: 'utf8' });
    return content;
  } catch (error) {
    console.error(`读取文件失败 ${filePath}:`, error);
    return null;
  }
}

async function analyzeFiles() {
  const changedFiles = getChangedFiles();

  if (changedFiles.length === 0) {
    console.log('没有需要检查的 TSX 文件');
    return;
  }

  console.log(`发现 ${changedFiles.length} 个修改的TSX文件`);

  const filesToAnalyze = [];

  for (const filePath of changedFiles) {
    const content = readFileContent(filePath);
    if (content) {
      filesToAnalyze.push({
        filePath,
        content,
      });
      console.log(`文件 ${filePath} 已读取，大小: ${content.length} 字符`);
    }
  }

  console.log(`共发现 ${filesToAnalyze.length} 个文件需要分析`);

  if (filesToAnalyze.length === 0) {
    return;
  }

  const batchResults = await batchAnalyzeFiles(filesToAnalyze);

  const fileResultsMap = new Map();

  for (const result of batchResults) {
    const { filePath, issues, overall, summary } = result;
    const relativePath = getRelativePath(filePath);

    if (!fileResultsMap.has(relativePath)) {
      fileResultsMap.set(relativePath, {
        path: relativePath,
        issues: [],
        lineCount: 0,
        modelUsed: result.modelUsed || 'unknown',
      });
    }

    const fileResult = fileResultsMap.get(relativePath);

    if (issues && issues.length > 0) {
      for (const issue of issues) {
        fileResult.issues.push({
          line: issue.line || 0,
          text: issue.text || '未知文案',
          attribute: 'text',
          issues: [issue],
          overall: overall,
          summary: summary,
        });
      }
    }
  }

  analysisResults.files = Array.from(fileResultsMap.values());
  analysisResults.summary.filesWithIssues = analysisResults.files.filter((f) => f.issues.length > 0).length;
  analysisResults.summary.totalIssues = analysisResults.files.reduce((sum, f) => sum + f.issues.length, 0);
  analysisResults.summary.totalFiles = fileResultsMap.size;
}

function generateMarkdownReport(results) {
  let md = `## 🤖 AI文案检查报告\n\n`;
  md += `**检查时间：** ${new Date().toLocaleString('zh-CN')}\n\n`;
  md += `**共检查文件数：** ${results.summary.totalFiles}\n\n`;
  md += `**发现问题数：** ${results.summary.totalIssues}\n\n`;

  // 显示使用的模型信息
  const usedModels = new Set();
  for (const file of results.files) {
    if (file.modelUsed && file.modelUsed !== 'none') {
      usedModels.add(file.modelUsed);
    }
  }

  if (usedModels.size > 0) {
    md += `**使用模型：** ${Array.from(usedModels).join(', ')}\n\n`;
  }

  if (results.files.length === 0) {
    md += `🎉 **未发现文案问题！**\n\n`;
    md += `所有文案都通过了AI检查，继续保持！\n`;
    return md;
  }

  const criticalIssues = [];
  const warningIssues = [];

  for (const file of results.files) {
    for (const issue of file.issues) {
      for (const i of issue.issues) {
        const issueItem = {
          file: file.path,
          line: issue.line,
          text: issue.text,
          attribute: issue.attribute,
          issue: i,
        };

        if (i.type === 'error') {
          criticalIssues.push(issueItem);
        } else if (i.type === 'warning') {
          warningIssues.push(issueItem);
        }
      }
    }
  }

  if (criticalIssues.length > 0) {
    md += `### ❌ 需要修复 (${criticalIssues.length})\n\n`;
    md += `<details open>\n<summary>点击展开查看详情</summary>\n\n`;
    for (const item of criticalIssues) {
      md += `**文件：** \`${item.file}:${item.line}\`\n`;
      md += `**文案：** \`${item.text}\`\n`;
      md += `**问题：** ${item.issue.message}\n`;
      if (item.issue.suggestion) {
        md += `**建议：** ${item.issue.suggestion}\n`;
      }
      md += `\n`;
    }
    md += `</details>\n\n`;
  }

  if (warningIssues.length > 0) {
    md += `### ⚠️ 建议优化 (${warningIssues.length})\n\n`;
    md += `<details>\n<summary>点击展开查看详情</summary>\n\n`;
    for (const item of warningIssues) {
      md += `**文件：** \`${item.file}:${item.line}\`\n`;
      md += `**文案：** \`${item.text}\`\n`;
      md += `**问题：** ${item.issue.message}\n`;
      if (item.issue.suggestion) {
        md += `**建议：** ${item.issue.suggestion}\n`;
      }
      md += `\n`;
    }
    md += `</details>\n\n`;
  }

  md += `---\n\n`;
  md += `*此报告由 AI 自动生成，专注于提升用户体验。*\n`;

  return md;
}

// 检查环境变量
if (geminiInstances.length === 0) {
  console.error('错误: 未设置任何可用的 API 密钥');
  console.error('请至少配置以下环境变量之一:');
  console.error('- GEMINI_API_KEY (个人Gemini)');
  console.error('- MOEGO_AI_API_KEY (公司Gemini)');
  process.exit(1);
}

if (geminiInstances.length === 0) {
  console.warn('警告: 未设置任何 Gemini API 密钥，将跳过 Gemini 模型');
}

// 显示可用的API配置
console.log('可用的API配置:');
if (process.env.GEMINI_API_KEY) {
  console.log('✅ 个人Gemini API已配置');
}
if (process.env.MOEGO_AI_API_KEY) {
  console.log('✅ 公司Gemini API已配置');
}

console.log('开始AI文案检查...');

try {
  await analyzeFiles();

  const report = generateMarkdownReport(analysisResults);
  const reportPath = 'text-analysis-report.md';
  writeFileSync(reportPath, report, 'utf8');

  console.log('检查完成！');
  console.log(`发现 ${analysisResults.summary.totalIssues} 个问题`);

  // 输出给GitHub Actions (新语法)
  console.log(`has-issues=${analysisResults.summary.totalIssues > 0 ? 'true' : 'false'}`);
  console.log(`report-path=${reportPath}`);

  // 写入GitHub Actions输出文件
  const outputFile = process.env.GITHUB_OUTPUT;
  if (outputFile) {
    writeFileSync(
      outputFile,
      `has-issues=${analysisResults.summary.totalIssues > 0 ? 'true' : 'false'}\nreport-path=${reportPath}\n`,
      { flag: 'a' },
    );
  }
} catch (error) {
  console.error('检查过程发生错误:', error);
  process.exit(1);
}
