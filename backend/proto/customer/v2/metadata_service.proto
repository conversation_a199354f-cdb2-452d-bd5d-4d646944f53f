// (-- api-linter: core::0131::response-message-name=disabled
//     aip.dev/not-precedent: 使用自定义响应消息 --)
// (-- api-linter: core::0133::request-required-fields=disabled
//     aip.dev/not-precedent: 使用自定义请求消息 --)
// (-- api-linter: core::0133::request-resource-field=disabled
//     aip.dev/not-precedent: 使用自定义请求消息 --)
// (-- api-linter: core::0133::response-message-name=disabled
//     aip.dev/not-precedent: 使用自定义响应消息 --)
// (-- api-linter: core::0134::response-message-name=disabled
//     aip.dev/not-precedent: 使用自定义响应消息 --)
// (-- api-linter: core::0135::response-message-name=disabled
//     aip.dev/not-precedent: 使用空resp易于拓展 --);
// (-- api-linter: core::0216::state-field-output-only=disabled
//     aip.dev/not-precedent: 使用自定义状态字段 --)
syntax = "proto3";

package backend.proto.customer.v2;

import "google/api/field_behavior.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "backend/proto/customer/v2/metadata.proto";
import "backend/proto/customer/v2/common.proto";
import "buf/validate/validate.proto";
import "google/protobuf/struct.proto";
import "google/type/phone_number.proto";
import "google/type/postal_address.proto";
import "google/type/latlng.proto";

option go_package = "github.com/MoeGolibrary/moego/backend/proto/customer/v2;customerpb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.customer.v2";

// MetadataService 提供客户元数据管理功能
service MetadataService {
  // ==================== Customer Management ====================
  // CreateCustomer 
  rpc CreateCustomer(CreateCustomerRequest) returns (CreateCustomerResponse);

  // GetCustomer
  // get customer by unique identification
  // if customer not found, return error
  rpc GetCustomer(GetCustomerRequest) returns (GetCustomerResponse);
  
  // ListCustomers 
  // list customers by filter
  // if customer not found, return empty list, not return error
  rpc ListCustomers(ListCustomersRequest) returns (ListCustomersResponse);
  
  // UpdateCustomer 更新客户
  rpc UpdateCustomer(UpdateCustomerRequest) returns (UpdateCustomerResponse);
  
  // DeleteCustomer 删除客户
  rpc DeleteCustomer(DeleteCustomerRequest) returns (DeleteCustomerResponse);

  // 聚合创建customer及相关实体
  // 原子性创建customer以及可选的address、contact、related data
  rpc CreateCustomerAggregate(CreateCustomerAggregateRequest) returns (CreateCustomerAggregateResponse);
  
  // ==================== Contact Management ====================
  // CreateContact 创建联系人
  rpc CreateContact(CreateContactRequest) returns (CreateContactResponse);
  
  // GetContact 获取联系人
  rpc GetContact(GetContactRequest) returns (GetContactResponse);
  
  // ListContacts 列出联系人
  rpc ListContacts(ListContactsRequest) returns (ListContactsResponse);
  
  // UpdateContact 更新联系人
  rpc UpdateContact(UpdateContactRequest) returns (UpdateContactResponse);
  
  // DeleteContact 删除联系人
  rpc DeleteContact(DeleteContactRequest) returns (DeleteContactResponse);

  // ==================== CustomerRelatedData Management ====================
  // CreateCustomerRelatedData 创建客户相关数据
  rpc CreateCustomerRelatedData(CreateCustomerRelatedDataRequest) returns (CreateCustomerRelatedDataResponse);
  
  // GetCustomerRelatedData 获取客户相关数据
  rpc GetCustomerRelatedData(GetCustomerRelatedDataRequest) returns (GetCustomerRelatedDataResponse);
  
  // ListCustomerRelatedData 列出客户相关数据
  rpc ListCustomerRelatedData(ListCustomerRelatedDataRequest) returns (ListCustomerRelatedDataResponse);
  
  // UpdateCustomerRelatedData 更新客户相关数据
  rpc UpdateCustomerRelatedData(UpdateCustomerRelatedDataRequest) returns (UpdateCustomerRelatedDataResponse);
  
  // DeleteCustomerRelatedData 删除客户相关数据
  rpc DeleteCustomerRelatedData(DeleteCustomerRelatedDataRequest) returns (DeleteCustomerRelatedDataResponse);

  // ==================== Contact Tag Management ====================
  // CreateContactTag 创建标签
  rpc CreateContactTag(CreateContactTagRequest) returns (CreateContactTagResponse);
  
  // GetContactTag 获取标签
  rpc GetContactTag(GetContactTagRequest) returns (GetContactTagResponse);

  // ListContactTags 列出标签
  rpc ListContactTags(ListContactTagsRequest) returns (ListContactTagsResponse);
  
  // UpdateContactTag 更新标签
  rpc UpdateContactTag(UpdateContactTagRequest) returns (UpdateContactTagResponse);
  
  // DeleteContactTag 删除标签
  rpc DeleteContactTag(DeleteContactTagRequest) returns (DeleteContactTagResponse);
  
  // ==================== Lead Management ====================
  // CreateLead 创建线索
  rpc CreateLead(CreateLeadRequest) returns (CreateLeadResponse);

  // GetLead 获取线索
  rpc GetLead(GetLeadRequest) returns (GetLeadResponse);

  // ListLeads 列出线索
  rpc ListLeads(ListLeadsRequest) returns (ListLeadsResponse);

  // UpdateLead 更新线索
  rpc UpdateLead(UpdateLeadRequest) returns (UpdateLeadResponse);

  // DeleteLead 删除线索
  rpc DeleteLead(DeleteLeadRequest) returns (DeleteLeadResponse);

  // ==================== Address Management ====================
  // CreateAddress 创建地址
  rpc CreateAddress(CreateAddressRequest) returns (CreateAddressResponse);

  // GetAddress 获取地址
  rpc GetAddress(GetAddressRequest) returns (GetAddressResponse);

  // ListAddresses 列出地址
  rpc ListAddresses(ListAddressesRequest) returns (ListAddressesResponse);

  // UpdateAddress 更新地址
  rpc UpdateAddress(UpdateAddressRequest) returns (UpdateAddressResponse);

  // DeleteAddress 删除地址
  rpc DeleteAddress(DeleteAddressRequest) returns (DeleteAddressResponse);

  // ==================== Custom Field Management ====================
  // CreateCustomField 创建自定义字段
  rpc CreateCustomField(CreateCustomFieldRequest) returns (CreateCustomFieldResponse);
  
  // GetCustomField 获取自定义字段
  rpc GetCustomField(GetCustomFieldRequest) returns (GetCustomFieldResponse);
  
  // ListCustomFields 列出自定义字段
  rpc ListCustomFields(ListCustomFieldsRequest) returns (ListCustomFieldsResponse);
  
  // UpdateCustomField 更新自定义字段
  rpc UpdateCustomField(UpdateCustomFieldRequest) returns (UpdateCustomFieldResponse);
  
  // BatchUpdateCustomFields 批量更新自定义字段
  rpc BatchUpdateCustomFields(BatchUpdateCustomFieldsRequest) returns (BatchUpdateCustomFieldsResponse);
  
  // DeleteCustomField 删除自定义字段
  rpc DeleteCustomField(DeleteCustomFieldRequest) returns (DeleteCustomFieldResponse);

  // ==================== Unified Metadata Management ====================
  // CreateMetadata 创建统一元数据 (customer 或 lead)
  rpc CreateMetadata(CreateMetadataRequest) returns (CreateMetadataResponse);
  
  // GetMetadata 获取统一元数据
  rpc GetMetadata(GetMetadataRequest) returns (GetMetadataResponse);
  
  // ListMetadata 列出统一元数据
  rpc ListMetadata(ListMetadataRequest) returns (ListMetadataResponse);
  
  // UpdateMetadata 更新统一元数据
  rpc UpdateMetadata(UpdateMetadataRequest) returns (UpdateMetadataResponse);
  
  // DeleteMetadata 删除统一元数据
  rpc DeleteMetadata(DeleteMetadataRequest) returns (DeleteMetadataResponse);

  // 聚合创建metadata及相关实体
  // 原子性创建metadata以及可选的address、contact、related data
  rpc CreateMetadataAggregate(CreateMetadataAggregateRequest) returns (CreateMetadataAggregateResponse);
}

// ==================== Customer Request/Response Messages ====================
// CreateCustomerRequest 创建客户请求
message CreateCustomerRequest {
  // 组织引用
  OrganizationRef organization = 1 [(google.api.field_behavior) = REQUIRED];
  // 客户名称
  string given_name = 2;
  // 客户姓氏
  string family_name = 3;
  // 客户自定义字段
  optional google.protobuf.Struct custom_fields = 4;
  // 客户生命周期ID
  optional int64 lifecycle_id = 5;
  // 负责人员工ID
  optional int64 owner_staff_id = 6;
  // 行动状态ID
  optional int64 action_state_id = 7;
  // 头像路径
  optional string avatar_path = 8 [(buf.validate.field).string.max_len = 255];
  // 推荐来源ID
  optional int64 referral_source_id = 9 [(buf.validate.field).int64.gt = 0];
}

// CreateCustomerResponse 创建客户响应
message CreateCustomerResponse {
  // 客户
  Customer customer = 1;
}

// GetCustomerRequest 获取客户请求
message GetCustomerRequest {
  // 客户ID
  int64 id = 1 [(buf.validate.field).int64.gt = 0];
}

// GetCustomerResponse 获取客户响应
message GetCustomerResponse {
  // 客户
  Customer customer = 1;
}

// ListCustomersRequest 列出客户请求
message ListCustomersRequest {
  // filter
  message Filter {
    // customer id list
    repeated int64 ids = 1;
    // 组织引用
    OrganizationRef organization = 2 [(buf.validate.field).required = true];
    // (-- api-linter: core::0216::state-field-output-only=disabled
    //     aip.dev/not-precedent: this field is used for filtering, not for output --)
    // 状态
    // 如果states为空, 则返回所有状态的客户, 没有默认值
    repeated Customer.State states = 3;
    // 生命周期ID
    repeated int64 lifecycle_ids = 4;
    // 负责人员工ID
    repeated int64 owner_staff_ids = 5;
    // 推荐来源ID
    repeated int64 referral_source_ids = 6;
  }
  // sorting
  message Sorting {
    // sorting field
    enum Field {
      // 默认排序
      FIELD_UNSPECIFIED = 0;
      // 客户ID
      ID = 1;
      // 创建时间
      CREATED_TIME = 2;
      // 更新时间
      UPDATED_TIME = 3;
      // 客户名称
      GIVEN_NAME = 4;
      // 客户姓氏
      FAMILY_NAME = 5;
    }
    // sorting direction
    enum Direction {
      // 默认排序
      DIRECTION_UNSPECIFIED = 0;
      // 升序
      ASC = 1;
      // 降序
      DESC = 2;
    }
    // 排序字段
    Field field = 1;
    // 排序方向
    Direction direction = 2;
  }
  // 过滤条件
  Filter filter = 1 [(buf.validate.field).required = true];
  // 排序
  Sorting sorting = 2;
  // 分页
  // 分页大小
  int32 page_size = 3 [(buf.validate.field).int32.gte = 1, (buf.validate.field).int32.lte = 1000];
  // 分页令牌
  string page_token = 4;
   // 是否返回总数量, 默认不返回, 会影响性能
   bool return_total_size = 5;
}

// ListCustomersResponse 列出客户响应
message ListCustomersResponse {
  // 客户列表
  repeated Customer customers = 1;
  // 下一页令牌
  string next_page_token = 2;
  // 总数量, 需要设置 return_total_size 为 true 才会返回
  optional int64 total_size = 3;
}

// UpdateCustomerRequest 更新客户请求
message UpdateCustomerRequest {
  // id
  int64 id = 1 [(buf.validate.field).int64.gt = 0];
  // 客户名称
  optional string given_name = 2;
  // 客户姓氏
  optional string family_name = 3;
  // 客户自定义字段
  optional google.protobuf.Struct custom_fields = 4;
  // 客户生命周期ID
  optional int64 lifecycle_id = 5;
  // 负责人员工ID
  optional int64 owner_staff_id = 6;
  // 行动状态ID
  optional int64 action_state_id = 7;
  // 头像路径
  optional string avatar_path = 8 [(buf.validate.field).string.max_len = 255];
  // 推荐来源ID
  optional int64 referral_source_id = 9 [(buf.validate.field).int64.gt = 0];
  // state
  // (-- api-linter: core::0216::state-field-output-only=disabled
  //     aip.dev/not-precedent: this field is used for filtering, not for output --)
  optional Customer.State state = 10;
}

// UpdateCustomerResponse 更新客户响应
message UpdateCustomerResponse {
  // 客户
  Customer customer = 1;
}

// DeleteCustomerRequest 删除客户请求
message DeleteCustomerRequest {
  // 客户ID
  int64 id = 1 [(buf.validate.field).int64.gt = 0];
}

// DeleteCustomerResponse 删除客户响应
message DeleteCustomerResponse {}

// CreateCustomerAggregateRequest 聚合创建客户及相关实体请求
message CreateCustomerAggregateRequest {
  // 组织引用
  OrganizationRef organization = 1 [(google.api.field_behavior) = REQUIRED];
  // 客户信息
  Customer customer = 2 [(google.api.field_behavior) = REQUIRED];
  // 联系人列表
  repeated Contact contacts = 3;
  // 地址列表
  repeated Address addresses = 4;
  // 客户相关数据
  optional CustomerRelatedData customer_related_data = 5;
}

// CreateCustomerAggregateResponse 聚合创建客户及相关实体响应
message CreateCustomerAggregateResponse {
  // 客户聚合
  CustomerAggregate customer_aggregate = 1;
}

// ==================== Contact Request/Response Messages ====================
// CreateContactRequest 创建联系人请求
message CreateContactRequest {
  // 所属客户ID
  int64 customer_id = 1 [(buf.validate.field).int64.gt = 0];
  // 名字
  string given_name = 2;
  // 姓氏
  string family_name = 3;
  // 邮箱
  optional string email = 4;
  // 手机号
  optional google.type.PhoneNumber phone = 5;
  // 是否是自己创建的
  bool is_self = 6;
  // 标签列表
  repeated ContactTag tags = 7;
  // 备注
  optional string note = 8;
}

// CreateContactResponse 创建联系人响应
message CreateContactResponse {
  // 联系人
  Contact contact = 1;
}

// GetContactRequest 获取联系人请求
message GetContactRequest {
  // 联系人ID
  int64 id = 1 [(buf.validate.field).int64.gt = 0];
}

// GetContactResponse 获取联系人响应
message GetContactResponse {
  // 联系人
  Contact contact = 1;
}

// ListContactsRequest 列出联系人请求
message ListContactsRequest {
  // filter
  message Filter {
    // 联系人ID列表
    repeated int64 ids = 1;
    // 所属客户ID列表
    repeated int64 customer_ids = 2;
    // (-- api-linter: core::0216::state-field-output-only=disabled
    //     aip.dev/not-precedent: this field is used for filtering, not for output --)
    // 状态
    // 如果states为空, 则返回所有状态的联系人, 没有默认值
    repeated Contact.State states = 3;
    // 电话号码批量搜索
    repeated google.type.PhoneNumber phones = 4;
    // Emails
    repeated string emails = 5;
    // organization, 非ids/customer_ids查询建议都传，防止扫全表
    optional OrganizationRef organization = 6;
  }
  // sorting
  message Sorting {
    // 排序字段
    enum Field {
      // 默认排序
      FIELD_UNSPECIFIED = 0;
      // 联系人ID
      ID = 1;
      // 创建时间
      CREATED_TIME = 2;
      // 更新时间
      UPDATED_TIME = 3;
    }
    // 排序方向
    enum Direction {
      // 默认排序
      DIRECTION_UNSPECIFIED = 0;
      // 升序
      ASC = 1;
      // 降序
      DESC = 2;
    }
    // 排序字段
    Field field = 1;
    // 排序方向
    Direction direction = 2;
  }
  // 过滤条件
  Filter filter = 1 [(buf.validate.field).required = true];
  // 排序
  Sorting sorting = 2;
  // 分页大小
  int32 page_size = 3 [(buf.validate.field).int32.gte = 1, (buf.validate.field).int32.lte = 1000];
  // 分页令牌
  string page_token = 4;
  // 是否返回总数量, 默认不返回, 会影响性能
  bool return_total_size = 5;
}

// ListContactsResponse 列出联系人响应
message ListContactsResponse {
  // 联系人列表
  repeated Contact contacts = 1;
  // 下一页令牌
  string next_page_token = 2;
  // 总数量, 需要设置 return_total_size 为 true 才会返回
  optional int64 total_size = 3;
}

// UpdateContactRequest 更新联系人请求
message UpdateContactRequest {
  // id
  int64 id = 1 [(buf.validate.field).int64.gt = 0];
  // 名字
  optional string given_name = 2;
  // 姓氏
  optional string family_name = 3;
  // 邮箱
  optional string email = 4;
  // 手机号
  optional google.type.PhoneNumber phone = 5;
  // 标签列表
  repeated int64 tag_ids = 6;
  // 备注
  optional string note = 7;
  // state
  // (-- api-linter: core::0216::state-field-output-only=disabled
  //     aip.dev/not-precedent: this field is used for updating, not for output --)
  optional Contact.State state = 8;
}

// UpdateContactResponse 更新联系人响应
message UpdateContactResponse {
  // 联系人
  Contact contact = 1;
}

// DeleteContactRequest 删除联系人请求
message DeleteContactRequest {
  // 联系人ID
  int64 id = 1 [(buf.validate.field).int64.gt = 0];
}

// DeleteContactResponse 删除联系人响应
message DeleteContactResponse {}

// ==================== CustomerRelatedData Request/Response Messages ====================
// CreateCustomerRelatedDataRequest 创建客户相关数据请求
message CreateCustomerRelatedDataRequest {
  // 关联的客户ID
  int64 customer_id = 1 [(buf.validate.field) = { int64: { gt: 0 } }];
  // 商家ID
  int64 preferred_business_id = 2;
  // 公司ID
  int64 company_id = 3;
  // 客户颜色
  string client_color = 4 [(buf.validate.field) = { string: { max_len: 50 } }];
  // 是否屏蔽消息 (0-正常 1-屏蔽)
  int32 is_block_message = 5;
  // 是否屏蔽在线预约 (0-正常 1-屏蔽)
  int32 is_block_online_booking = 6;
  // 登录邮箱
  string login_email = 7 [(buf.validate.field) = { string: { max_len: 50 } }];
  // 推荐来源ID
  int32 referral_source_id = 8;
  // 推荐来源描述
  string referral_source_desc = 9 [(buf.validate.field) = { string: { max_len: 255 } }];
  // 发送自动邮件 (0-不接受 1-接受)
  int32 send_auto_email = 10;
  // 发送自动短信 (0-不接受 1-接受)
  int32 send_auto_message = 11;
  // 发送app自动短信 (0-不接受 1-接受)
  int32 send_app_auto_message = 12;
  // 未确认提醒方式 (1-text 2-email 3-phone call)
  // (-- api-linter: core::0140::prepositions=disabled
  //     aip.dev/not-precedent: this is not a preposition --)
  repeated int32 unconfirmed_reminder_by = 13;
  // 首选美容师ID
  int32 preferred_groomer_id = 14;
  // 首选频率天数
  int32 preferred_frequency_day = 15;
  // 首选频率类型 (0-by days 1-by weeks)
  int32 preferred_frequency_type = 16;
  // 最后服务时间
  // (-- api-linter: core::0142::time-field-type=disabled
  //     aip.dev/not-precedent: this is json string not a time --)
  string last_service_time = 17 [(buf.validate.field) = { string: { max_len: 20 } }];
  // 来源
  string source = 18 [(buf.validate.field) = { string: { max_len: 20 } }];
  // 外部ID
  string external_id = 19 [(buf.validate.field) = { string: { max_len: 20 } }];
  // 创建人
  // (-- api-linter: core::0140::prepositions=disabled
  //     aip.dev/not-precedent: this is not a preposition --)
  int32 create_by = 20;
  // 更新人
  // (-- api-linter: core::0140::prepositions=disabled
  //     aip.dev/not-precedent: this is not a preposition --)
  int32 update_by = 21;
  // 是否重复 (1-true 2-false)
  optional int32 is_recurring = 22;
  // 分享预约状态 (0-all 1-unconfirm 2-confirm 4-finished)
  int32 share_appt_status = 23;
  // 分享范围类型 (0-all 1-in x days 2-next x appointment 3-manually apptids)
  int32 share_range_type = 24;
  // 分享范围值
  int32 share_range_value = 25;
  // 分享预约JSON
  optional string share_appt_json = 26;
  // 首选天 (JSON数组格式)
  string preferred_day = 27 [(buf.validate.field) = { string: { max_len: 50 } }];
  // 首选时间 (JSON数组格式)
  // (-- api-linter: core::0142::time-field-type=disabled
  //     aip.dev/not-precedent: this is json string not a time --)
  string preferred_time = 28 [(buf.validate.field) = { string: { max_len: 50 } }];
  // 账户ID
  int64 account_id = 29;
  // 客户编码
  string customer_code = 30 [(buf.validate.field) = { string: { max_len: 8 } }];
  // 是否退订
  bool is_unsubscribed = 31;
  // 生日
  // (-- api-linter: core::0142::time-field-names=disabled
  //     aip.dev/not-precedent: this is not a time field --)
  optional google.protobuf.Timestamp birthday = 32;
  // 行动状态
  string action_state = 33 [(buf.validate.field) = { string: { max_len: 255 } }];
  // 自定义生命周期ID
  int64 customize_life_cycle_id = 34;
  // 自定义行动状态ID
  int64 customize_action_state_id = 35; 
  // preferred tip configuration
  // 首选小费是否启用 (0-关闭 1-开启)
  int32 preferred_tip_enable = 36;
  // 首选小费类型 (0-按金额 1-按百分比)
  int32 preferred_tip_type = 37;
  // 按金额小费
  double preferred_tip_amount = 38;
  // 按百分比小费 [1,100]
  int32 preferred_tip_percentage = 39;
  
  // default preferred frequency configuration
  // 默认首选频率类型 (1-grooming)
  int32 default_preferred_frequency_type = 40;
  // 默认首选频率周期 (1-day, 2-week, 4-month)
  int32 default_preferred_calendar_period = 41;
  // 默认首选频率值
  int32 default_preferred_frequency_value = 42;
}

// CreateCustomerRelatedDataResponse 创建客户相关数据响应
message CreateCustomerRelatedDataResponse {
  // 客户相关数据
  CustomerRelatedData customer_related_data = 1;
}

// GetCustomerRelatedDataRequest 获取客户相关数据请求
message GetCustomerRelatedDataRequest {
  // 客户相关数据ID
  int64 customer_id = 1 [(buf.validate.field).int64.gt = 0];
}

// GetCustomerRelatedDataResponse 获取客户相关数据响应
message GetCustomerRelatedDataResponse {
  // 客户相关数据
  CustomerRelatedData customer_related_data = 1;
}

// ListCustomerRelatedDataRequest 列出客户相关数据请求
message ListCustomerRelatedDataRequest {
  // filter
  message Filter {
    // 客户相关数据ID列表
    repeated int64 ids = 1;
    // 客户ID列表
    repeated int64 customer_ids = 2;
    // 组织引用
    OrganizationRef organization = 3;
    // 商家ID列表
    repeated int32 business_ids = 4;
    // 公司ID列表 
    repeated int64 company_ids = 5;
    // 状态列表
    // (-- api-linter: core::0216::state-field-output-only=disabled
    //     aip.dev/not-precedent: this field is used for filtering, not for output --)
    repeated CustomerRelatedData.State states = 6;
    // CustomerCodes
    repeated string customer_codes = 7;
    // AccountIDs
    repeated int64 account_ids = 8;
  }
  // sorting
  message Sorting {
    // 排序字段
    enum Field {
      // 默认排序
      FIELD_UNSPECIFIED = 0;
      // 客户相关数据ID
      ID = 1;
      // 创建时间
      CREATED_TIME = 2;
      // 更新时间
      UPDATED_TIME = 3;
    }
    // 排序方向
    enum Direction {
      // 默认排序
      DIRECTION_UNSPECIFIED = 0;
      // 升序
      ASC = 1;
      // 降序
      DESC = 2;
    }
    // 排序字段
    Field field = 1;
    // 排序方向
    Direction direction = 2;
  }
  // 过滤条件
  Filter filter = 1 [(buf.validate.field).required = true];
  // 排序
  Sorting sorting = 2;
  // 分页大小
  int32 page_size = 3 [(buf.validate.field).int32.gte = 1, (buf.validate.field).int32.lte = 1000];
  // 分页令牌
  string page_token = 4;
  // 是否返回总数量, 默认不返回, 会影响性能
  bool return_total_size = 5;
}

// ListCustomerRelatedDataResponse 列出客户相关数据响应
message ListCustomerRelatedDataResponse {
  // 客户相关数据列表
  repeated CustomerRelatedData customer_related_data = 1;
  // 下一页令牌
  string next_page_token = 2;
  // 总数量, 需要设置 return_total_size 为 true 才会返回
  optional int64 total_size = 3;
}

// UpdateCustomerRelatedDataRequest 更新客户相关数据请求
message UpdateCustomerRelatedDataRequest {
  // customer id
  int64 id = 1 [(buf.validate.field).int64.gt = 0];
  // 商家ID
  optional int64 preferred_business_id = 2;
  // 客户颜色
  optional string client_color = 3 [(buf.validate.field) = { string: { max_len: 50 } }];
  // 是否屏蔽消息 (0-正常 1-屏蔽)
  optional int32 is_block_message = 4;
  // 是否屏蔽在线预约 (0-正常 1-屏蔽)
  optional int32 is_block_online_booking = 5;
  // 登录邮箱
  optional string login_email = 6 [(buf.validate.field) = { string: { max_len: 50 } }];
  // 推荐来源ID
  optional int32 referral_source_id = 7;
  // 推荐来源描述
  optional string referral_source_desc = 8 [(buf.validate.field) = { string: { max_len: 255 } }];
  // 发送自动邮件 (0-不接受 1-接受)
  optional int32 send_auto_email = 9;
  // 发送自动短信 (0-不接受 1-接受)
  optional int32 send_auto_message = 10;
  // 发送app自动短信 (0-不接受 1-接受)
  optional int32 send_app_auto_message = 11;
  // 未确认提醒方式 (1-text 2-email 3-phone call)
  // (-- api-linter: core::0140::prepositions=disabled
  //     aip.dev/not-precedent: this is not a preposition --)
  repeated int32 unconfirmed_reminder_by = 12;
  // 首选美容师ID
  optional int32 preferred_groomer_id = 13;
  // 首选频率天数
  optional int32 preferred_frequency_day = 14;
  // 首选频率类型 (0-by days 1-by weeks)
  optional int32 preferred_frequency_type = 15;
  // 最后服务时间
  // (-- api-linter: core::0142::time-field-type=disabled
  //     aip.dev/not-precedent: this is json string not a time --)
  optional string last_service_time = 16 [(buf.validate.field) = { string: { max_len: 20 } }];
  // 来源
  optional string source = 17 [(buf.validate.field) = { string: { max_len: 20 } }];
  // 外部ID
  optional string external_id = 18 [(buf.validate.field) = { string: { max_len: 20 } }];
  // 创建人
  // (-- api-linter: core::0140::prepositions=disabled
  //     aip.dev/not-precedent: this is not a preposition --)
  optional int32 create_by = 19;
  // 更新人
  // (-- api-linter: core::0140::prepositions=disabled
  //     aip.dev/not-precedent: this is not a preposition --)
  optional int32 update_by = 20;
  // 是否重复 (1-true 2-false)
  optional int32 is_recurring = 21;
  // 分享预约状态 (0-all 1-unconfirm 2-confirm 4-finished)
  optional int32 share_appt_status = 22;
  // 分享范围类型 (0-all 1-in x days 2-next x appointment 3-manually apptids)
  optional int32 share_range_type = 23;
  // 分享范围值
  optional int32 share_range_value = 24;
  // 分享预约JSON
  optional string share_appt_json = 25;
  // 首选天 (JSON数组格式)
  optional string preferred_day = 26 [(buf.validate.field) = { string: { max_len: 50 } }];
  // 首选时间 (JSON数组格式)
  // (-- api-linter: core::0142::time-field-type=disabled
  //     aip.dev/not-precedent: this is json string not a time --)
  optional string preferred_time = 27 [(buf.validate.field) = { string: { max_len: 50 } }];
  // 账户ID
  optional int64 account_id = 28;
  // 客户编码
  optional string customer_code = 29 [(buf.validate.field) = { string: { max_len: 8 } }];
  // 是否退订
  optional bool is_unsubscribed = 30;
  // 生日
  // (-- api-linter: core::0142::time-field-names=disabled
  //     aip.dev/not-precedent: this is not a time field --)
  optional google.protobuf.Timestamp birthday = 31;
  // 行动状态
  optional string action_state = 32 [(buf.validate.field) = { string: { max_len: 255 } }];
  // 自定义生命周期ID
  optional int64 customize_life_cycle_id = 33;
  // 自定义行动状态ID
  optional int64 customize_action_state_id = 34;

  // preferred tip configuration
  // 首选小费是否启用 (0-关闭 1-开启)
  optional int32 preferred_tip_enable = 35;
  // 首选小费类型 (0-按金额 1-按百分比)
  optional int32 preferred_tip_type = 36;
  // 按金额小费
  optional double preferred_tip_amount = 37;
  // 按百分比小费 [1,100]
  optional int32 preferred_tip_percentage = 38;
  
  // default preferred frequency configuration
  // 默认首选频率类型 (1-grooming)
  optional int32 default_preferred_frequency_type = 39;
  // 默认首选频率周期 (1-day, 2-week, 4-month)
  optional int32 default_preferred_calendar_period = 40;
  // 默认首选频率值
  optional int32 default_preferred_frequency_value = 41;
}

// UpdateCustomerRelatedDataResponse 更新客户相关数据响应
message UpdateCustomerRelatedDataResponse {
  // 客户相关数据
  CustomerRelatedData customer_related_data = 1;
}

// DeleteCustomerRelatedDataRequest 删除客户相关数据请求
message DeleteCustomerRelatedDataRequest {
  // customer id
  int64 id = 1 [(buf.validate.field).int64.gt = 0];
}

// DeleteCustomerRelatedDataResponse 删除客户相关数据响应
message DeleteCustomerRelatedDataResponse {}

// ==================== Contact Tag Request/Response Messages ====================
// CreateContactTagRequest 创建标签请求
message CreateContactTagRequest {
  // 组织引用
  OrganizationRef organization = 1 [(google.api.field_behavior) = REQUIRED];
  // 标签名称
  string name = 2 [(google.api.field_behavior) = REQUIRED];
  // 标签描述
  optional string description = 3;
  // 颜色
  string color = 4;
  // 排序顺序
  optional int32 sort_order = 5;
  // 状态
  ContactTag.State state = 6;
  // 标签类型
  optional ContactTag.Type type = 7;
}

// CreateContactTagResponse 创建标签响应
message CreateContactTagResponse {
  // 联系人标签
  ContactTag contact_tag = 1;
}

// GetContactTagRequest 获取标签请求
message GetContactTagRequest {
  // 标签ID
  int64 id = 1 [(buf.validate.field).int64.gt = 0];
}

// GetContactTagResponse 获取标签响应
message GetContactTagResponse {
  // 联系人标签
  ContactTag contact_tag = 1;
}

// ListContactTagsRequest 列出标签请求
message ListContactTagsRequest {
  // 过滤条件
  message Filter {
    // 标签ID列表
    repeated int64 ids = 1;
    // 组织引用 (必填, 如果为空会报错)
    OrganizationRef organization = 2 [(buf.validate.field).required = true];
    // name, is fuzzy search
    optional string name = 3;
    // states
    repeated ContactTag.State states = 4;
  }
  // sorting
  message Sorting {
    // 排序字段
    enum Field {
      // 默认排序
      FIELD_UNSPECIFIED = 0;
      // 标签ID
      ID = 1;
      // 创建时间
      CREATED_TIME = 2;
      // 更新时间
      UPDATED_TIME = 3;
    }
    // 排序方向
    enum Direction {
      // 默认排序
      DIRECTION_UNSPECIFIED = 0;
      // 升序
      ASC = 1;
      // 降序
      DESC = 2;
    }
    // 排序字段
    Field field = 1;
    // 排序方向
    Direction direction = 2;
  }
  // 过滤条件
  Filter filter = 1 [(buf.validate.field).required = true];
  // 排序
  Sorting sorting = 2;
  // 分页大小
  int32 page_size = 3 [(buf.validate.field).int32.gte = 1, (buf.validate.field).int32.lte = 1000];
  // 分页令牌
  string page_token = 4;
  // 是否返回总数量, 默认不返回, 会影响性能
  bool return_total_size = 5;
}

// ListContactTagsResponse 列出标签响应
message ListContactTagsResponse {
  // 标签列表
  repeated ContactTag contact_tags = 1;
  // 下一页令牌
  string next_page_token = 2;
  // 总数量, 需要设置 return_total_size 为 true 才会返回
  optional int64 total_size = 3;
}

// UpdateContactTagRequest 更新标签请求
message UpdateContactTagRequest {
  // id
  int64 id = 1 [(buf.validate.field).int64.gt = 0];
  // name
  optional string name = 2;
  // description
  optional string description = 3;
  // color
  optional string color = 4;
  // sort order
  optional int32 sort_order = 5;
  // state
  optional ContactTag.State state = 6;
}

// UpdateContactTagResponse 更新标签响应
message UpdateContactTagResponse {
  // 联系人标签
  ContactTag contact_tag = 1;
}

// DeleteContactTagRequest 删除标签请求
message DeleteContactTagRequest {
  // 标签ID
  int64 id = 1 [(buf.validate.field).int64.gt = 0];
}

// DeleteContactTagResponse 删除标签响应
message DeleteContactTagResponse {}

// ==================== Lead Request/Response Messages ====================
// CreateLeadRequest 创建线索请求
message CreateLeadRequest {
  // 组织引用
  OrganizationRef organization = 1 [(google.api.field_behavior) = REQUIRED];
  // 线索名称
  string given_name = 2;
  // 线索姓氏
  string family_name = 3;
  // 线索自定义字段
  optional google.protobuf.Struct custom_fields = 4;
  // 生命周期ID
  optional int64 lifecycle_id = 5;
  // 负责人员工ID
  optional int64 owner_staff_id = 6;
  // 行动状态ID
  optional int64 action_state_id = 7;
  // 头像路径
  optional string avatar_path = 8 [(buf.validate.field).string.max_len = 255];
  // 推荐来源ID
  optional int64 referral_source_id = 9;
}

// CreateLeadResponse 创建线索响应
message CreateLeadResponse {
  // 线索
  Lead lead = 1;
}

// GetLeadRequest 获取线索请求
message GetLeadRequest {
  // 线索ID
  int64 id = 1 [(buf.validate.field).int64.gt = 0];
}

// GetLeadResponse 获取线索响应
message GetLeadResponse {
  // 线索
  Lead lead = 1;
}

// ListLeadsRequest 列出线索请求
message ListLeadsRequest {
  // filter
  message Filter {
    // 线索ID列表
    repeated int64 ids = 1;
    // 组织引用
    OrganizationRef organization = 2;
    // (-- api-linter: core::0216::state-field-output-only=disabled
    //     aip.dev/not-precedent: this field is used for filtering, not for output --)
    // 状态
    // 如果states为空, 则返回所有状态的lead, 没有默认值
    repeated Lead.State states = 3;
    // 生命周期ID
    repeated int64 lifecycle_ids = 4;
    // 负责人员工ID
    repeated int64 owner_staff_ids = 5;
    // Converted Customer IDs
    repeated int64 converted_customer_ids = 6;
    // is already converted
    optional bool is_converted = 7;
  }
  // sorting
  message Sorting {
    // 排序字段
    enum Field {
      // 默认排序
      FIELD_UNSPECIFIED = 0;
      // 线索ID
      ID = 1;
      // 创建时间
      CREATED_TIME = 2;
      // 更新时间
      UPDATED_TIME = 3;
    }
    // 排序方向
    enum Direction {
      // 默认排序
      DIRECTION_UNSPECIFIED = 0;
      // 升序
      ASC = 1;
      // 降序
      DESC = 2;
    }
    // 排序字段
    Field field = 1;
    // 排序方向
    Direction direction = 2;
  }
  // 过滤条件
  Filter filter = 1 [(buf.validate.field).required = true];
  // 排序
  Sorting sorting = 2;
  // 分页大小
  int32 page_size = 3 [(buf.validate.field).int32.gte = 1, (buf.validate.field).int32.lte = 1000];
  // 分页令牌
  string page_token = 4;
  // 是否返回总数量, 默认不返回, 会影响性能
  bool return_total_size = 5;
}

// ListLeadsResponse 列出线索响应
message ListLeadsResponse {
  // 线索列表
  repeated Lead leads = 1;
  // 下一页令牌
  string next_page_token = 2;
  // 总数量, 需要设置 return_total_size 为 true 才会返回
  optional int64 total_size = 3;
}

// UpdateLeadRequest 更新线索请求
message UpdateLeadRequest {
  // id
  int64 id = 1 [(buf.validate.field).int64.gt = 0];
  // 线索名称
  optional string given_name = 2;
  // 线索姓氏
  optional string family_name = 3;
  // 线索自定义字段
  optional google.protobuf.Struct custom_fields = 4;
  // 生命周期ID
  optional int64 lifecycle_id = 5;
  // 负责人员工ID
  optional int64 owner_staff_id = 6;
  // (-- api-linter: core::0216::state-field-output-only=disabled
  //     aip.dev/not-precedent: this field is used for updating, not for output --)
  // 状态
  optional Lead.State state = 7;
  // 头像路径
  optional string avatar_path = 8 [(buf.validate.field).string.max_len = 255];
}

// UpdateLeadResponse 更新线索响应
message UpdateLeadResponse {
  // 线索
  Lead lead = 1;
}

// DeleteLeadRequest 删除线索请求
message DeleteLeadRequest {
  // 线索ID
  int64 id = 1 [(buf.validate.field).int64.gt = 0];
}

// DeleteLeadResponse 删除线索响应
message DeleteLeadResponse {}

// ==================== Address Request/Response Messages ====================
// CreateAddressRequest 创建地址请求
message CreateAddressRequest {
  // 所有者ID
  int64 customer_id = 1 [(buf.validate.field).int64.gt = 0];
  // 地址
  google.type.PostalAddress address = 2 [(google.api.field_behavior) = REQUIRED];
  // 经纬度
  google.type.LatLng latlng = 3;
  // 类型
  Address.Type type = 4;
  // 组织引用
  OrganizationRef organization = 5 [(google.api.field_behavior) = REQUIRED];
}

// CreateAddressResponse 创建地址响应
message CreateAddressResponse {
  // 地址
  Address address = 1;
}

// GetAddressRequest 获取地址请求
message GetAddressRequest {
  // 地址ID
  int64 id = 1 [(buf.validate.field).int64.gt = 0];
}

// GetAddressResponse 获取地址响应
message GetAddressResponse {
  // 地址
  Address address = 1;
}

// ListAddressesRequest 列出地址请求
message ListAddressesRequest {
  // filter
  message Filter {
    // 地址ID列表
    repeated int64 ids = 1;
    // 所有者ID
    repeated int64 customer_ids = 2;
    // (-- api-linter: core::0216::state-field-output-only=disabled
    //     aip.dev/not-precedent: this field is used for filtering, not for output --)
    // state 
    // 如果states为空, 则返回所有状态的地址, 没有默认值
    repeated Address.State states = 3;
    // 组织引用
    optional OrganizationRef organization = 4;
    // types
    repeated Address.Type types = 5;
  }
  // sorting
  message Sorting {
    // 排序字段
    enum Field {
      // 默认排序
      FIELD_UNSPECIFIED = 0;
      // 地址ID
      ID = 1;
      // 创建时间
      CREATED_TIME = 2;
      // 更新时间
      UPDATED_TIME = 3;
    }
    // 排序方向
    enum Direction {
      // 默认排序
      DIRECTION_UNSPECIFIED = 0;
      // 升序
      ASC = 1;
      // 降序
      DESC = 2;
    }
    // 排序字段
    Field field = 1;
    // 排序方向
    Direction direction = 2;
  }
  // 过滤条件
  Filter filter = 1 [(buf.validate.field).required = true];
  // 排序
  Sorting sorting = 2;
  // 分页大小
  int32 page_size = 3 [(buf.validate.field).int32.gte = 1, (buf.validate.field).int32.lte = 1000];
  // 分页令牌
  string page_token = 4;
  // 是否返回总数量, 默认不返回, 会影响性能
  bool return_total_size = 5;
}

// ListAddressesResponse 列出地址响应
message ListAddressesResponse {
  // 地址列表
  repeated Address addresses = 1;
  // 下一页令牌
  string next_page_token = 2;
  // 总数量, 需要设置 return_total_size 为 true 才会返回
  optional int64 total_size = 3;
}

// UpdateAddressRequest 更新地址请求
message UpdateAddressRequest {
  // id
  int64 id = 1 [(buf.validate.field).int64.gt = 0];
  // address 
  google.type.PostalAddress address = 2;
  // 经纬度
  google.type.LatLng latlng = 3;
  // type
  optional Address.Type type = 4;
  // state
  // (-- api-linter: core::0216::state-field-output-only=disabled
  //     aip.dev/not-precedent: this field is used for updating, not for output --)
  optional Address.State state = 5;
}

// UpdateAddressResponse 更新地址响应
message UpdateAddressResponse {
  // 地址
  Address address = 1;
}

// DeleteAddressRequest 删除地址请求
message DeleteAddressRequest {
  // 地址ID
  int64 id = 1 [(buf.validate.field).int64.gt = 0];
}

// DeleteAddressResponse 删除地址响应
message DeleteAddressResponse {}

// ==================== Custom Field Request/Response Messages ====================
// CreateCustomFieldRequest 创建自定义字段请求
message CreateCustomFieldRequest {
  // 组织引用
  OrganizationRef organization = 1 [(google.api.field_behavior) = REQUIRED];
  // 字段名称
  string label = 2 [(google.api.field_behavior) = REQUIRED];
  // 字段类型
  CustomField.Type type = 3 [(google.api.field_behavior) = REQUIRED];
  // 实体所有者
  CustomField.AssociationType association_type = 4 [(google.api.field_behavior) = REQUIRED];
  // 是否必填
  optional bool is_required = 5;
  // 默认值
  optional CustomField.Value default_value = 6;
  // 选项
  repeated CustomField.Option options = 7;
  // 显示顺序
  optional int32 display_order = 8;
  // 帮助文本
  optional string help_text = 9;
}

// CreateCustomFieldResponse 创建自定义字段响应
message CreateCustomFieldResponse {
  // 自定义字段
  CustomField custom_field = 1;
}

// GetCustomFieldRequest 获取自定义字段请求
message GetCustomFieldRequest {
  // 自定义字段ID
  int64 id = 1 [(buf.validate.field).int64.gt = 0];
}

// GetCustomFieldResponse 获取自定义字段响应
message GetCustomFieldResponse {
  // 自定义字段
  CustomField custom_field = 1;
}

// ListCustomFieldsRequest 列出自定义字段请求
message ListCustomFieldsRequest {
  // filter
  message Filter {
    // 自定义字段ID列表
    repeated int64 ids = 1;
    // organization 
    OrganizationRef organization = 2 [(buf.validate.field).required = true];
    // 实体所有者
    backend.proto.customer.v2.CustomField.AssociationType association_type = 3;
    // 字段类型
    repeated CustomField.Type types = 4;
    // 是否必填
    optional bool is_required = 5;
    // (-- api-linter: core::0216::state-field-output-only=disabled
    //     aip.dev/not-precedent: this field is used for filtering, not for output --)
    // 状态
    repeated CustomField.State states = 6;
  }
  // sorting
  message Sorting {
    // 排序字段
    enum Field {
      // 默认排序
      FIELD_UNSPECIFIED = 0;
      // 自定义字段ID
      ID = 1;
      // 创建时间
      CREATED_TIME = 2;
      // 更新时间
      UPDATED_TIME = 3;
      // 显示顺序
      DISPLAY_ORDER = 4;
    }
    // 排序方向
    enum Direction {
      // 默认排序
      DIRECTION_UNSPECIFIED = 0;
      // 升序
      ASC = 1;
      // 降序
      DESC = 2;
    }
    // 排序字段
    Field field = 1;
    // 排序方向
    Direction direction = 2;
  }
  // 过滤条件
  Filter filter = 1 [(buf.validate.field).required = true];
  // 排序
  Sorting sorting = 2;
  // 分页大小
  int32 page_size = 3 [(buf.validate.field).int32.gte = 1, (buf.validate.field).int32.lte = 1000];
  // 分页令牌
  string page_token = 4;
  // 是否返回总数量, 默认不返回, 会影响性能
  bool return_total_size = 5;
}

// ListCustomFieldsResponse 列出自定义字段响应
message ListCustomFieldsResponse {
  // 自定义字段列表
  repeated CustomField custom_fields = 1;
  // 下一页令牌
  string next_page_token = 2;
  // 总数量, 需要设置 return_total_size 为 true 才会返回
  optional int64 total_size = 3;
}

// UpdateCustomFieldRequest 更新自定义字段请求
message UpdateCustomFieldRequest {
  // id
  int64 id = 1 [(buf.validate.field).int64.gt = 0];
  // 字段名称
  optional string label = 2;
  // 是否必填
  optional bool is_required = 3;
  // (-- api-linter: core::0216::state-field-output-only=disabled
  //     aip.dev/not-precedent: this field is used for updating, not for output --)
  // 状态
  optional CustomField.State state = 4;
  // 默认值
  optional CustomField.Value default_value = 5;
  // options 
  repeated CustomField.Option options = 6;
  // display order
  optional int32 display_order = 7;
  // help text
  optional string help_text = 8;
}

// UpdateCustomFieldResponse 更新自定义字段响应
message UpdateCustomFieldResponse {
  // 自定义字段
  CustomField custom_field = 1;
}

// (-- api-linter: core::0234::request-parent-field=disabled
//     aip.dev/not-precedent: 无 parent 语义 --)
// (-- api-linter: core::0234::request-parent-reference=disabled
//     aip.dev/not-precedent: 无 parent 语义 --)
// (-- api-linter: core::0234::request-requests-field=disabled
//     aip.dev/not-precedent: 不复用 UpdateCustomFieldRequest 结构 --)
// BatchUpdateCustomFieldsRequest 批量更新自定义字段请求
message BatchUpdateCustomFieldsRequest {
  // 父级资源路径, 目前为 organization id, 做批量更新时加锁用
  string parent = 1 [(buf.validate.field).string.min_len = 1];
  
  // 批量更新请求列表
  repeated UpdateCustomFieldRequest requests = 2 [
    (buf.validate.field).repeated.min_items = 1,
    (buf.validate.field).repeated.max_items = 100,
    (google.api.field_behavior) = REQUIRED
  ];
}

// BatchUpdateCustomFieldsResponse 批量更新自定义字段响应
message BatchUpdateCustomFieldsResponse {
  // 更新后的自定义字段列表
  repeated CustomField custom_fields = 1;
}

// DeleteCustomFieldRequest 删除自定义字段请求
message DeleteCustomFieldRequest {
  // 自定义字段ID
  int64 id = 1 [(buf.validate.field).int64.gt = 0];
}

// DeleteCustomFieldResponse 删除自定义字段响应
message DeleteCustomFieldResponse {}

// ==================== Unified Metadata Request/Response Messages ====================
// CreateMetadataRequest 创建统一元数据请求
message CreateMetadataRequest {
  // 组织引用
  OrganizationRef organization = 1 [(google.api.field_behavior) = REQUIRED];
  // 客户类型
  CustomerType customer_type = 2 [(google.api.field_behavior) = REQUIRED];
  // 元数据名称
  string given_name = 3;
  // 元数据姓氏
  string family_name = 4;
  // 元数据自定义字段
  optional google.protobuf.Struct custom_fields = 5;
  // 元数据生命周期ID
  optional int64 lifecycle_id = 6;
  // 负责人员工ID
  optional int64 owner_staff_id = 7;
  // 行动状态ID
  optional int64 action_state_id = 8;
  // 头像路径
  optional string avatar_path = 9 [(buf.validate.field).string.max_len = 255];
  // 推荐来源ID
  optional int64 referral_source_id = 10 [(buf.validate.field).int64.gt = 0];
}

// CreateMetadataResponse 创建统一元数据响应
message CreateMetadataResponse {
  // 元数据
  Metadata metadata = 1;
}

// GetMetadataRequest 获取统一元数据请求
message GetMetadataRequest {
  // 元数据ID
  int64 id = 1 [(buf.validate.field).int64.gt = 0];
}

// GetMetadataResponse 获取统一元数据响应
message GetMetadataResponse {
  // 元数据
  Metadata metadata = 1;
}

// ListMetadataRequest 列出统一元数据请求
message ListMetadataRequest {
  // filter
  message Filter {
    // metadata id list
    repeated int64 ids = 1;
    // 组织引用
    OrganizationRef organization = 2 [(buf.validate.field).required = true];
    // 客户类型 (必填，用于确定查询customer还是lead)
    CustomerType customer_type = 3 [(google.api.field_behavior) = REQUIRED];
    // 状态
    repeated Metadata.State states = 4 [(google.api.field_behavior) = OUTPUT_ONLY];
    // 生命周期ID
    repeated int64 lifecycle_ids = 5;
    // 负责人员工ID
    repeated int64 owner_staff_ids = 6;
    // 推荐来源ID
    repeated int64 referral_source_ids = 7;
    // lead特有：转换的客户ID (仅当customer_type=LEAD时使用)
    repeated int64 converted_customer_ids = 8;
    // lead特有：是否已转换 (仅当customer_type=LEAD时使用)
    optional bool is_converted = 9;
  }
  // sorting
  message Sorting {
    // sorting field
    enum Field {
      // 默认排序
      FIELD_UNSPECIFIED = 0;
      // 元数据ID
      ID = 1;
      // 创建时间
      CREATED_TIME = 2;
      // 更新时间
      UPDATED_TIME = 3;
    }
    // sorting direction
    enum Direction {
      // 默认排序
      DIRECTION_UNSPECIFIED = 0;
      // 升序
      ASC = 1;
      // 降序
      DESC = 2;
    }
    // 排序字段
    Field field = 1;
    // 排序方向
    Direction direction = 2;
  }
  // 过滤条件
  Filter filter = 1 [(buf.validate.field).required = true];
  // 排序
  Sorting sorting = 2;
  // 分页
  // 分页大小
  int32 page_size = 3 [(buf.validate.field).int32.gte = 1, (buf.validate.field).int32.lte = 1000];
  // 分页令牌
  string page_token = 4;
  // 是否返回总数量, 默认不返回, 会影响性能
  bool return_total_size = 5;
}

// ListMetadataResponse 列出统一元数据响应
message ListMetadataResponse {
  // 元数据列表
  repeated Metadata metadata = 1;
  // 下一页令牌
  string next_page_token = 2;
  // 总数量, 需要设置 return_total_size 为 true 才会返回
  optional int64 total_size = 3;
}

// UpdateMetadataRequest 更新统一元数据请求
message UpdateMetadataRequest {
  // id
  int64 id = 1 [(buf.validate.field).int64.gt = 0];
  // 客户类型 (用于路由到正确的逻辑层)
  CustomerType customer_type = 2;
  // 元数据名称
  optional string given_name = 3;
  // 元数据姓氏
  optional string family_name = 4;
  // 元数据自定义字段
  optional google.protobuf.Struct custom_fields = 5;
  // 元数据生命周期ID
  optional int64 lifecycle_id = 6;
  // 负责人员工ID
  optional int64 owner_staff_id = 7;
  // 行动状态ID
  optional int64 action_state_id = 8;
  // 头像路径
  optional string avatar_path = 9 [(buf.validate.field).string.max_len = 255];
  // 推荐来源ID
  optional int64 referral_source_id = 10 [(buf.validate.field).int64.gt = 0];
  // state
  optional Metadata.State state = 11 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// UpdateMetadataResponse 更新统一元数据响应
message UpdateMetadataResponse {
  // 元数据
  Metadata metadata = 1;
}

// DeleteMetadataRequest 删除统一元数据请求
message DeleteMetadataRequest {
  // 元数据ID
  int64 id = 1 [(buf.validate.field).int64.gt = 0];
  // 客户类型 (用于路由到正确的逻辑层)
  CustomerType customer_type = 2;
}

// DeleteMetadataResponse 删除统一元数据响应
message DeleteMetadataResponse {}

// CreateMetadataAggregateRequest 聚合创建元数据及相关实体请求
message CreateMetadataAggregateRequest {
  // 组织引用
  OrganizationRef organization = 1 [(google.api.field_behavior) = REQUIRED];
  // 客户类型
  CustomerType customer_type = 2 [(google.api.field_behavior) = REQUIRED];
  // 元数据信息
  Metadata metadata = 3 [(google.api.field_behavior) = REQUIRED];
  // 联系人列表
  repeated Contact contacts = 4;
  // 地址列表
  repeated Address addresses = 5;
  // 客户相关数据 (仅当customer_type=CUSTOMER时有效)
  optional CustomerRelatedData customer_related_data = 6;
}

// CreateMetadataAggregateResponse 聚合创建元数据及相关实体响应
message CreateMetadataAggregateResponse {
  // 元数据聚合
  MetadataAggregate metadata_aggregate = 1;
}

// ErrCode 定义错误码枚举
//
// 以下两行枚举不要改动，从下面开始累加最后两位数新增错误码，前四位不能改
// 如果单个服务新增错误码超过100个, 建议入土
// 如果是老服务重构的新服务可删除以上错误码继续沿用原有的错误码
// 注意：pb enum 的规范是：统一前缀，全大写，下划线
// 业务代码优先使用通用错误码 /backend/common/proto/rpc/code.proto ，再定义自己的错误码
enum ErrCode {
    // (-- api-linter: core::0126::unspecified=disabled
    //     aip.dev/not-precedent: We need to do this because
    //     the content of the error code is automatically generated by
    //     the script and is exclusive to each service.
    //     Please do not turn off this linter for the rest of the enum --)
    // customer 
    // 成功
    ERR_CODE_OK = 0;
    // 本服务自动分配的全局唯一的起始错误码
    ERR_CODE_UNSPECIFIED = 119500;
    // 客户不存在
    ERR_CODE_CUSTOMER_NOT_FOUND = 119501;
    // 客户已存在
    ERR_CODE_CUSTOMER_ALREADY_EXISTS = 119502;
    // 无效的客户ID
    ERR_CODE_INVALID_CUSTOMER_ID = 119503;
    // 无效的客户名称
    ERR_CODE_INVALID_CUSTOMER_NAME = 119504;
    // 客户已删除
    ERR_CODE_CUSTOMER_DELETED = 119505;
    // 创建客户失败
    ERR_CODE_CREATE_CUSTOMER_FAILED = 119506;
  
    // address 
    // 地址不存在
    ERR_CODE_ADDRESS_NOT_FOUND = 119510;
    // 无效的地址信息
    ERR_CODE_INVALID_ADDRESS = 119511;
    // 超出地址数量限制
    ERR_CODE_ADDRESS_LIMIT_EXCEEDED = 119512;
    // 不允许重复设置主地址
    ERR_CODE_DUPLICATE_SET_PRIMARY_ADDRESS = 119513;
  
    // task
    // 任务不存在
    ERR_CODE_TASK_NOT_FOUND = 119520;
    // 任务已完成
    ERR_CODE_TASK_ALREADY_COMPLETED = 119521;
    // 无效的任务状态
    ERR_CODE_INVALID_TASK_STATUS = 119522;
  
    // source 
    // Action State Name 已经存在
    ERR_CODE_ACTION_STATE_NAME_EXIST = 119525;
    // Life Cycle Name 已经存在
    ERR_CODE_LIFE_CYCLE_NAME_EXIST = 119526;
    // View Name 已经存在
    ERR_CODE_VIEW_NAME_EXIST = 119527;
    // Source Name 已经存在
    ERR_CODE_SOURCE_NAME_EXIST = 119528;
    // Tag Name 已经存在
    ERR_CODE_TAG_NAME_EXIST = 119529;

    // contact
    // 联系人不存在
    ERR_CODE_CONTACT_NOT_FOUND = 119535;
    // 联系人已存在
    ERR_CODE_CONTACT_ALREADY_EXISTS = 119536;
    // 无效的联系人信息
    ERR_CODE_INVALID_CONTACT = 119537;
    // 更新的内容与该联系方式记录的内容不符 (即contact记录email就只能更新email)
    ERR_CODE_UPDATE_CONTACT_CONTENT_NOT_MATCH = 119538;
  
    // lead
    // 线索不存在
    ERR_CODE_LEAD_NOT_FOUND = 119540;
    // 创建线索失败
    ERR_CODE_CREATE_LEAD_FAILED = 119541;

    // contact tag
    // 标签不存在
    ERR_CODE_CONTACT_TAG_NOT_FOUND = 119545;
    // 创建标签失败
    ERR_CODE_CREATE_CONTACT_TAG_FAILED = 119546;

    // custom field
    // 自定义字段不存在
    ERR_CODE_CUSTOM_FIELD_NOT_FOUND = 119550;
    // 创建自定义字段失败
    ERR_CODE_CREATE_CUSTOM_FIELD_FAILED = 119551;
    // 自定义字段已存在
    ERR_CODE_CUSTOM_FIELD_ALREADY_EXISTS = 119552;
    // 自定义字段已删除
    ERR_CODE_CUSTOM_FIELD_DELETED = 119553;
    // 自定义字段选项不存在
    ERR_CODE_CUSTOM_FIELD_OPTION_NOT_FOUND = 119554;
    // 自定义字段选项已删除
    ERR_CODE_CUSTOM_FIELD_OPTION_DELETED = 119555;

    // customer related data
    // 客户关联数据不存在
    ERR_CODE_CUSTOMER_RELATED_DATA_NOT_FOUND = 119560;
    // 聚合创建失败
    ERR_CODE_CREATE_CUSTOMER_AGGREGATE_FAILED = 119561;
}
