package contact

import (
	"time"

	"gorm.io/datatypes"

	"github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres"
	contacttag "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/contact_tag"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

type Contact struct {
	ID               int64                           `gorm:"primaryKey;column:id"`
	CustomerID       int64                           `gorm:"column:customer_id"`
	OrganizationID   int64                           `gorm:"column:organization_id"`
	OrganizationType customerpb.OrganizationRef_Type `gorm:"column:organization_type;serializer:proto_enum"`
	GivenName        string                          `gorm:"column:given_name"`
	FamilyName       string                          `gorm:"column:family_name"`
	Email            string                          `gorm:"column:email"`
	Phone            string                          `gorm:"column:phone"`
	IsSelf           bool                            `gorm:"column:is_self"`
	Note             string                          `gorm:"column:note"`
	State            customerpb.Contact_State        `gorm:"column:state;serializer:proto_enum"`
	Title            string                          `gorm:"column:title"`
	CustomFields     datatypes.JSON                  `gorm:"column:custom_fields;type:jsonb;default:'{}'"`
	DeletedTime      *time.Time                      `gorm:"column:deleted_time"`
	CreatedTime      time.Time                       `gorm:"column:created_time;autoCreateTime"`
	UpdatedTime      time.Time                       `gorm:"column:updated_time;autoUpdateTime"`

	// Tags
	Tags []*contacttag.ContactTag `gorm:"-"`
}

func (Contact) TableName() string {
	return "contact"
}

type ListFilter struct {
	IDs             []int64
	CustomerIDs     []int64
	Email           string
	Phone           string
	Phones          []string
	States          []customerpb.Contact_State
	Types           []customerpb.ContactTag_Type
	Emails          []string
	OrganizationRef *customerpb.OrganizationRef
}

type Pagination struct {
	PageSize        int32
	Cursor          *postgres.Cursor
	ReturnTotalSize bool
}

type OrderBy struct {
	Field     customerpb.ListContactsRequest_Sorting_Field
	Direction customerpb.ListContactsRequest_Sorting_Direction
}

// CursorResult 游标分页结果
type CursorResult struct {
	Data       []*Contact `json:"data"`     // 数据列表
	HasNext    bool       `json:"has_next"` // 是否有下一页
	TotalCount *int64     `json:"total_count"`
}

// FilterConditions 联系方式过滤条件
type FilterConditions struct {
	CompanyID   int64             `json:"company_id"`
	BusinessIDs []int64           `json:"business_ids"`
	CustomerIDs []int64           `json:"customer_ids"`
	Conditions  []FilterCondition `json:"conditions"`
	Combiner    string            `json:"combiner"` // AND | OR
}

// FilterCondition 单个过滤条件
type FilterCondition struct {
	Field    string   `json:"field"`
	Operator string   `json:"operator"`
	Value    string   `json:"value"`
	Values   []string `json:"values"`
	Start    string   `json:"start"`
	End      string   `json:"end"`
}

// HavingCondition 计数过滤条件
type HavingCondition struct {
	Field string `json:"field"` // contact_count, email_count, phone_count
	// EQUAL, NOT_EQUAL, LESS_THAN, LESS_THAN_OR_EQUAL, GREATER_THAN, GREATER_THAN_OR_EQUAL
	Operator string `json:"operator"`
	Value    string `json:"value"`
}

// CustomerPhone 客户电话信息
type CustomerPhone struct {
	CustomerID  int64  `json:"customer_id"`
	PhoneNumber string `json:"phone_number"`
}
