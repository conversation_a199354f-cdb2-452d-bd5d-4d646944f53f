package address

import (
	"time"

	"github.com/lib/pq"

	"github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

type Address struct {
	ID                 int64                           `gorm:"primaryKey;column:id"`
	CustomerID         int64                           `gorm:"column:customer_id"`
	OrganizationID     int64                           `gorm:"column:organization_id"`
	OrganizationType   customerpb.OrganizationRef_Type `gorm:"column:organization_type;serializer:proto_enum"`
	Revision           int32                           `gorm:"column:revision"`
	RegionCode         string                          `gorm:"column:region_code"`
	LanguageCode       string                          `gorm:"column:language_code"`
	Organization       string                          `gorm:"column:organization"`
	PostalCode         string                          `gorm:"column:postal_code"`
	SortingCode        string                          `gorm:"column:sorting_code"`
	AdministrativeArea string                          `gorm:"column:administrative_area"`
	Locality           string                          `gorm:"column:locality"`
	Sublocality        string                          `gorm:"column:sublocality"`
	AddressLines       pq.StringArray                  `gorm:"column:address_lines;type:text[]"`
	Recipients         pq.StringArray                  `gorm:"column:recipients;type:text[]"`
	Latitude           float64                         `gorm:"column:latitude"`
	Longitude          float64                         `gorm:"column:longitude"`
	Type               customerpb.Address_Type         `gorm:"column:type;serializer:proto_enum"`
	State              customerpb.Address_State        `gorm:"column:state;serializer:proto_enum"`
	DeletedTime        *time.Time                      `gorm:"column:deleted_time"`
	CreatedTime        time.Time                       `gorm:"column:created_time;autoCreateTime"`
	UpdatedTime        time.Time                       `gorm:"column:updated_time;autoUpdateTime"`
}

func (Address) TableName() string {
	return "address"
}

type ListFilter struct {
	IDs              []int64
	CustomerID       int64
	OrganizationID   int64
	OrganizationType customerpb.OrganizationRef_Type
	States           []customerpb.Address_State
	CustomerIDs      []int64
	Types            []customerpb.Address_Type
}

type Pagination struct {
	PageSize        int32
	Cursor          *postgres.Cursor
	ReturnTotalSize bool
}

type OrderBy struct {
	Field     customerpb.ListAddressesRequest_Sorting_Field
	Direction customerpb.ListAddressesRequest_Sorting_Direction
}

// CursorResult 游标分页结果
type CursorResult struct {
	Data       []*Address `json:"data"`     // 数据列表
	HasNext    bool       `json:"has_next"` // 是否有下一页
	TotalCount *int64     `json:"total_count"`
}

// HavingCondition 计数过滤条件
type HavingCondition struct {
	Field    string // 字段名，如 address_count
	Operator string // 操作符，如 GREATER_THAN
	Value    string // 值
}
