package address

import (
	"context"
	"fmt"
	"strings"
	"time"

	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

type Repository interface {
	Create(ctx context.Context, address *Address) (*Address, error)
	Get(ctx context.Context, id int64) (*Address, error)
	GetPrimaryAddress(ctx context.Context, customerID int64) (*Address, error)
	ListByCursor(ctx context.Context,
		filter *ListFilter, pagination *Pagination, orderBy *OrderBy) (*CursorResult, error)
	Update(ctx context.Context, address *Address) (*Address, error)
	Delete(ctx context.Context, id int64) error
	WithTx(tx *gorm.DB) Repository

	// Query methods for CustomerQueryService
	// SearchAddressCustomerIDs 根据关键词搜索地址关联的客户ID
	SearchAddressCustomerIDs(
		ctx context.Context, companyID int64, businessIDs []int64, keyword string,
		organizationID int64, organizationType customerpb.OrganizationRef_Type,
	) ([]int64, error)
	// FilterCustomerIDsByZip 根据邮编过滤客户ID
	FilterCustomerIDsByZip(
		ctx context.Context, companyID int64, businessIDs []int64, customerIDs []int64,
		zipList []string, operator string,
		organizationID int64, organizationType customerpb.OrganizationRef_Type,
	) ([]int64, error)
	// CountFilterAddressCustomerIDs 根据计数条件过滤地址关联的客户ID
	CountFilterAddressCustomerIDs(
		ctx context.Context, companyID int64, businessIDs []int64, customerIDs []int64,
		havingConditions []HavingCondition,
		organizationID int64, organizationType customerpb.OrganizationRef_Type,
	) ([]int64, error)
}

func (i *impl) WithTx(tx *gorm.DB) Repository {
	return &impl{db: tx}
}

type impl struct {
	db *gorm.DB
}

func New() Repository {
	return &impl{
		db: postgres.GetDB(),
	}
}

func (i *impl) Create(ctx context.Context, address *Address) (*Address, error) {
	err := i.db.WithContext(ctx).Create(address).Error
	if err != nil {
		return nil, err
	}

	return address, nil
}

func (i *impl) Get(ctx context.Context, id int64) (*Address, error) {
	var address Address
	err := i.db.WithContext(ctx).
		Where("id = ?", id).
		Where("state = ?", customerpb.Customer_ACTIVE.String()).
		First(&address).Error
	if err != nil {
		return nil, err
	}

	return &address, nil
}

// ListWithCursor 基于游标分页的列表查询
func (i *impl) ListByCursor(ctx context.Context,
	filter *ListFilter, pagination *Pagination, orderBy *OrderBy) (*CursorResult, error) {
	var total *int64
	if pagination.ReturnTotalSize {
		count, err := i.getTotalCount(ctx, filter)
		if err != nil {
			return nil, err
		}
		total = &count
	}
	query := i.db.WithContext(ctx).Model(&Address{})
	query = i.applyFilter(query, filter)
	query = i.applyCursor(query, pagination.Cursor, orderBy)
	query = i.applyOrderBy(query, orderBy)

	// 多查一条用于判断是否有下一页
	query = query.Limit(int(pagination.PageSize + 1))

	var addresses []*Address
	if err := query.Find(&addresses).Error; err != nil {
		return nil, err
	}

	hasNext := len(addresses) > int(pagination.PageSize)
	if hasNext {
		addresses = addresses[:pagination.PageSize]
	}

	return &CursorResult{
		Data:       addresses,
		HasNext:    hasNext,
		TotalCount: total,
	}, nil
}

func (i *impl) applyFilter(query *gorm.DB, filter *ListFilter) *gorm.DB {
	if filter == nil {
		return query
	}

	if filter.IDs != nil {
		query = query.Where("id IN (?)", filter.IDs)
	}

	if filter.CustomerID != 0 {
		query = query.Where("customer_id = ?", filter.CustomerID)
	}

	if filter.OrganizationID != 0 {
		query = query.Where("organization_id = ? AND organization_type = ?",
			filter.OrganizationID, filter.OrganizationType.String())
	}

	if len(filter.CustomerIDs) > 0 {
		query = query.Where("customer_id IN (?)", filter.CustomerIDs)
	}

	if len(filter.Types) > 0 {
		types := make([]string, 0, len(filter.Types))
		for _, t := range filter.Types {
			types = append(types, t.String())
		}
		query = query.Where("type IN (?)", types)
	}

	if len(filter.States) > 0 {
		states := make([]string, 0, len(filter.States))
		for _, state := range filter.States {
			states = append(states, state.String())
		}
		query = query.Where("state IN (?)", states)
	}

	return query
}

func (i *impl) applyCursor(query *gorm.DB, cursor *postgres.Cursor, orderBy *OrderBy) *gorm.DB {
	if cursor == nil {
		return query
	}

	if orderBy != nil && orderBy.Direction == customerpb.ListAddressesRequest_Sorting_DESC {
		query = query.Where("(created_time, id) < (?, ?)", cursor.CreatedAt, cursor.ID)
	} else {
		query = query.Where("(created_time, id) > (?, ?)", cursor.CreatedAt, cursor.ID)
	}

	return query
}

func (i *impl) applyOrderBy(query *gorm.DB, orderBy *OrderBy) *gorm.DB {
	if orderBy != nil {
		query = query.
			Order(fmt.Sprintf("%s %s, id %s", orderBy.Field.String(),
				orderBy.Direction.String(), orderBy.Direction.String()))
	} else {
		query = query.Order("created_time DESC, id DESC")
	}

	return query
}

func (i *impl) getTotalCount(ctx context.Context, filter *ListFilter) (int64, error) {
	query := i.db.WithContext(ctx).Model(&Address{})
	query = i.applyFilter(query, filter)

	var count int64
	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}

	return count, nil
}

func (i *impl) Update(ctx context.Context, address *Address) (*Address, error) {
	err := i.db.WithContext(ctx).Updates(address).Error
	if err != nil {
		return nil, err
	}

	return address, nil
}

func (i *impl) Delete(ctx context.Context, id int64) error {
	now := time.Now()

	return i.db.WithContext(ctx).
		Model(&Address{}).
		Where("id = ?", id).
		Updates(&Address{
			State:       customerpb.Address_DELETED,
			DeletedTime: &now,
		}).Error
}

func (i *impl) GetPrimaryAddress(ctx context.Context, customerID int64) (*Address, error) {
	var address Address
	err := i.db.WithContext(ctx).
		Where("customer_id = ?", customerID).
		Where("type = ?", customerpb.Address_PRIMARY).
		First(&address).Error
	if err != nil {
		return nil, err
	}

	return &address, nil
}

// SearchAddressCustomerIDs 根据关键词搜索地址关联的客户ID
func (i *impl) SearchAddressCustomerIDs(
	ctx context.Context, companyID int64, businessIDs []int64, keyword string,
	organizationID int64, organizationType customerpb.OrganizationRef_Type,
) ([]int64, error) {
	// 1. 从 customer_related_data 获取符合 scope 的 customer_id
	crdQuery := i.db.WithContext(ctx).Table("customer_related_data").
		Select("DISTINCT customer_id").
		Where("state != ? AND company_id = ?", "DELETED", companyID)
	if len(businessIDs) > 0 {
		crdQuery = crdQuery.Where("business_id IN ?", businessIDs)
	}
	var scopedCustomerIDs []int64
	if err := crdQuery.Pluck("customer_id", &scopedCustomerIDs).Error; err != nil {
		return nil, fmt.Errorf("get scoped customer ids from customer_related_data: %w", err)
	}
	if len(scopedCustomerIDs) == 0 {
		return nil, nil
	}

	// 2. 过滤掉不符合条件的 customer
	customerQuery := i.db.WithContext(ctx).Table("customer").
		Select("id").
		Where("id IN ?", scopedCustomerIDs).
		Where("state != ?", customerpb.Customer_DELETED.String()).
		Where("convert_to_customer_id = 0")
	var activeCustomerIDs []int64
	if err := customerQuery.Pluck("id", &activeCustomerIDs).Error; err != nil {
		return nil, fmt.Errorf("get active customer ids: %w", err)
	}
	if len(activeCustomerIDs) == 0 {
		return nil, nil
	}

	// 3. 从 address 表查询
	addressQuery := i.db.WithContext(ctx).Table("address a").
		Where("a.state != ?", customerpb.Address_DELETED.String()).
		Where("a.customer_id IN ?", activeCustomerIDs)

	if organizationID != 0 {
		addressQuery = addressQuery.Where("a.organization_id = ? AND a.organization_type = ?",
			organizationID, organizationType.String())
	}

	// 构建地址搜索条件：拼接所有地址字段进行模糊搜索
	addressFields := "COALESCE(a.organization, '') || ' ' || " +
		"COALESCE(a.postal_code, '') || ' ' || " +
		"COALESCE(a.administrative_area, '') || ' ' || " +
		"COALESCE(a.locality, '') || ' ' || " +
		"COALESCE(a.sublocality, '') || ' ' || " +
		"array_to_string(a.address_lines, ' ')"

	addressQuery = addressQuery.Where(addressFields+" ILIKE ?", "%"+keyword+"%")

	var customerIDs []int64
	err := addressQuery.Distinct("a.customer_id").Pluck("a.customer_id", &customerIDs).Error
	if err != nil {
		return nil, err
	}

	return customerIDs, nil
}

// FilterCustomerIDsByZip 根据邮编过滤客户ID
func (i *impl) FilterCustomerIDsByZip(
	ctx context.Context, companyID int64, businessIDs []int64, customerIDs []int64,
	zipList []string, operator string,
	organizationID int64, organizationType customerpb.OrganizationRef_Type,
) ([]int64, error) {
	query := i.db.WithContext(ctx).Table("customer_related_data crd").
		Joins("JOIN customer c ON crd.customer_id = c.id").
		Joins("LEFT JOIN address a ON crd.customer_id = a.customer_id AND a.state != ?",
			customerpb.Address_DELETED.String()).
		Where("c.state != ?", customerpb.Customer_DELETED.String()).
		Where("c.convert_to_customer_id = 0").
		Where("crd.company_id = ?", companyID).
		Where("crd.state != ?", "DELETED")

	if organizationID != 0 {
		query = query.Where("a.organization_id = ? AND a.organization_type = ?",
			organizationID, organizationType.String())
	}

	if len(businessIDs) > 0 {
		query = query.Where("crd.business_id IN ?", businessIDs)
	}

	if len(customerIDs) > 0 {
		query = query.Where("crd.customer_id IN ?", customerIDs)
	}

	// 清理 zipList 中可能包含的多余引号
	cleanZipList := make([]string, len(zipList))
	for i, zip := range zipList {
		// 移除字符串开头和结尾的单引号或双引号
		cleanZip := strings.Trim(zip, `'"`)
		cleanZipList[i] = cleanZip
	}

	switch operator {
	case "IN":
		if len(cleanZipList) > 0 {
			query = query.Where("a.postal_code IN ?", cleanZipList)
		}
	case "NOT_IN":
		if len(cleanZipList) > 0 {
			query = query.Where("(a.postal_code IS NULL OR a.postal_code NOT IN ?)", cleanZipList)
		}
	default:
		return nil, fmt.Errorf("unsupported operator: %s", operator)
	}

	var resultIDs []int64
	err := query.Distinct("crd.customer_id").Pluck("crd.customer_id", &resultIDs).Error
	if err != nil {
		return nil, err
	}

	return resultIDs, nil
}

// CountFilterAddressCustomerIDs 根据计数条件过滤地址关联的客户ID
func (i *impl) CountFilterAddressCustomerIDs(
	ctx context.Context, companyID int64, businessIDs []int64, customerIDs []int64,
	havingConditions []HavingCondition,
	organizationID int64, organizationType customerpb.OrganizationRef_Type,
) ([]int64, error) {
	query := i.db.WithContext(ctx).Table("customer_related_data crd").
		Joins("JOIN customer c ON crd.customer_id = c.id").
		Joins("LEFT JOIN address a ON crd.customer_id = a.customer_id AND a.state != ?",
			customerpb.Address_DELETED.String()).
		Where("c.state != ?", customerpb.Customer_DELETED.String()).
		Where("c.convert_to_customer_id = 0").
		Where("crd.company_id = ?", companyID).
		Where("crd.state != ?", "DELETED")

	if organizationID != 0 {
		query = query.Where("a.organization_id = ? AND a.organization_type = ?",
			organizationID, organizationType.String())
	}

	if len(businessIDs) > 0 {
		query = query.Where("crd.business_id IN ?", businessIDs)
	}

	if len(customerIDs) > 0 {
		query = query.Where("crd.customer_id IN ?", customerIDs)
	}

	query = query.Group("crd.customer_id")

	// 构建 HAVING 条件
	if len(havingConditions) > 0 {
		havingSQL, args, err := i.buildHavingConditions(havingConditions)
		if err != nil {
			return nil, err
		}
		if havingSQL != "" {
			query = query.Having(havingSQL, args...)
		}
	}

	var resultIDs []int64
	err := query.Pluck("crd.customer_id", &resultIDs).Error
	if err != nil {
		return nil, err
	}

	return resultIDs, nil
}

// buildHavingConditions 构建 HAVING 条件SQL
func (i *impl) buildHavingConditions(conditions []HavingCondition) (string, []interface{}, error) {
	if len(conditions) == 0 {
		return "", nil, nil
	}

	var sqlParts []string
	var args []interface{}

	for _, condition := range conditions {
		conditionSQL, conditionArgs, err := i.buildSingleHavingCondition(condition)
		if err != nil {
			return "", nil, err
		}
		if conditionSQL != "" {
			sqlParts = append(sqlParts, conditionSQL)
			args = append(args, conditionArgs...)
		}
	}

	if len(sqlParts) == 0 {
		return "", nil, nil
	}

	return strings.Join(sqlParts, " AND "), args, nil
}

// buildSingleHavingCondition 构建单个 HAVING 条件
func (i *impl) buildSingleHavingCondition(condition HavingCondition) (string, []interface{}, error) {
	// 映射字段名到对应的聚合表达式
	fieldExpressionMap := map[string]string{
		"address_count": "COUNT(CASE WHEN a.id IS NOT NULL THEN 1 END)",
	}

	fieldExpression, exists := fieldExpressionMap[condition.Field]
	if !exists {
		return "", nil, fmt.Errorf("unsupported having field: %s", condition.Field)
	}

	// 将枚举操作符映射到 SQL 操作符
	operatorMap := map[string]string{
		"EQUAL":                 "=",
		"NOT_EQUAL":             "!=",
		"LESS_THAN":             "<",
		"LESS_THAN_OR_EQUAL":    "<=",
		"GREATER_THAN":          ">",
		"GREATER_THAN_OR_EQUAL": ">=",
	}

	sqlOperator, exists := operatorMap[condition.Operator]
	if !exists {
		return "", nil, fmt.Errorf("unsupported having operator: %s", condition.Operator)
	}

	return fieldExpression + " " + sqlOperator + " ?", []interface{}{condition.Value}, nil
}
