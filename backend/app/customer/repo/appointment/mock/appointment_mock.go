// Code generated by MockGen. DO NOT EDIT.
// Source: ./appointment/appointment.go
//
// Generated by this command:
//
//	mockgen -source=./appointment/appointment.go -destination=./appointment/mock/appointment_mock.go -package=mock
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	appointmentpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1"
	businesscustomerpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	gomock "go.uber.org/mock/gomock"
)

// MockReadWriter is a mock of ReadWriter interface.
type MockReadWriter struct {
	ctrl     *gomock.Controller
	recorder *MockReadWriterMockRecorder
	isgomock struct{}
}

// MockReadWriterMockRecorder is the mock recorder for MockReadWriter.
type MockReadWriterMockRecorder struct {
	mock *MockReadWriter
}

// NewMockReadWriter creates a new mock instance.
func NewMockReadWriter(ctrl *gomock.Controller) *MockReadWriter {
	mock := &MockReadWriter{ctrl: ctrl}
	mock.recorder = &MockReadWriterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockReadWriter) EXPECT() *MockReadWriterMockRecorder {
	return m.recorder
}

// ListAppointmentsForCustomers mocks base method.
func (m *MockReadWriter) ListAppointmentsForCustomers(ctx context.Context, companyID, customerID int64) ([]*appointmentpb.AppointmentModel, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListAppointmentsForCustomers", ctx, companyID, customerID)
	ret0, _ := ret[0].([]*appointmentpb.AppointmentModel)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListAppointmentsForCustomers indicates an expected call of ListAppointmentsForCustomers.
func (mr *MockReadWriterMockRecorder) ListAppointmentsForCustomers(ctx, companyID, customerID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAppointmentsForCustomers", reflect.TypeOf((*MockReadWriter)(nil).ListAppointmentsForCustomers), ctx, companyID, customerID)
}

// MergeCustomerAppointment mocks base method.
func (m *MockReadWriter) MergeCustomerAppointment(ctx context.Context, companyID int64, customerRel *businesscustomerpb.MergeRelationDef, petRels []*businesscustomerpb.MergeRelationDef) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MergeCustomerAppointment", ctx, companyID, customerRel, petRels)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MergeCustomerAppointment indicates an expected call of MergeCustomerAppointment.
func (mr *MockReadWriterMockRecorder) MergeCustomerAppointment(ctx, companyID, customerRel, petRels any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MergeCustomerAppointment", reflect.TypeOf((*MockReadWriter)(nil).MergeCustomerAppointment), ctx, companyID, customerRel, petRels)
}
