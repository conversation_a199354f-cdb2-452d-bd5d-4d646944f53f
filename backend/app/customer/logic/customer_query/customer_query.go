package customerquery

import (
	"context"
	"strings"

	addressRepo "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/address"
	contactRepo "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/contact"
	customerRepo "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/customer"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v1"
	customerpbv2 "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

const (
	combinerAND   = "AND"
	combinerOR    = "OR"
	operatorEQUAL = "EQUAL"
	operatorIN    = "IN"
	operatorNOTIN = "NOT_IN"
)

type Logic struct {
	customerRepo customerRepo.Repository
	contactRepo  contactRepo.Repository
	addressRepo  addressRepo.Repository
}

func New() *Logic {
	return &Logic{
		customerRepo: customerRepo.New(),
		contactRepo:  contactRepo.New(),
		addressRepo:  addressRepo.New(),
	}
}

// FilterCustomerIDs 仅按 Scope 白名单字段过滤（company_id、business_ids、customer_ids）
func (l *Logic) FilterCustomerIDs(ctx context.Context, scope *customerpb.Scope) ([]int64, error) {
	filter := &customerRepo.FilterConditions{
		CompanyID:   scope.GetCompanyId(),
		BusinessIDs: scope.GetBusinessIds(),
		CustomerIDs: scope.GetCustomerIds(),
	}

	return l.customerRepo.FilterCustomerIDs(ctx, filter)
}

// ListCustomerIDs 支持排序与分页（offset, limit），按 id 二级排序稳定输出
func (l *Logic) ListCustomerIDs(ctx context.Context, req *customerpb.ListCustomerIdsRequest) ([]int64, error) {
	var orderField string
	switch req.GetOrderBy() {
	case customerpb.CustomerOrderField_FIRST_NAME:
		orderField = "first_name"
	case customerpb.CustomerOrderField_LAST_NAME:
		orderField = "last_name"
	default:
		orderField = ""
	}

	return l.customerRepo.ListCustomerIDs(
		ctx,
		req.Scope.GetCompanyId(),
		req.Scope.GetBusinessIds(),
		req.Scope.GetCustomerIds(),
		orderField,
		toSQLDir(req.GetDirection()),
		int(req.Page.GetOffset()),
		int(req.Page.GetLimit()),
	)
}

// ValidateActiveCustomerIDs 过滤出有效客户ID（存在且未删除）
func (l *Logic) ValidateActiveCustomerIDs(ctx context.Context, companyID int64, customerIDs []int64) ([]int64, error) {
	return l.customerRepo.ValidateActiveCustomerIDs(ctx, companyID, customerIDs)
}

// GetCustomersBasicInfo 查询列表装配所需的客户基础信息
func (l *Logic) GetCustomersBasicInfo(
	ctx context.Context,
	companyID int64,
	customerIDs []int64,
) ([]*customerpb.CustomerBasicInfo, error) {
	rows, err := l.customerRepo.GetCustomersBasicRows(ctx, companyID, customerIDs)
	if err != nil {
		return nil, err
	}

	items := make([]*customerpb.CustomerBasicInfo, 0, len(rows))
	for _, r := range rows {
		email := r.Email
		items = append(items, &customerpb.CustomerBasicInfo{
			Id:                     r.ID,
			BusinessId:             r.BusinessID,
			First:                  r.FirstName,
			Last:                   r.LastName,
			AvatarPath:             r.AvatarPath,
			ClientColor:            r.ClientColor,
			Email:                  email,
			PreferredFrequencyType: r.PreferredFrequencyType,
			PreferredFrequencyDay:  r.PreferredFrequencyDay,
			Inactive:               r.Inactive,
			IsUnsubscribed:         btoi(r.IsUnsubscribed),
			Source:                 r.Source,
			AccountId:              r.AccountID,
			CompanyId:              r.CompanyID,
		})
	}

	return items, nil
}

func toSQLDir(d customerpb.OrderDirection) string {
	if d == customerpb.OrderDirection_DESC {
		return "DESC"
	}

	return "ASC"
}

func btoi(b bool) int32 {
	if b {
		return 1
	}

	return 0
}

// FilterCustomerIDsWithFilter 根据过滤条件查询客户ID（处理协议转换）
func (l *Logic) FilterCustomerIDsWithFilter(
	ctx context.Context,
	scope *customerpb.Scope,
	filter *customerpb.FilterGroup,
) ([]int64, error) {
	// 转换协议结构到 repo 层结构
	repoFilter := &customerRepo.FilterConditions{
		CompanyID:   scope.GetCompanyId(),
		BusinessIDs: scope.GetBusinessIds(),
		CustomerIDs: scope.GetCustomerIds(),
	}

	if filter != nil {
		conditions, combiner := l.convertFilterGroup(filter)
		repoFilter.Conditions = conditions
		repoFilter.Combiner = combiner
	}

	return l.customerRepo.FilterCustomerIDs(ctx, repoFilter)
}

// convertFilterGroup 转换 FilterGroup 到 repo 层结构
func (l *Logic) convertFilterGroup(filter *customerpb.FilterGroup) ([]customerRepo.FilterCondition, string) {
	var conditions []customerRepo.FilterCondition

	// 转换原子条件
	for _, condition := range filter.GetConditions() {
		repoCondition := l.convertFilterCondition(condition)
		if repoCondition != nil {
			conditions = append(conditions, *repoCondition)
		}
	}

	// 处理子组（只支持一层嵌套）
	for _, group := range filter.GetGroups() {
		groupConditions, _ := l.convertFilterGroup(group)
		conditions = append(conditions, groupConditions...)
	}

	// 获取组合方式
	combiner := strings.ToUpper(strings.TrimSpace(filter.GetCombiner()))
	if combiner != combinerAND && combiner != combinerOR {
		combiner = combinerAND
	}

	return conditions, combiner
}

// convertFilterCondition 转换单个过滤条件
func (l *Logic) convertFilterCondition(condition *customerpb.FilterCondition) *customerRepo.FilterCondition {
	if condition == nil {
		return nil
	}

	// 处理特殊字段
	field := strings.TrimSpace(condition.GetField())
	if field == "has_account" {
		return l.convertHasAccountCondition(condition)
	}

	repoCondition := &customerRepo.FilterCondition{
		Field:    field,
		Operator: l.convertOperator(condition.GetOp()),
		Value:    condition.GetValue(),
		Values:   condition.GetValues(),
		Start:    condition.GetStart(),
		End:      condition.GetEnd(),
	}

	return repoCondition
}

// convertHasAccountCondition 转换 has_account 特殊条件
func (l *Logic) convertHasAccountCondition(condition *customerpb.FilterCondition) *customerRepo.FilterCondition {
	value := strings.TrimSpace(condition.GetValue())
	hasAccount := value == "true" || value == "1"

	switch condition.GetOp() {
	case customerpb.FilterOperator_EQUAL:
		if hasAccount {
			return &customerRepo.FilterCondition{
				Field:    "account_id",
				Operator: "IS_NOT_NULL",
			}
		}

		return &customerRepo.FilterCondition{
			Field:    "account_id",
			Operator: "IS_NULL",
		}
	case customerpb.FilterOperator_NOT_EQUAL:
		if hasAccount {
			return &customerRepo.FilterCondition{
				Field:    "account_id",
				Operator: "IS_NULL",
			}
		}

		return &customerRepo.FilterCondition{
			Field:    "account_id",
			Operator: "IS_NOT_NULL",
		}
	default:
		return nil
	}
}

// convertOperator 转换操作符
func (l *Logic) convertOperator(op customerpb.FilterOperator) string {
	switch op {
	case customerpb.FilterOperator_EQUAL:
		return operatorEQUAL
	case customerpb.FilterOperator_NOT_EQUAL:
		return "NOT_EQUAL"
	case customerpb.FilterOperator_LESS_THAN:
		return "LESS_THAN"
	case customerpb.FilterOperator_LESS_THAN_OR_EQUAL:
		return "LESS_THAN_OR_EQUAL"
	case customerpb.FilterOperator_GREATER_THAN:
		return "GREATER_THAN"
	case customerpb.FilterOperator_GREATER_THAN_OR_EQUAL:
		return "GREATER_THAN_OR_EQUAL"
	case customerpb.FilterOperator_FILTER_IN:
		return "FILTER_IN"
	case customerpb.FilterOperator_FILTER_NOT_IN:
		return "FILTER_NOT_IN"
	case customerpb.FilterOperator_LIKE:
		return "LIKE"
	case customerpb.FilterOperator_IS_NULL:
		return "IS_NULL"
	case customerpb.FilterOperator_IS_NOT_NULL:
		return "IS_NOT_NULL"
	case customerpb.FilterOperator_BETWEEN:
		return "BETWEEN"
	default:
		return operatorEQUAL
	}
}

// ========== Contact Methods ==========

// SearchContactCustomerIDs 根据关键词搜索联系方式关联的客户ID
func (l *Logic) SearchContactCustomerIDs(
	ctx context.Context, req *customerpb.SearchContactCustomerIdsRequest,
) ([]int64, error) {
	scope := req.GetScope()
	if scope == nil || scope.GetCompanyId() <= 0 {
		return nil, nil
	}

	return l.contactRepo.SearchContactCustomerIDs(
		ctx,
		scope.GetCompanyId(),
		scope.GetBusinessIds(),
		req.GetKeyword(),
		req.GetNameKeyword(),
	)
}

// FilterContactCustomerIDs 根据过滤条件查询联系方式关联的客户ID
func (l *Logic) FilterContactCustomerIDs(
	ctx context.Context, req *customerpb.FilterContactCustomerIdsRequest,
) ([]int64, error) {
	scope := req.GetScope()
	if scope == nil || scope.GetCompanyId() <= 0 {
		return nil, nil
	}

	// 转换过滤条件
	filter := &contactRepo.FilterConditions{
		CompanyID:   scope.GetCompanyId(),
		BusinessIDs: scope.GetBusinessIds(),
		CustomerIDs: scope.GetCustomerIds(),
	}

	// 转换过滤条件
	if filterGroup := req.GetFilter(); filterGroup != nil {
		// 获取组合方式
		combiner := strings.ToUpper(strings.TrimSpace(filterGroup.GetCombiner()))
		if combiner != combinerAND && combiner != combinerOR {
			combiner = combinerAND
		}
		filter.Combiner = combiner

		conditions := make([]contactRepo.FilterCondition, 0, len(filterGroup.GetConditions()))
		for _, condition := range filterGroup.GetConditions() {
			conditions = append(conditions, contactRepo.FilterCondition{
				Field:    condition.GetField(),
				Operator: l.convertOperator(condition.GetOp()),
				Value:    condition.GetValue(),
				Values:   condition.GetValues(),
				Start:    condition.GetStart(),
				End:      condition.GetEnd(),
			})
		}
		filter.Conditions = conditions
	}

	return l.contactRepo.FilterContactCustomerIDs(ctx, filter)
}

// CountFilterContactCustomerIDs 根据计数条件过滤联系方式关联的客户ID
func (l *Logic) CountFilterContactCustomerIDs(
	ctx context.Context, req *customerpb.CountFilterContactCustomerIdsRequest,
) ([]int64, error) {
	scope := req.GetScope()
	if scope == nil || scope.GetCompanyId() <= 0 {
		return nil, nil
	}

	// 转换 HAVING 条件
	havingConditions := make([]contactRepo.HavingCondition, 0, len(req.GetHaving()))
	for _, condition := range req.GetHaving() {
		havingConditions = append(havingConditions, contactRepo.HavingCondition{
			Field:    condition.GetField(),
			Operator: l.convertOperator(condition.GetOp()),
			Value:    condition.GetValue(),
		})
	}

	return l.contactRepo.CountFilterContactCustomerIDs(
		ctx,
		scope.GetCompanyId(),
		scope.GetBusinessIds(),
		scope.GetCustomerIds(),
		havingConditions,
	)
}

// GetPrimaryPhones 获取指定客户的主要电话号码
func (l *Logic) GetPrimaryPhones(
	ctx context.Context, req *customerpb.GetPrimaryPhonesRequest,
) ([]*customerpb.CustomerPhone, error) {
	scope := req.GetScope()
	if scope == nil || scope.GetCompanyId() <= 0 {
		return nil, nil
	}

	phones, err := l.contactRepo.GetPrimaryPhones(
		ctx,
		scope.GetCompanyId(),
		scope.GetBusinessIds(),
		req.GetCustomerIds(),
	)
	if err != nil {
		return nil, err
	}

	// 转换为 protobuf 格式
	result := make([]*customerpb.CustomerPhone, 0, len(phones))
	for _, phone := range phones {
		result = append(result, &customerpb.CustomerPhone{
			CustomerId:  phone.CustomerID,
			PhoneNumber: phone.PhoneNumber,
		})
	}

	return result, nil
}

// ========== Address Methods ==========

// SearchAddressCustomerIDs 根据关键词搜索地址关联的客户ID
func (l *Logic) SearchAddressCustomerIDs(
	ctx context.Context, req *customerpb.SearchAddressCustomerIdsRequest,
) ([]int64, error) {
	scope := req.GetScope()
	if scope == nil || scope.GetCompanyId() <= 0 {
		return nil, nil
	}

	return l.addressRepo.SearchAddressCustomerIDs(
		ctx,
		scope.GetCompanyId(),
		scope.GetBusinessIds(),
		req.GetKeyword(),
		scope.GetCompanyId(),
		customerpbv2.OrganizationRef_COMPANY,
	)
}

// FilterCustomerIDsByZip 根据邮编过滤客户ID
func (l *Logic) FilterCustomerIDsByZip(
	ctx context.Context, req *customerpb.FilterCustomerIdsByZipRequest,
) ([]int64, error) {
	scope := req.GetScope()
	if scope == nil || scope.GetCompanyId() <= 0 {
		return nil, nil
	}

	// 转换操作符
	var operator string
	switch req.GetOperator() {
	case customerpb.InNotInOperator_IN:
		operator = "IN"
	case customerpb.InNotInOperator_NOT_IN:
		operator = "NOT_IN"
	default:
		return nil, nil
	}

	return l.addressRepo.FilterCustomerIDsByZip(
		ctx,
		scope.GetCompanyId(),
		scope.GetBusinessIds(),
		scope.GetCustomerIds(),
		req.GetZipList(),
		operator,
		scope.GetCompanyId(),
		customerpbv2.OrganizationRef_COMPANY,
	)
}

// CountFilterAddressCustomerIDs 根据计数条件过滤地址关联的客户ID
func (l *Logic) CountFilterAddressCustomerIDs(
	ctx context.Context, req *customerpb.CountFilterAddressCustomerIdsRequest,
) ([]int64, error) {
	scope := req.GetScope()
	if scope == nil || scope.GetCompanyId() <= 0 {
		return nil, nil
	}

	// 转换 HAVING 条件
	havingConditions := make([]addressRepo.HavingCondition, 0, len(req.GetHaving()))
	for _, condition := range req.GetHaving() {
		havingConditions = append(havingConditions, addressRepo.HavingCondition{
			Field:    condition.GetField(),
			Operator: l.convertOperator(condition.GetOp()),
			Value:    condition.GetValue(),
		})
	}

	return l.addressRepo.CountFilterAddressCustomerIDs(
		ctx,
		scope.GetCompanyId(),
		scope.GetBusinessIds(),
		scope.GetCustomerIds(),
		havingConditions,
		scope.GetCompanyId(),
		customerpbv2.OrganizationRef_COMPANY,
	)
}

// ========== Tag Methods ==========

// FilterCustomerIDsByTag 根据标签过滤客户ID
func (l *Logic) FilterCustomerIDsByTag(
	ctx context.Context, req *customerpb.FilterCustomerIdsByTagRequest,
) ([]int64, error) {
	scope := req.GetScope()
	if scope == nil || scope.GetCompanyId() <= 0 {
		return nil, nil
	}

	if len(req.GetTagIds()) == 0 {
		return nil, nil
	}

	// 转换操作符
	var operator string
	switch req.GetOperator() {
	case customerpb.InNotInOperator_IN:
		operator = operatorIN
	case customerpb.InNotInOperator_NOT_IN:
		operator = operatorNOTIN
	default:
		return nil, nil
	}

	return l.customerRepo.FilterCustomerIDsByTag(
		ctx,
		scope.GetCompanyId(),
		scope.GetBusinessIds(),
		scope.GetCustomerIds(),
		req.GetTagIds(),
		operator,
	)
}
