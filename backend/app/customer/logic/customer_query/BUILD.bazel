load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "customer_query",
    srcs = ["customer_query.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/customer/logic/customer_query",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/customer/repo/postgres/address",
        "//backend/app/customer/repo/postgres/contact",
        "//backend/app/customer/repo/postgres/customer",
        "//backend/proto/customer/v1:customer",
        "//backend/proto/customer/v2:customer",
    ],
)
