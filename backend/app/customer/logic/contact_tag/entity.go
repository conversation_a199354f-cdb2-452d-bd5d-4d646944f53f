package contacttag

import (
	"encoding/base64"
	"encoding/json"
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres"
	contacttagrepo "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/contact_tag"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

type ContactTag struct {
	ID               int64                           `gorm:"primaryKey;column:id"`
	OrganizationType customerpb.OrganizationRef_Type `gorm:"column:organization_type;serializer:proto_enum"`
	OrganizationID   int64                           `gorm:"column:organization_id;not null"`
	Name             string                          `gorm:"column:name;type:varchar(255);not null"`
	Color            string                          `gorm:"column:color;type:varchar(7);not null"`
	SortOrder        int32                           `gorm:"column:sort_order;not null;default:0"`
	Description      string                          `gorm:"column:description;type:text"`
	State            customerpb.ContactTag_State     `gorm:"column:state;serializer:proto_enum"`
	Type             customerpb.ContactTag_Type      `gorm:"column:type;serializer:proto_enum"`
	CreatedTime      time.Time                       `gorm:"column:created_time"`
	UpdatedTime      time.Time                       `gorm:"column:updated_time"`
	DeletedTime      *time.Time                      `gorm:"column:deleted_time"`
}

func (c *ContactTag) Load(dbTag *contacttagrepo.ContactTag) *ContactTag {
	return &ContactTag{
		ID:               dbTag.ID,
		OrganizationType: dbTag.OrganizationType,
		OrganizationID:   dbTag.OrganizationID,
		Name:             dbTag.Name,
		Color:            dbTag.Color,
		SortOrder:        dbTag.SortOrder,
		Description:      dbTag.Description,
		State:            dbTag.State,
		Type:             dbTag.Type,
		CreatedTime:      dbTag.CreatedTime,
		UpdatedTime:      dbTag.UpdatedTime,
		DeletedTime:      dbTag.DeletedTime,
	}
}

func (c *ContactTag) ToDB() *contacttagrepo.ContactTag {
	return &contacttagrepo.ContactTag{
		ID:               c.ID,
		OrganizationType: c.OrganizationType,
		OrganizationID:   c.OrganizationID,
		Name:             c.Name,
		Color:            c.Color,
		SortOrder:        c.SortOrder,
		Description:      c.Description,
		State:            c.State,
		Type:             c.Type,
		CreatedTime:      c.CreatedTime,
		UpdatedTime:      c.UpdatedTime,
		DeletedTime:      c.DeletedTime,
	}
}

func (c *ContactTag) ToPB() *customerpb.ContactTag {
	if c == nil {
		return nil
	}
	contactTag := &customerpb.ContactTag{
		Id:          c.ID,
		Name:        c.Name,
		Color:       c.Color,
		SortOrder:   c.SortOrder,
		State:       c.State,
		Description: c.Description,
		Type:        c.Type,
		Organization: &customerpb.OrganizationRef{
			Type: c.OrganizationType,
			Id:   c.OrganizationID,
		},
		CreateTime: &timestamppb.Timestamp{
			Seconds: c.CreatedTime.Unix(),
			Nanos:   int32(c.CreatedTime.Nanosecond()),
		},
		UpdateTime: &timestamppb.Timestamp{
			Seconds: c.UpdatedTime.Unix(),
			Nanos:   int32(c.UpdatedTime.Nanosecond()),
		},
	}

	if c.DeletedTime != nil {
		contactTag.DeleteTime = &timestamppb.Timestamp{
			Seconds: c.DeletedTime.Unix(),
			Nanos:   int32(c.DeletedTime.Nanosecond()),
		}
	}

	return contactTag
}

// ListContactTagsRequest
type ListContactTagsRequest struct {
	Filter     *ListContactTagsFilter     `json:"filter"`
	OrderBy    *ListContactTagsOrderBy    `json:"order_by"`
	Pagination *ListContactTagsPagination `json:"pagination"`
}

// ListContactTagsFilter
type ListContactTagsFilter struct {
	IDs              []int64                         `json:"ids"`
	OrganizationType customerpb.OrganizationRef_Type `json:"organization_type"`
	OrganizationID   int64                           `json:"organization_id"`
	States           []customerpb.ContactTag_State   `json:"states"`
	Name             string                          `json:"name"`
}

// ListContactTagsOrderBy
type ListContactTagsOrderBy struct {
	Field     customerpb.ListContactTagsRequest_Sorting_Field     `json:"field"`
	Direction customerpb.ListContactTagsRequest_Sorting_Direction `json:"direction"`
}

// ListContactTagsPagination
type ListContactTagsPagination struct {
	PageSize        int32  `json:"page_size"`
	Cursor          string `json:"cursor"`
	ReturnTotalSize bool   `json:"return_total_size"`
}

func (p *ListContactTagsPagination) DecodeCursor() *postgres.Cursor {
	if p.Cursor == "" {
		return nil
	}
	bytes, err := base64.StdEncoding.DecodeString(p.Cursor)
	if err != nil {
		return nil
	}
	cursor := &postgres.Cursor{}
	_ = json.Unmarshal(bytes, cursor)

	return cursor
}

type ListContactTagsResponse struct {
	ContactTags []*ContactTag `json:"contact_tags"`
	HasNext     bool          `json:"has_next"`
	NextToken   string        `json:"next_token"`
	TotalSize   *int64        `json:"total_size"`
}

// UpdateContactTagRequest
type UpdateContactTagRequest struct {
	ID          int64                       `json:"id"`
	Name        string                      `json:"name"`
	Description string                      `json:"description"`
	Color       string                      `json:"color"`
	SortOrder   int32                       `json:"sort_order"`
	State       customerpb.ContactTag_State `json:"state"`
}
