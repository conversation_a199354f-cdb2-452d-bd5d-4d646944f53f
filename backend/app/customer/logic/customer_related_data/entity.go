package customerrelateddata

import (
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	customerrelateddatarepo "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/customer_related_data"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

type CustomerRelatedData struct {
	ID                     int64                                `json:"id"`
	CustomerID             int64                                `json:"customer_id"`
	PreferredBusinessID    int64                                `json:"preferred_business_id"`
	CompanyID              int64                                `json:"company_id"`
	ClientColor            string                               `json:"client_color"`
	IsBlockMessage         int32                                `json:"is_block_message"`
	IsBlockOnlineBooking   int32                                `json:"is_block_online_booking"`
	LoginEmail             string                               `json:"login_email"`
	ReferralSourceID       int32                                `json:"referral_source_id"`
	ReferralSourceDesc     string                               `json:"referral_source_desc"`
	SendAutoEmail          int32                                `json:"send_auto_email"`
	SendAutoMessage        int32                                `json:"send_auto_message"`
	SendAppAutoMessage     int32                                `json:"send_app_auto_message"`
	UnconfirmedReminderBy  []byte                               `json:"unconfirmed_reminder_by"`
	PreferredGroomerID     int32                                `json:"preferred_groomer_id"`
	PreferredFrequencyDay  int32                                `json:"preferred_frequency_day"`
	PreferredFrequencyType int32                                `json:"preferred_frequency_type"`
	LastServiceTime        string                               `json:"last_service_time"`
	Source                 string                               `json:"source"`
	ExternalID             string                               `json:"external_id"`
	CreateBy               int32                                `json:"create_by"`
	UpdateBy               int32                                `json:"update_by"`
	IsRecurring            *int32                               `json:"is_recurring"`
	ShareApptStatus        int32                                `json:"share_appt_status"`
	ShareRangeType         int32                                `json:"share_range_type"`
	ShareRangeValue        int32                                `json:"share_range_value"`
	ShareApptJSON          *string                              `json:"share_appt_json"`
	PreferredDay           string                               `json:"preferred_day"`
	PreferredTime          string                               `json:"preferred_time"`
	AccountID              int64                                `json:"account_id"`
	CustomerCode           string                               `json:"customer_code"`
	IsUnsubscribed         bool                                 `json:"is_unsubscribed"`
	Birthday               *time.Time                           `json:"birthday"`
	ActionState            string                               `json:"action_state"`
	CustomizeLifeCycleID   int64                                `json:"customize_life_cycle_id"`
	CustomizeActionStateID int64                                `json:"customize_action_state_id"`
	State                  customerpb.CustomerRelatedData_State `json:"state"`
	DeletedTime            *time.Time                           `json:"deleted_time"`
	CreatedTime            time.Time                            `json:"created_time"`
	UpdatedTime            time.Time                            `json:"updated_time"`

	PreferredTipEnable     int32   `json:"preferred_tip_enable"`
	PreferredTipType       int32   `json:"preferred_tip_type"`
	PreferredTipAmount     float64 `json:"preferred_tip_amount"`
	PreferredTipPercentage int32   `json:"preferred_tip_percentage"`

	DefaultPreferredFrequencyType  int32 `json:"default_preferred_frequency_type"`
	DefaultPreferredCalendarPeriod int32 `json:"default_preferred_calendar_period"`
	DefaultPreferredFrequencyValue int32 `json:"default_preferred_frequency_value"`
}

func GetApptReminderListForDB(apptReminderByList []byte) *byte {
	var originByte byte
	for _, choice := range apptReminderByList {
		switch choice {
		case ApptReminderByMsg:
			originByte = originByte | ByMsg
		case ApptReminderByEmail:
			originByte = originByte | ByEmail
		case ApptReminderByCall:
			originByte = originByte | ByCall
		case ApptReminderByApp:
			originByte = originByte | ByApp
		default:
			// 在 Go 中我们可以使用 log 包或者返回 error，这里先忽略未知类型
		}
	}

	return &originByte
}

func (c *CustomerRelatedData) ToDB() *customerrelateddatarepo.CustomerRelatedData {
	var unconfirmedReminderBy byte
	if len(c.UnconfirmedReminderBy) > 0 {
		unconfirmedReminderByPtr := GetApptReminderListForDB(c.UnconfirmedReminderBy)
		if unconfirmedReminderByPtr != nil {
			unconfirmedReminderBy = *unconfirmedReminderByPtr
		}
	}

	return &customerrelateddatarepo.CustomerRelatedData{
		ID:                     c.ID,
		CustomerID:             c.CustomerID,
		BusinessID:             c.PreferredBusinessID,
		CompanyID:              c.CompanyID,
		ClientColor:            c.ClientColor,
		IsBlockMessage:         c.IsBlockMessage,
		IsBlockOnlineBooking:   c.IsBlockOnlineBooking,
		LoginEmail:             c.LoginEmail,
		ReferralSourceID:       c.ReferralSourceID,
		ReferralSourceDesc:     c.ReferralSourceDesc,
		SendAutoEmail:          c.SendAutoEmail,
		SendAutoMessage:        c.SendAutoMessage,
		SendAppAutoMessage:     c.SendAppAutoMessage,
		UnconfirmedReminderBy:  unconfirmedReminderBy,
		PreferredGroomerID:     c.PreferredGroomerID,
		PreferredFrequencyDay:  c.PreferredFrequencyDay,
		PreferredFrequencyType: c.PreferredFrequencyType,
		LastServiceTime:        c.LastServiceTime,
		Source:                 c.Source,
		ExternalID:             c.ExternalID,
		CreateBy:               c.CreateBy,
		UpdateBy:               c.UpdateBy,
		IsRecurring:            c.IsRecurring,
		ShareApptStatus:        c.ShareApptStatus,
		ShareRangeType:         c.ShareRangeType,
		ShareRangeValue:        c.ShareRangeValue,
		ShareApptJSON:          c.ShareApptJSON,
		PreferredDay:           c.PreferredDay,
		PreferredTime:          c.PreferredTime,
		AccountID:              c.AccountID,
		CustomerCode:           c.CustomerCode,
		IsUnsubscribed:         c.IsUnsubscribed,
		Birthday:               c.Birthday,
		ActionState:            c.ActionState,
		CustomizeLifeCycleID:   c.CustomizeLifeCycleID,
		CustomizeActionStateID: c.CustomizeActionStateID,
		State:                  c.State,
		DeletedTime:            c.DeletedTime,
		CreatedTime:            c.CreatedTime,
		UpdatedTime:            c.UpdatedTime,
	}
}

func (c *CustomerRelatedData) ToPB() *customerpb.CustomerRelatedData {
	if c == nil {
		return nil
	}

	unconfirmedReminderBy := make([]int32, len(c.UnconfirmedReminderBy))
	for i, v := range c.UnconfirmedReminderBy {
		unconfirmedReminderBy[i] = int32(v)
	}

	result := &customerpb.CustomerRelatedData{
		Id:                     c.ID,
		CustomerId:             c.CustomerID,
		PreferredBusinessId:    c.PreferredBusinessID,
		CompanyId:              c.CompanyID,
		ClientColor:            c.ClientColor,
		IsBlockMessage:         c.IsBlockMessage,
		IsBlockOnlineBooking:   c.IsBlockOnlineBooking,
		LoginEmail:             c.LoginEmail,
		ReferralSourceId:       c.ReferralSourceID,
		ReferralSourceDesc:     c.ReferralSourceDesc,
		SendAutoEmail:          c.SendAutoEmail,
		SendAutoMessage:        c.SendAutoMessage,
		SendAppAutoMessage:     c.SendAppAutoMessage,
		UnconfirmedReminderBy:  unconfirmedReminderBy,
		PreferredGroomerId:     c.PreferredGroomerID,
		PreferredFrequencyDay:  c.PreferredFrequencyDay,
		PreferredFrequencyType: c.PreferredFrequencyType,
		LastServiceTime:        c.LastServiceTime,
		Source:                 c.Source,
		ExternalId:             c.ExternalID,
		CreateBy:               c.CreateBy,
		UpdateBy:               c.UpdateBy,
		ShareApptStatus:        c.ShareApptStatus,
		ShareRangeType:         c.ShareRangeType,
		ShareRangeValue:        c.ShareRangeValue,
		PreferredDay:           c.PreferredDay,
		PreferredTime:          c.PreferredTime,
		AccountId:              c.AccountID,
		CustomerCode:           c.CustomerCode,
		IsUnsubscribed:         c.IsUnsubscribed,
		ActionState:            c.ActionState,
		CustomizeLifeCycleId:   c.CustomizeLifeCycleID,
		CustomizeActionStateId: c.CustomizeActionStateID,
		State:                  c.State,
		CreateTime: &timestamppb.Timestamp{
			Seconds: c.CreatedTime.Unix(),
			Nanos:   int32(c.CreatedTime.Nanosecond()),
		},
		UpdateTime: &timestamppb.Timestamp{
			Seconds: c.UpdatedTime.Unix(),
			Nanos:   int32(c.UpdatedTime.Nanosecond()),
		},
	}

	if c.IsRecurring != nil {
		result.IsRecurring = c.IsRecurring
	}

	if c.ShareApptJSON != nil {
		result.ShareApptJson = c.ShareApptJSON
	}

	if c.Birthday != nil {
		result.Birthday = &timestamppb.Timestamp{
			Seconds: c.Birthday.Unix(),
			Nanos:   int32(c.Birthday.Nanosecond()),
		}
	}

	if c.DeletedTime != nil {
		result.DeleteTime = &timestamppb.Timestamp{
			Seconds: c.DeletedTime.Unix(),
			Nanos:   int32(c.DeletedTime.Nanosecond()),
		}
	}

	result.PreferredTipEnable = c.PreferredTipEnable
	result.PreferredTipType = c.PreferredTipType
	result.PreferredTipAmount = c.PreferredTipAmount
	result.PreferredTipPercentage = c.PreferredTipPercentage

	result.DefaultPreferredFrequencyType = c.DefaultPreferredFrequencyType
	result.DefaultPreferredCalendarPeriod = c.DefaultPreferredCalendarPeriod
	result.DefaultPreferredFrequencyValue = c.DefaultPreferredFrequencyValue

	return result
}

// ************ ListCustomerRelatedData ************
type ListCustomerRelatedDataRequest struct {
	Filter     *ListCustomerRelatedDataFilter     `json:"filter"`
	Pagination *ListCustomerRelatedDataPagination `json:"pagination"`
	OrderBy    *ListCustomerRelatedDataOrderBy    `json:"order_by"`
}

type ListCustomerRelatedDataPagination struct {
	PageSize        int32  `json:"page_size"`
	Cursor          string `json:"cursor"`
	ReturnTotalSize bool   `json:"return_total_size"`
}

type ListCustomerRelatedDataOrderBy struct {
	Field     customerpb.ListCustomerRelatedDataRequest_Sorting_Field     `json:"field"`
	Direction customerpb.ListCustomerRelatedDataRequest_Sorting_Direction `json:"direction"`
}

type ListCustomerRelatedDataFilter struct {
	IDs           []int64                                `json:"ids"`
	CustomerIDs   []int64                                `json:"customer_ids"`
	BusinessIDs   []int32                                `json:"business_ids"`
	CompanyIDs    []int64                                `json:"company_ids"`
	States        []customerpb.CustomerRelatedData_State `json:"states"`
	CustomerCodes []string
	AccountIDs    []int64
}

type ListCustomerRelatedDataResponse struct {
	CustomerRelatedData []*CustomerRelatedData `json:"customer_related_data"`
	HasNext             bool                   `json:"has_next"`
	NextToken           string                 `json:"next_token"`
	TotalSize           *int64                 `json:"total_size"`
}

// ************ UpdateCustomerRelatedData ************
type UpdateCustomerRelatedDataRequest struct {
	ID                     int64      `json:"id"`
	ClientColor            string     `json:"client_color"`
	IsBlockMessage         int32      `json:"is_block_message"`
	IsBlockOnlineBooking   int32      `json:"is_block_online_booking"`
	LoginEmail             string     `json:"login_email"`
	ReferralSourceID       int32      `json:"referral_source_id"`
	ReferralSourceDesc     string     `json:"referral_source_desc"`
	SendAutoEmail          int32      `json:"send_auto_email"`
	SendAutoMessage        int32      `json:"send_auto_message"`
	SendAppAutoMessage     int32      `json:"send_app_auto_message"`
	PreferredGroomerID     int32      `json:"preferred_groomer_id"`
	PreferredFrequencyDay  int32      `json:"preferred_frequency_day"`
	PreferredFrequencyType int32      `json:"preferred_frequency_type"`
	PreferredDay           string     `json:"preferred_day"`
	PreferredTime          string     `json:"preferred_time"`
	IsUnsubscribed         bool       `json:"is_unsubscribed"`
	Birthday               *time.Time `json:"birthday"`
	CustomizeLifeCycleID   int64      `json:"customize_life_cycle_id"`
	CustomizeActionStateID int64      `json:"customize_action_state_id"`
	PreferredBusinessID    int64      `json:"preferred_business_id"`
	UnconfirmedReminderBy  []byte     `json:"unconfirmed_reminder_by"`

	PreferredTipEnable     int32   `json:"preferred_tip_enable"`
	PreferredTipType       int32   `json:"preferred_tip_type"`
	PreferredTipAmount     float64 `json:"preferred_tip_amount"`
	PreferredTipPercentage int32   `json:"preferred_tip_percentage"`

	DefaultPreferredFrequencyType  int32 `json:"default_preferred_frequency_type"`
	DefaultPreferredCalendarPeriod int32 `json:"default_preferred_calendar_period"`
	DefaultPreferredFrequencyValue int32 `json:"default_preferred_frequency_value"`
}

func convertTimestampToTime(ts *timestamppb.Timestamp) *time.Time {
	if ts == nil {
		return nil
	}
	t := ts.AsTime()

	return &t
}

func (c *CustomerRelatedData) FromProto(pb *customerpb.CustomerRelatedData) {
	if pb == nil {
		return
	}
	c.ID = pb.Id
	c.CustomerID = pb.CustomerId
	c.PreferredBusinessID = pb.PreferredBusinessId
	c.CompanyID = pb.CompanyId
	c.ClientColor = pb.ClientColor
	c.IsBlockMessage = pb.IsBlockMessage
	c.IsBlockOnlineBooking = pb.IsBlockOnlineBooking
	c.LoginEmail = pb.LoginEmail
	c.ReferralSourceID = pb.ReferralSourceId
	c.ReferralSourceDesc = pb.ReferralSourceDesc
	c.SendAutoEmail = pb.SendAutoEmail
	c.SendAutoMessage = pb.SendAutoMessage
	c.SendAppAutoMessage = pb.SendAppAutoMessage
	c.UnconfirmedReminderBy = make([]byte, len(pb.UnconfirmedReminderBy))
	for i, v := range pb.UnconfirmedReminderBy {
		c.UnconfirmedReminderBy[i] = byte(v)
	}
	c.PreferredGroomerID = pb.PreferredGroomerId
	c.PreferredFrequencyDay = pb.PreferredFrequencyDay
	c.PreferredFrequencyType = pb.PreferredFrequencyType
	c.LastServiceTime = pb.LastServiceTime
	c.Source = pb.Source
	c.ExternalID = pb.ExternalId
	c.CreateBy = pb.CreateBy
	c.UpdateBy = pb.UpdateBy
	c.ShareApptStatus = pb.ShareApptStatus
	c.ShareRangeType = pb.ShareRangeType
	c.ShareRangeValue = pb.ShareRangeValue
	c.PreferredDay = pb.PreferredDay
	c.PreferredTime = pb.PreferredTime
	c.AccountID = pb.AccountId
	c.CustomerCode = pb.CustomerCode
	c.IsUnsubscribed = pb.IsUnsubscribed
	c.ActionState = pb.ActionState
	c.CustomizeLifeCycleID = pb.CustomizeLifeCycleId
	c.CustomizeActionStateID = pb.CustomizeActionStateId
	c.Birthday = convertTimestampToTime(pb.Birthday)

	if pb.IsRecurring != nil {
		c.IsRecurring = pb.IsRecurring
	}

	if pb.ShareApptJson != nil {
		c.ShareApptJSON = pb.ShareApptJson
	}

	c.PreferredTipEnable = pb.PreferredTipEnable
	c.PreferredTipType = pb.PreferredTipType
	c.PreferredTipAmount = pb.PreferredTipAmount
	c.PreferredTipPercentage = pb.PreferredTipPercentage

	c.DefaultPreferredFrequencyType = pb.DefaultPreferredFrequencyType
	c.DefaultPreferredCalendarPeriod = pb.DefaultPreferredCalendarPeriod
	c.DefaultPreferredFrequencyValue = pb.DefaultPreferredFrequencyValue
}
