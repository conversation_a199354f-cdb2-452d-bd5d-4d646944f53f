package lead

import (
	"encoding/base64"
	"encoding/json"
	"time"

	"google.golang.org/protobuf/types/known/structpb"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres"
	customerrepo "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/customer"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

type Lead struct {
	ID                  int64                           `json:"id"`
	OrganizationType    customerpb.OrganizationRef_Type `json:"organization_type"`
	OrganizationID      int64                           `json:"organization_id"`
	GivenName           string                          `json:"given_name"`
	FamilyName          string                          `json:"family_name"`
	CustomerType        customerpb.CustomerType         `json:"customer_type"`
	State               customerpb.Lead_State           `json:"state"`
	CustomFields        *structpb.Struct                `json:"custom_fields"`
	LifeCycleID         int64                           `json:"life_cycle_id"`
	OwnerStaffID        int64                           `json:"owner_staff_id"`
	ActionStateID       int64                           `json:"action_state_id"`
	AvatarPath          string                          `json:"avatar_path"`
	CustomerCode        string                          `json:"customer_code"`
	ReferralSourceID    int64                           `json:"referral_source_id"`
	ConvertToCustomerID int64                           `json:"convert_to_customer_id"`
	DeletedTime         *time.Time                      `json:"deleted_time"`
	CreatedTime         time.Time                       `json:"created_time"`
	UpdatedTime         time.Time                       `json:"updated_time"`
}

func (c *Lead) ToDB() *customerrepo.Customer {
	customerState := customerpb.Customer_State(c.State)

	return &customerrepo.Customer{
		ID:               c.ID,
		OrganizationType: c.OrganizationType,
		OrganizationID:   c.OrganizationID,
		GivenName:        c.GivenName,
		FamilyName:       c.FamilyName,
		CustomerType:     c.CustomerType,
		State:            customerState,
		CustomFields:     c.CustomFields,
		LifeCycleID:      c.LifeCycleID,
		OwnerStaffID:     c.OwnerStaffID,
		DeletedTime:      c.DeletedTime,
		CreatedTime:      c.CreatedTime,
		UpdatedTime:      c.UpdatedTime,
		CustomerCode:     c.CustomerCode,
	}
}

func (c *Lead) ToPB() *customerpb.Lead {
	if c == nil {
		return nil
	}

	lead := &customerpb.Lead{
		Id: c.ID,
		Organization: &customerpb.OrganizationRef{
			Type: c.OrganizationType,
			Id:   c.OrganizationID,
		},
		GivenName:           c.GivenName,
		FamilyName:          c.FamilyName,
		State:               c.State,
		CustomFields:        c.CustomFields,
		LifecycleId:         c.LifeCycleID,
		OwnerStaffId:        c.OwnerStaffID,
		ActionStateId:       c.ActionStateID,
		AvatarPath:          c.AvatarPath,
		CustomerCode:        c.CustomerCode,
		ReferralSourceId:    c.ReferralSourceID,
		ConvertedCustomerId: &c.ConvertToCustomerID,
		CreateTime: &timestamppb.Timestamp{
			Seconds: c.CreatedTime.Unix(),
			Nanos:   int32(c.CreatedTime.Nanosecond()),
		},
		UpdateTime: &timestamppb.Timestamp{
			Seconds: c.UpdatedTime.Unix(),
			Nanos:   int32(c.UpdatedTime.Nanosecond()),
		},
	}
	if c.DeletedTime != nil {
		lead.DeleteTime = &timestamppb.Timestamp{
			Seconds: c.DeletedTime.Unix(),
			Nanos:   int32(c.DeletedTime.Nanosecond()),
		}
	}

	return lead
}

// ************ ListLeads ************
type ListLeadsRequest struct {
	Filter     *ListLeadsFilter     `json:"filter"`
	Pagination *ListLeadsPagination `json:"pagination"`
	OrderBy    *ListLeadsOrderBy    `json:"order_by"`
}

type ListLeadsPagination struct {
	PageSize        int32  `json:"page_size"`
	Cursor          string `json:"cursor"`
	ReturnTotalSize bool   `json:"return_total_size"`
}

func (p *ListLeadsPagination) DecodeCursor() *postgres.Cursor {
	if p.Cursor == "" {
		return nil
	}
	bytes, err := base64.StdEncoding.DecodeString(p.Cursor)
	if err != nil {
		return nil
	}
	cursor := &postgres.Cursor{}
	_ = json.Unmarshal(bytes, cursor)

	return cursor
}

type ListLeadsOrderBy struct {
	Field     customerpb.ListLeadsRequest_Sorting_Field     `json:"field"`
	Direction customerpb.ListLeadsRequest_Sorting_Direction `json:"direction"`
}

type ListLeadsFilter struct {
	IDs                  []int64                          `json:"ids"`
	OrganizationType     *customerpb.OrganizationRef_Type `json:"organization_type"`
	OrganizationID       int64                            `json:"organization_id"`
	CustomerType         customerpb.CustomerType          `json:"customer_type"`
	States               []customerpb.Lead_State          `json:"states"`
	OwnerStaffIDs        []int64                          `json:"owner_staff_ids"`
	LifecycleIDs         []int64                          `json:"lifecycle_ids"`
	ConvertedCustomerIDs []int64
	IsConverted          *bool
}

type ListLeadsResponse struct {
	Leads     []*Lead `json:"leads"`
	HasNext   bool    `json:"has_next"`
	NextToken string  `json:"next_token"`
	TotalSize *int64  `json:"total_size"`
}

// ************ UpdateLead ************
type UpdateLeadRequest struct {
	ID           int64                 `json:"id"`
	GivenName    string                `json:"given_name"`
	FamilyName   string                `json:"family_name"`
	CustomFields *structpb.Struct      `json:"custom_fields"`
	LifeCycleID  int64                 `json:"life_cycle_id"`
	OwnerStaffID int64                 `json:"owner_staff_id"`
	State        customerpb.Lead_State `json:"state"`
	AvatarPath   string                `json:"avatar_path"`
}
