load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "address",
    srcs = [
        "address.go",
        "entity.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/customer/logic/address",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/customer/repo/postgres",
        "//backend/app/customer/repo/postgres/address",
        "//backend/app/customer/repo/redis",
        "//backend/common/rpc/framework/errs",
        "//backend/common/rpc/framework/log",
        "//backend/proto/customer/v2:customer",
        "@io_gorm_gorm//:gorm",
        "@org_golang_google_genproto//googleapis/type/latlng",
        "@org_golang_google_genproto//googleapis/type/postaladdress",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_protobuf//types/known/structpb",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)

go_test(
    name = "address_test",
    srcs = ["address_test.go"],
    deps = [
        ":address",
        "//backend/app/customer/repo/postgres",
        "//backend/app/customer/repo/postgres/address",
        "//backend/app/customer/repo/postgres/address/mock",
        "//backend/proto/customer/v2:customer",
        "@com_github_stretchr_testify//require",
        "@io_gorm_gorm//:gorm",
        "@org_uber_go_mock//gomock",
    ],
)
