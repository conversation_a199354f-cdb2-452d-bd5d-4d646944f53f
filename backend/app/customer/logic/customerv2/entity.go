package customer

import (
	"time"

	"google.golang.org/protobuf/types/known/structpb"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/MoeGolibrary/moego/backend/app/customer/logic/address"
	"github.com/MoeGolibrary/moego/backend/app/customer/logic/contact"
	customerrelateddata "github.com/MoeGolibrary/moego/backend/app/customer/logic/customer_related_data"
	customerrepo "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/customer"
	"github.com/MoeGolibrary/moego/backend/common/utils/random"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

type Customer struct {
	ID               int64                           `json:"id"`
	OrganizationType customerpb.OrganizationRef_Type `json:"organization_type"`
	OrganizationID   int64                           `json:"organization_id"`
	GivenName        string                          `json:"given_name"`
	FamilyName       string                          `json:"family_name"`
	CustomerType     customerpb.CustomerType         `json:"customer_type"`
	State            customerpb.Customer_State       `json:"state"`
	CustomFields     *structpb.Struct                `json:"custom_fields"`
	LifeCycleID      int64                           `json:"life_cycle_id"`
	OwnerStaffID     int64                           `json:"owner_staff_id"`
	ActionStateID    int64                           `json:"action_state_id"`
	AvatarPath       string                          `json:"avatar_path"`
	CustomerCode     string                          `json:"customer_code"`
	ReferralSourceID int64                           `json:"referral_source_id"`
	CreatedTime      time.Time                       `json:"created_time"`
	UpdatedTime      time.Time                       `json:"updated_time"`
	DeletedTime      *time.Time                      `json:"deleted_time"`
}

func (c *Customer) ToDB() *customerrepo.Customer {
	return &customerrepo.Customer{
		ID:               c.ID,
		OrganizationType: c.OrganizationType,
		OrganizationID:   c.OrganizationID,
		GivenName:        c.GivenName,
		FamilyName:       c.FamilyName,
		CustomerType:     c.CustomerType,
		State:            c.State,
		CustomFields:     c.CustomFields,
		LifeCycleID:      c.LifeCycleID,
		OwnerStaffID:     c.OwnerStaffID,
		ActionStateID:    c.ActionStateID,
		AvatarPath:       c.AvatarPath,
		CustomerCode:     c.CustomerCode,
		ReferralSourceID: c.ReferralSourceID,
		CreatedTime:      c.CreatedTime,
		UpdatedTime:      c.UpdatedTime,
		DeletedTime:      c.DeletedTime,
	}
}

func (c *Customer) ToPB() *customerpb.Customer {
	if c == nil {
		return nil
	}
	customer := &customerpb.Customer{
		Id: c.ID,
		Organization: &customerpb.OrganizationRef{
			Type: c.OrganizationType,
			Id:   c.OrganizationID,
		},
		GivenName:        c.GivenName,
		FamilyName:       c.FamilyName,
		State:            c.State,
		CustomFields:     c.CustomFields,
		LifecycleId:      c.LifeCycleID,
		OwnerStaffId:     c.OwnerStaffID,
		ActionStateId:    c.ActionStateID,
		AvatarPath:       c.AvatarPath,
		CustomerCode:     c.CustomerCode,
		ReferralSourceId: c.ReferralSourceID,
		CreateTime:       timestamppb.New(c.CreatedTime),
		UpdateTime:       timestamppb.New(c.UpdatedTime),
	}
	if c.DeletedTime != nil {
		customer.DeleteTime = timestamppb.New(*c.DeletedTime)
	}

	return customer
}

// ************ ListCustomers ************
type ListCustomersRequest struct {
	Filter     *ListCustomersFilter     `json:"filter"`
	Pagination *ListCustomersPagination `json:"pagination"`
	OrderBy    *ListCustomersOrderBy    `json:"order_by"`
}

type ListCustomersPagination struct {
	PageSize        int32  `json:"page_size"`
	Cursor          string `json:"cursor"`
	ReturnTotalSize bool   `json:"return_total_size"`
}

type ListCustomersOrderBy struct {
	Field     customerpb.ListCustomersRequest_Sorting_Field     `json:"field"`
	Direction customerpb.ListCustomersRequest_Sorting_Direction `json:"direction"`
}

type ListCustomersFilter struct {
	IDs               []int64                          `json:"ids"`
	OrganizationType  *customerpb.OrganizationRef_Type `json:"organization_type"`
	OrganizationID    int64                            `json:"organization_id"`
	CustomerType      customerpb.CustomerType          `json:"customer_type"`
	States            []customerpb.Customer_State      `json:"states"`
	ReferralSourceIDs []int64                          `json:"referral_source_ids"`
	OwnerStaffIDs     []int64                          `json:"owner_staff_ids"`
	LifecycleIDs      []int64                          `json:"lifecycle_ids"`
	ActionStateIDs    []int64                          `json:"action_state_ids"`
	CustomerCodes     []string                         `json:"customer_codes"`
}

type ListCustomersResponse struct {
	Customers []*Customer `json:"customers"`
	HasNext   bool        `json:"has_next"`
	NextToken string      `json:"next_token"`
	TotalSize *int64      `json:"total_size"`
}

// ************ UpdateCustomer ************
type UpdateCustomerRequest struct {
	ID               int64                     `json:"id"`
	GivenName        string                    `json:"given_name"`
	FamilyName       string                    `json:"family_name"`
	CustomFields     *structpb.Struct          `json:"custom_fields"`
	LifeCycleID      int64                     `json:"life_cycle_id"`
	OwnerStaffID     int64                     `json:"owner_staff_id"`
	ActionStateID    int64                     `json:"action_state_id"`
	AvatarPath       string                    `json:"avatar_path"`
	CustomerCode     string                    `json:"customer_code"`
	ReferralSourceID int64                     `json:"referral_source_id"`
	State            customerpb.Customer_State `json:"state"`
}

type AggregateRequest struct {
	Customer    *Customer
	Addresses   []*address.Address
	Contacts    []*contact.Contact
	RelatedData *customerrelateddata.CustomerRelatedData
}

type AggregateResponse struct {
	Customer    *Customer
	Addresses   []*address.Address
	Contacts    []*contact.Contact
	RelatedData *customerrelateddata.CustomerRelatedData
}

func (c *Customer) FromProto(pb *customerpb.Customer) {
	c.ID = pb.GetId()
	c.GivenName = pb.GetGivenName()
	c.FamilyName = pb.GetFamilyName()
	c.LifeCycleID = pb.GetLifecycleId()
	c.OwnerStaffID = pb.GetOwnerStaffId()
	c.ActionStateID = pb.GetActionStateId()
	c.AvatarPath = pb.GetAvatarPath()
	c.ReferralSourceID = pb.GetReferralSourceId()
	c.CustomerCode = random.String(16)
	c.CustomFields = pb.GetCustomFields()
	c.OrganizationType = pb.GetOrganization().GetType()
	c.OrganizationID = pb.GetOrganization().GetId()
}

type ConvertCustomersAttributeDatum struct {
	CustomerIDs            []int64
	CustomizeLifeCycleID   *int64
	CustomizeActionStateID *int64
}
