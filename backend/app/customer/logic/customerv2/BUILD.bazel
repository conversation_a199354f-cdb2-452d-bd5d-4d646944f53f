load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "customerv2",
    srcs = [
        "customer.go",
        "entity.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/customer/logic/customerv2",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/customer/logic/address",
        "//backend/app/customer/logic/contact",
        "//backend/app/customer/logic/customer_related_data",
        "//backend/app/customer/repo/db",
        "//backend/app/customer/repo/postgres",
        "//backend/app/customer/repo/postgres/address",
        "//backend/app/customer/repo/postgres/contact",
        "//backend/app/customer/repo/postgres/customer",
        "//backend/app/customer/repo/postgres/customer_related_data",
        "//backend/app/customer/utils",
        "//backend/common/rpc/framework/errs",
        "//backend/common/rpc/framework/log",
        "//backend/common/utils/random",
        "//backend/proto/customer/v2:customer",
        "@io_gorm_gorm//:gorm",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
        "@org_golang_google_protobuf//types/known/structpb",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)

go_test(
    name = "customerv2_test",
    srcs = [
        "customer_aggregate_test.go",
        "customer_test.go",
    ],
    embed = [":customerv2"],
    deps = [
        "//backend/app/customer/logic/contact",
        "//backend/app/customer/repo/db/mock",
        "//backend/app/customer/repo/postgres/address/mock",
        "//backend/app/customer/repo/postgres/contact/mock",
        "//backend/app/customer/repo/postgres/contact_tag/mock",
        "//backend/app/customer/repo/postgres/contact_tag_rel/mock",
        "//backend/app/customer/repo/postgres/customer",
        "//backend/app/customer/repo/postgres/customer/mock",
        "//backend/app/customer/repo/postgres/customer_related_data",
        "//backend/app/customer/repo/postgres/customer_related_data/mock",
        "//backend/app/customer/utils",
        "//backend/proto/customer/v2:customer",
        "@com_github_stretchr_testify//require",
        "@io_gorm_gorm//:gorm",
        "@org_golang_google_protobuf//types/known/structpb",
        "@org_uber_go_mock//gomock",
    ],
)
