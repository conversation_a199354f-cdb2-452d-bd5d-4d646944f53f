package service

import (
	"context"

	"github.com/IBM/sarama"

	eventbuspb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/event_bus/v1"
	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	organizationpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	"github.com/MoeGolibrary/moego/backend/app/customer/logic/convert"
	"github.com/MoeGolibrary/moego/backend/app/customer/logic/customer"
	historylog "github.com/MoeGolibrary/moego/backend/app/customer/logic/history_log"
	"github.com/MoeGolibrary/moego/backend/app/customer/repo/growthbook"
	customerrepo "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/customer"
	customerutils "github.com/MoeGolibrary/moego/backend/app/customer/utils"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v1"
	customerpbv2 "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

type AppointmentEventConsumer struct {
	cl  *customer.Logic
	hll *historylog.Logic
	gb  growthbook.API

	// v2
	customerRepo customerrepo.Repository
	convertLogic *convert.Logic
}

func NewAppointmentEventConsumer() *AppointmentEventConsumer {
	return &AppointmentEventConsumer{
		cl:           customer.New(),
		hll:          historylog.New(),
		gb:           growthbook.New(),
		customerRepo: customerrepo.New(),
		convertLogic: convert.New(),
	}
}

func (c *AppointmentEventConsumer) AppointmentEventHandle(ctx context.Context, msg *sarama.ConsumerMessage) error {
	if msg == nil {
		return nil
	}
	log.InfoContextf(ctx, "AppointmentEventHandle get message, key:%s, value:%s", string(msg.Key), string(msg.Value))

	// parse msg
	eventData, _, err := parseEventData(ctx, msg)
	if err != nil {
		log.ErrorContextf(ctx, "AppointmentEventHandle parseEventData err, err: %v", err)

		return err
	}
	log.InfoContextf(ctx, "AppointmentEventHandle parseEventData, eventData: %+v", eventData)

	// check
	if !c.validateEventData(ctx, eventData) {
		return nil // Invalid data, stop processing
	}
	tenant := eventData.GetTenant()
	apptCreatedEvent := eventData.GetAppointmentCreatedEvent()

	// TODO: 针对k9的特殊逻辑 grooming服务(tour)不进行lead转换
	if c.shouldSkipK9Grooming(ctx, tenant, apptCreatedEvent) {
		return nil
	}

	//customerID, err := c.convertV1(ctx, tenant, apptCreatedEvent)
	//if err != nil {
	//	return err
	//}

	customerID, err := c.convertV2(ctx, apptCreatedEvent)
	if err != nil {
		return err
	}

	log.InfoContextf(ctx, "AppointmentEventHandle convert customer success, leadID:%d, customerID:%d",
		apptCreatedEvent.GetCustomerId(), customerID)

	return nil
}

//nolint:unused
func (c *AppointmentEventConsumer) convertV1(ctx context.Context, tenant *organizationpb.Tenant,
	apptCreatedEvent *eventbuspb.AppointmentCreatedEvent) (int64, error) {
	// check customer lead
	customerID := apptCreatedEvent.GetCustomerId()
	customerInfo, err := c.cl.Get(ctx, &customer.GetCustomerParams{ID: customerID})
	if err != nil {
		log.ErrorContextf(ctx, "AppointmentEventHandle get customer error, err: %v, customerID:%d", err, customerID)

		return 0, err
	}
	if customerInfo == nil || customerInfo.Type != customerpb.Customer_LEAD {
		log.InfoContextf(ctx, "AppointmentEventHandle get customer not found, customerID:%d", customerID)

		return 0, nil
	}

	// update business ID
	if customerInfo.PreferredBusinessID <= 0 {
		if err := c.cl.Update(ctx, &customer.Customer{
			ID:                  customerInfo.ID,
			PreferredBusinessID: tenant.GetBusinessId(),
		}); err != nil {
			log.ErrorContextf(ctx, "AppointmentEventHandle update customer error, err: %v, customerID:%d",
				err, customerID)

			return 0, err
		}
		customerInfo.PreferredBusinessID = tenant.GetBusinessId()
	}

	// convert customer
	if err := c.cl.ConvertCustomer(ctx, customerInfo.ID); err != nil {
		log.ErrorContextf(ctx, "AppointmentEventHandle convert customer error, err: %v, customerID:%d", err, customerID)

		return 0, err
	}

	// add convert history log
	if _, err := c.hll.Create(ctx, &historylog.CreateHistoryLogDatum{
		CustomerID:          customerInfo.ID,
		CustomerName:        customerutils.ConvCustomerName(customerInfo.GivenName, customerInfo.FamilyName),
		CustomerPhoneNumber: customerInfo.PhoneNumber,
		BusinessID:          customerInfo.PreferredBusinessID,
		CompanyID:           customerInfo.CompanyID,
		Action: &customerpb.HistoryLog_Action{
			Action: &customerpb.HistoryLog_Action_Convert{
				Convert: &customerpb.HistoryLog_Convert{
					OriginType: customerInfo.Type,
					TargetType: customerpb.Customer_CUSTOMER,
				},
			},
		},
		Source:   customerpb.HistoryLog_APPOINTMENT.Enum(),
		SourceID: &apptCreatedEvent.Id,
	}); err != nil {
		log.ErrorContextf(ctx, "AppointmentEventHandle Create log error, err: %v, customerID:%d", err, customerID)

		return 0, err
	}

	return customerID, nil
}

func (c *AppointmentEventConsumer) convertV2(ctx context.Context,
	apptCreatedEvent *eventbuspb.AppointmentCreatedEvent) (int64, error) {
	// check customer lead
	customerID := apptCreatedEvent.GetCustomerId()
	customerInfo, err := c.customerRepo.GetByID(ctx, customerID)
	if err != nil {
		log.ErrorContextf(ctx, "AppointmentEventHandle convertV2 get customer error, err: %v, customerID:%d", err,
			customerID)

		return 0, err
	}
	if customerInfo == nil ||
		customerInfo.CustomerType != customerpbv2.CustomerType_LEAD ||
		customerInfo.ConvertToCustomerID > 0 {
		log.InfoContextf(ctx,
			"AppointmentEventHandle convertV2 get customer not found or not lead or lead converted,customerID:%d",
			customerID)

		return 0, nil
	}

	// convert customer
	convertCustomerID, err := c.convertLogic.ConvertCustomer(ctx, &convert.Datum{
		ID:      customerID,
		WithLog: true,
		LogSource: &customerpbv2.SystemSource{
			Source:   customerpbv2.SystemSource_APPOINTMENT,
			SourceId: apptCreatedEvent.Id,
		},
	})
	if err != nil {
		log.ErrorContextf(ctx, "AppointmentEventHandle convertV2 ConvertCustomer error, err: %v, customerID:%d", err,
			customerID)

		return 0, err
	}

	return convertCustomerID, nil
}

func (c *AppointmentEventConsumer) validateEventData(ctx context.Context, eventData *eventbuspb.EventData) bool {
	tenant := eventData.GetTenant()
	apptCreatedEvent := eventData.GetAppointmentCreatedEvent()

	if tenant == nil || tenant.GetCompanyId() == 0 || tenant.GetBusinessId() == 0 {
		log.InfoContextf(ctx, "AppointmentEventHandle eventData invalid tenant data, tenant: %+v", tenant)

		return false
	}
	if apptCreatedEvent == nil || apptCreatedEvent.GetCustomerId() == 0 {
		log.InfoContextf(ctx, "AppointmentEventHandle apptCreatedEvent is invalid, apptCreatedEvent: %+v",
			apptCreatedEvent)

		return false
	}

	return true
}

func (c *AppointmentEventConsumer) shouldSkipK9Grooming(ctx context.Context, tenant *organizationpb.Tenant,
	apptCreatedEvent *eventbuspb.AppointmentCreatedEvent) bool {
	for _, companyID := range c.gb.GetK9CompanyIDList(ctx) {
		if tenant.GetCompanyId() == companyID {
			for _, serviceItemType := range apptCreatedEvent.GetServiceItemTypes() {
				if serviceItemType == offeringpb.ServiceItemType_GROOMING {
					log.InfoContextf(ctx, "AppointmentEventHandle K9 grooming(tour) not need to convert, customerID:%d",
						apptCreatedEvent.GetCustomerId())

					return true // Skip processing
				}
			}
		}
	}

	return false // Do not skip
}
