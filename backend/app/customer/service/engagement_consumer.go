package service

import (
	"context"
	"fmt"

	"github.com/IBM/sarama"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	engagementpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/engagement/v1"
	eventbuspb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/event_bus/v1"
	organizationpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	activitylog "github.com/MoeGolibrary/moego/backend/app/customer/logic/activity_log"
	"github.com/MoeGolibrary/moego/backend/app/customer/logic/customer"
	historylog "github.com/MoeGolibrary/moego/backend/app/customer/logic/history_log"
	customerrepo "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/customer"
	"github.com/MoeGolibrary/moego/backend/app/customer/repo/staff"
	customerutils "github.com/MoeGolibrary/moego/backend/app/customer/utils"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v1"
	customerpbv2 "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

type EngagementEventConsumer struct {
	cl        *customer.Logic
	hll       *historylog.Logic
	staffRepo staff.ReadWriter

	// v2
	customerRepo customerrepo.Repository
	al           *activitylog.Logic
}

func NewEngagementEventConsumer() *EngagementEventConsumer {
	return &EngagementEventConsumer{
		cl:           customer.New(),
		hll:          historylog.New(),
		staffRepo:    staff.New(),
		customerRepo: customerrepo.New(),
		al:           activitylog.New(),
	}
}

func (c *EngagementEventConsumer) EngagementEventHandle(ctx context.Context, msg *sarama.ConsumerMessage) error {
	if msg == nil {
		return nil
	}
	log.InfoContextf(ctx, "EngagementEventHandle get message, key:%s, value:%s", string(msg.Key), string(msg.Value))

	// parse msg
	eventData, _, err := parseEventData(ctx, msg)
	if err != nil {
		log.ErrorContextf(ctx, "EngagementEventHandle parseEventData err, err: %v", err)

		return err
	}
	log.InfoContextf(ctx, "EngagementEventHandle parseEventData, eventData: %+v", eventData)

	// check
	var (
		tenant = eventData.GetTenant()
		event  = eventData.GetCallUpdatedStatusEvent()
	)
	if tenant == nil || tenant.GetCompanyId() == 0 || tenant.GetBusinessId() == 0 {
		log.InfoContextf(ctx, "EngagementEventHandle eventData invalid tenant data, tenant: %+v", tenant)

		return nil
	}
	if event == nil ||
		event.GetCallLogId() == 0 ||
		event.GetCustomerId() == 0 ||
		event.GetStaffId() == 0 ||
		event.GetDirection() == engagementpb.CallingDirection_CALLING_DIRECTION_UNSPECIFIED ||
		event.GetStatus() == engagementpb.Status_CALLING_LOG_STATUS_UNSPECIFIED {
		log.InfoContextf(ctx, "EngagementEventHandle eventData invalid event, "+
			"event: %+v", event)

		return nil
	}

	//logID, err := c.saveLogV1(ctx, tenant, event)
	//if err != nil {
	//	return err
	//}

	logID, err := c.saveLogV2(ctx, tenant, event)
	if err != nil {
		return err
	}

	log.InfoContextf(ctx, "EngagementEventHandle create history log success, logID:%d, customerID:%d",
		logID, event.GetCustomerId())

	return nil
}

//nolint:unused
func (c *EngagementEventConsumer) saveLogV1(ctx context.Context, tenant *organizationpb.Tenant,
	event *eventbuspb.CallUpdatedStatusEvent) (int64, error) {
	// check customer lead
	customerID := event.GetCustomerId()
	customer, err := c.cl.Get(ctx, &customer.GetCustomerParams{ID: customerID})
	if err != nil {
		log.ErrorContextf(ctx, "EngagementEventHandle get customer error, err: %v, customerID:%d", err, customerID)

		return 0, err
	}
	if customer == nil || customer.Type != customerpb.Customer_LEAD {
		log.InfoContextf(ctx, "EngagementEventHandle get customer not found, customerID:%d", customerID)

		return 0, nil
	}

	// save to db
	logID, err := c.hll.Create(ctx, &historylog.CreateHistoryLogDatum{
		CustomerID:          customerID,
		CustomerName:        customerutils.ConvCustomerName(customer.GivenName, customer.FamilyName),
		CustomerPhoneNumber: customer.PhoneNumber,
		BusinessID:          tenant.GetBusinessId(),
		CompanyID:           tenant.GetCompanyId(),
		StaffID:             event.GetStaffId(),
		Action: &customerpb.HistoryLog_Action{
			Action: &customerpb.HistoryLog_Action_Call{ // 使用包装器类型
				Call: &customerpb.HistoryLog_Call{
					CallId:    event.GetCallLogId(),
					State:     customerpb.HistoryLog_Call_State(event.GetStatus()),
					Direction: customerpb.HistoryLog_Call_Direction(event.GetDirection()),
				},
			},
		},
		Source:     customerpb.HistoryLog_STAFF.Enum(),
		SourceID:   &event.StaffId,
		SourceName: customerutils.ToPointer(c.getStaffName(ctx, event.GetStaffId(), tenant.GetCompanyId())),
	})
	if err != nil {
		log.ErrorContextf(ctx, "EngagementEventHandle create history log error, err: %v, callID:%d", err.Error(),
			event.GetCallLogId())

		return 0, err
	}

	return logID, nil
}

func (c *EngagementEventConsumer) getStaffName(ctx context.Context, id int64, companyID int64) string {
	detail, err := c.staffRepo.GetStaffDetail(ctx, id, companyID, 0)
	if err != nil {
		log.ErrorContextf(ctx, "EngagementEventConsumer getStaffName err, id:%d, err: %v", id, err)

		return ""
	}

	return fmt.Sprintf("%s %s", detail.GetFirstName(), detail.GetLastName())
}

func parseEventData(ctx context.Context, msg *sarama.ConsumerMessage) (
	*eventbuspb.EventData, eventbuspb.EventType, error) {
	unmarshaler := protojson.UnmarshalOptions{
		DiscardUnknown: true, // new fields should not break old consumer.
	}
	var event eventbuspb.Event
	if err := unmarshaler.Unmarshal(msg.Value, &event); err != nil {
		log.ErrorContextf(ctx, "parseEventData unmarshal event err: %v, value: %v", err, string(msg.Value))

		return nil, eventbuspb.EventType_TYPE_UNSPECIFIED, err
	}

	var detail proto.Message
	var err error
	if detail, err = event.Detail.UnmarshalNew(); err != nil {
		log.ErrorContextf(ctx, "parseEventData unmarshal detail err: %v, value: %v", err, string(msg.Value))

		return nil, eventbuspb.EventType_TYPE_UNSPECIFIED, err
	}

	var eventData *eventbuspb.EventData
	if ed, ok := detail.(*eventbuspb.EventData); ok {
		eventData = ed
	} else {
		log.ErrorContextf(ctx, "parseEventData invalid detail type, expected *eventbuspb.EventData, got %T", detail)

		return nil, eventbuspb.EventType_TYPE_UNSPECIFIED, fmt.Errorf("invalid detail type")
	}

	return eventData, event.EventType, nil
}

func (c *EngagementEventConsumer) saveLogV2(ctx context.Context, tenant *organizationpb.Tenant,
	event *eventbuspb.CallUpdatedStatusEvent) (int64, error) {
	// get customer info
	customerID := event.GetCustomerId()
	customer, err := c.customerRepo.GetByID(ctx, customerID)
	if err != nil {
		log.ErrorContextf(ctx, "EngagementEventConsumer saveLogV2 get customer error, err: %v, customerID:%d", err,
			customerID)

		return 0, err
	}
	if customer == nil || customer.ConvertToCustomerID > 0 {
		log.InfoContextf(ctx, "EngagementEventConsumer saveLogV2 get customer not found or is converted, customerID:%d",
			customerID)

		return 0, nil
	}

	// save to db
	logID, err := c.al.Create(ctx, &activitylog.CreateActivityLogDatum{
		CustomerID:          customerID,
		CustomerName:        customerutils.ConvCustomerName(customer.GivenName, customer.FamilyName),
		CustomerPhoneNumber: event.GetPhoneNumber(),
		BusinessID:          tenant.GetBusinessId(),
		CompanyID:           tenant.GetCompanyId(),
		StaffID:             event.GetStaffId(),
		Action: &customerpbv2.ActivityLog_Action{
			Action: &customerpbv2.ActivityLog_Action_Call{ // 使用包装器类型
				Call: &customerpbv2.ActivityLog_Call{
					CallId:    event.GetCallLogId(),
					State:     customerpbv2.ActivityLog_Call_State(event.GetStatus()),
					Direction: customerpbv2.ActivityLog_Call_Direction(event.GetDirection()),
				},
			},
		},
		Source: &customerpbv2.SystemSource{
			Source:     customerpbv2.SystemSource_STAFF,
			SourceId:   event.StaffId,
			SourceName: c.getStaffName(ctx, event.GetStaffId(), tenant.GetCompanyId()),
		},
	})
	if err != nil {
		log.ErrorContextf(ctx, "EngagementEventHandle saveLogV2 create history log error, err: %v, callID:%d",
			err.Error(),
			event.GetCallLogId())

		return 0, err
	}

	return logID, nil
}
