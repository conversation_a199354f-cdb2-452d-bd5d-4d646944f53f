package service

import (
	"context"

	"github.com/IBM/sarama"

	eventbuspb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/event_bus/v1"
	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	organizationpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	"github.com/MoeGolibrary/moego/backend/app/customer/logic/convert"
	"github.com/MoeGolibrary/moego/backend/app/customer/logic/customer"
	historylog "github.com/MoeGolibrary/moego/backend/app/customer/logic/history_log"
	"github.com/MoeGolibrary/moego/backend/app/customer/repo/growthbook"
	customerrepo "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/customer"
	customerutils "github.com/MoeGolibrary/moego/backend/app/customer/utils"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v1"
	customerpbv2 "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

type OnlineBookingEventConsumer struct {
	cl  *customer.Logic
	hll *historylog.Logic
	gb  growthbook.API

	// v2
	customerRepo customerrepo.Repository
	convertLogic *convert.Logic
}

func NewOnlineBookingEventConsumer() *OnlineBookingEventConsumer {
	return &OnlineBookingEventConsumer{
		cl:           customer.New(),
		hll:          historylog.New(),
		gb:           growthbook.New(),
		customerRepo: customerrepo.New(),
		convertLogic: convert.New(),
	}
}

func (c *OnlineBookingEventConsumer) OnlineBookingEventHandle(ctx context.Context, msg *sarama.ConsumerMessage) error {
	if msg == nil {
		return nil
	}
	log.InfoContextf(ctx, "OnlineBookingEventHandle get message, key:%s, value:%s", string(msg.Key), string(msg.Value))

	// parse msg
	eventData, _, err := parseEventData(ctx, msg)
	if err != nil {
		log.ErrorContextf(ctx, "OnlineBookingEventHandle parseEventData err, err: %v", err)

		return err
	}
	log.InfoContextf(ctx, "OnlineBookingEventHandle parseEventData, eventData: %+v", eventData)

	// check
	if !c.validateEventData(ctx, eventData) {
		return nil // Invalid data, stop processing
	}
	tenant := eventData.GetTenant()
	obSubmittedEvent := eventData.GetOnlineBookingSubmittedEvent()

	// TODO: 针对k9的特殊逻辑 grooming服务(tour)不进行lead转换
	if c.shouldSkipK9Grooming(ctx, tenant, obSubmittedEvent) {
		return nil
	}

	//customerID, err := c.convertV1(ctx, tenant, obSubmittedEvent)
	//if err != nil {
	//	return err
	//}

	customerID, err := c.convertV2(ctx, obSubmittedEvent)
	if err != nil {
		return err
	}

	log.InfoContextf(ctx, "OnlineBookingEventHandle convert customer success, leadID:%d, customerID:%d",
		obSubmittedEvent.GetCustomerId(), customerID)

	return nil
}

//nolint:unused
func (c *OnlineBookingEventConsumer) convertV1(ctx context.Context, tenant *organizationpb.Tenant,
	obSubmittedEvent *eventbuspb.OnlineBookingSubmittedEvent) (int64, error) {
	// check customer lead
	customerID := obSubmittedEvent.GetCustomerId()
	customerInfo, err := c.cl.Get(ctx, &customer.GetCustomerParams{ID: customerID})
	if err != nil {
		log.ErrorContextf(ctx, "OnlineBookingEventHandle get customer error, err: %v, customerID:%d", err, customerID)

		return 0, err
	}
	if customerInfo == nil || customerInfo.Type != customerpb.Customer_LEAD {
		log.InfoContextf(ctx, "OnlineBookingEventHandle get customer not found, customerID:%d", customerID)

		return 0, nil
	}

	// update business ID
	if customerInfo.PreferredBusinessID <= 0 {
		if err := c.cl.Update(ctx, &customer.Customer{
			ID:                  customerInfo.ID,
			PreferredBusinessID: tenant.GetBusinessId(),
		}); err != nil {
			log.ErrorContextf(ctx, "OnlineBookingEventHandle update customer error, err: %v, customerID:%d",
				err, customerID)

			return 0, err
		}
	}

	// convert customer
	if err := c.cl.ConvertCustomer(ctx, customerInfo.ID); err != nil {
		log.ErrorContextf(ctx, "OnlineBookingEventHandle convert customer error, err: %v, customerID:%d",
			err, customerID)

		return 0, err
	}

	// add convert history log
	if _, err := c.hll.Create(ctx, &historylog.CreateHistoryLogDatum{
		CustomerID:          customerInfo.ID,
		CustomerName:        customerutils.ConvCustomerName(customerInfo.GivenName, customerInfo.FamilyName),
		CustomerPhoneNumber: customerInfo.PhoneNumber,
		BusinessID:          customerInfo.PreferredBusinessID,
		CompanyID:           customerInfo.CompanyID,
		Action: &customerpb.HistoryLog_Action{
			Action: &customerpb.HistoryLog_Action_Convert{
				Convert: &customerpb.HistoryLog_Convert{
					OriginType: customerInfo.Type,
					TargetType: customerpb.Customer_LEAD,
				},
			},
		},
		Source:   customerpb.HistoryLog_ONLINE_BOOKING.Enum(),
		SourceID: &obSubmittedEvent.Id,
	}); err != nil {
		log.ErrorContextf(ctx, "OnlineBookingEventHandle Create log error, err: %v, customerID:%d", err, customerID)

		return 0, err
	}

	return customerID, nil
}

func (c *OnlineBookingEventConsumer) convertV2(ctx context.Context,
	obSubmittedEvent *eventbuspb.OnlineBookingSubmittedEvent) (int64, error) {
	// check customer lead
	customerID := obSubmittedEvent.GetCustomerId()
	customerInfo, err := c.customerRepo.GetByID(ctx, customerID)
	if err != nil {
		log.ErrorContextf(ctx, "OnlineBookingEventHandle convertV2 get customer error, err: %v, customerID:%d",
			err, customerID)

		return 0, err
	}
	if customerInfo == nil ||
		customerInfo.CustomerType != customerpbv2.CustomerType_LEAD ||
		customerInfo.ConvertToCustomerID > 0 {
		log.InfoContextf(ctx,
			"OnlineBookingEventHandle convertV2 get customer not found or not lead or lead converted,customerID:%d",
			customerID)

		return 0, nil
	}

	// convert customer
	convertCustomerID, err := c.convertLogic.ConvertCustomer(ctx, &convert.Datum{
		ID:      customerID,
		WithLog: true,
		LogSource: &customerpbv2.SystemSource{
			Source:   customerpbv2.SystemSource_ONLINE_BOOKING,
			SourceId: obSubmittedEvent.Id,
		},
	})
	if err != nil {
		log.ErrorContextf(ctx, "OnlineBookingEventHandle convertV2 ConvertCustomer error, err: %v, customerID:%d",
			err, customerID)

		return 0, err
	}

	return convertCustomerID, nil
}

func (c *OnlineBookingEventConsumer) validateEventData(ctx context.Context, eventData *eventbuspb.EventData) bool {
	tenant := eventData.GetTenant()
	obSubmittedEvent := eventData.GetOnlineBookingSubmittedEvent()

	if tenant == nil || tenant.GetCompanyId() == 0 || tenant.GetBusinessId() == 0 {
		log.InfoContextf(ctx, "OnlineBookingEventHandle eventData invalid tenant data, tenant: %+v", tenant)

		return false
	}
	if obSubmittedEvent == nil || obSubmittedEvent.GetCustomerId() == 0 {
		log.InfoContextf(ctx, "OnlineBookingEventHandle obSubmittedEvent is invalid, obSubmittedEvent: %+v",
			obSubmittedEvent)

		return false
	}

	return true
}

func (c *OnlineBookingEventConsumer) shouldSkipK9Grooming(ctx context.Context, tenant *organizationpb.Tenant,
	obSubmittedEvent *eventbuspb.OnlineBookingSubmittedEvent) bool {
	for _, companyID := range c.gb.GetK9CompanyIDList(ctx) {
		if tenant.GetCompanyId() == companyID {
			for _, serviceItemType := range obSubmittedEvent.GetServiceItemTypes() {
				if serviceItemType == offeringpb.ServiceItemType_GROOMING {
					log.InfoContextf(ctx, "OnlineBookingEventHandle K9 grooming(tour) not need to convert, "+
						"customerID:%d", obSubmittedEvent.GetCustomerId())

					return true // Skip processing
				}
			}
		}
	}

	return false // Do not skip
}
