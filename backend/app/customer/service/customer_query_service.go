package service

import (
	"context"
	"strings"
	"time"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"gorm.io/gorm"

	customerquery "github.com/MoeGolibrary/moego/backend/app/customer/logic/customer_query"
	customersearch "github.com/MoeGolibrary/moego/backend/app/customer/logic/customer_search"
	"github.com/MoeGolibrary/moego/backend/app/customer/repo/db"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v1"
)

// CustomerQueryService implements the queries specified in docs/unified-moe_customer_queries_tasks.md
// Uses raw SQL built with strict column whitelist and parameter binding for safety.
type CustomerQueryService struct {
	gorm           *gorm.DB
	customerSearch *customersearch.Logic
	customerQuery  *customerquery.Logic
	customerpb.UnimplementedCustomerQueryServiceServer
}

func NewCustomerQueryService() *CustomerQueryService {
	return &CustomerQueryService{
		gorm:           db.GetDB(),
		customerSearch: customersearch.New(),
		customerQuery:  customerquery.New(),
	}
}

// helpers

/*
func sanitizeOrder(field customerpb.CustomerOrderField, dir customerpb.OrderDirection) (
	column string, direction string, err error) {
	switch field {
	case customerpb.CustomerOrderField_CLIENT_ID:
		column = "id"
	case customerpb.CustomerOrderField_FIRST_NAME:
		column = "first_name"
	case customerpb.CustomerOrderField_LAST_NAME:
		column = "last_name"
	default:
		return "", "", fmt.Errorf("invalid order field")
	}
	switch dir {
	case customerpb.OrderDirection_ASC:
		direction = "ASC"
	case customerpb.OrderDirection_DESC:
		direction = "DESC"
	default:
		return "", "", fmt.Errorf("invalid order direction")
	}

	return
}

func buildScopeWhere(sb *strings.Builder, args *[]any, scope *customerpb.Scope, tableAlias string) {
	if tableAlias != "" {
		tableAlias += "."
	}
	sb.WriteString(" status = 1 AND ")
	sb.WriteString(tableAlias)
	sb.WriteString("company_id = ?")
	*args = append(*args, scope.GetCompanyId())
	if len(scope.GetBusinessIds()) > 0 {
		sb.WriteString(" AND ")
		sb.WriteString(tableAlias)
		sb.WriteString("business_id IN (" + placeHolders(len(scope.GetBusinessIds())) + ")")
		for _, id := range scope.GetBusinessIds() {
			*args = append(*args, id)
		}
	}
	if len(scope.GetCustomerIds()) > 0 {
		sb.WriteString(" AND ")
		sb.WriteString(tableAlias)
		sb.WriteString("id IN (" + placeHolders(len(scope.GetCustomerIds())) + ")")
		for _, id := range scope.GetCustomerIds() {
			*args = append(*args, id)
		}
	}
}
*/

func placeHolders(n int) string {
	if n <= 0 {
		return ""
	}
	r := make([]string, n)
	for i := 0; i < n; i++ {
		r[i] = "?"
	}

	return strings.Join(r, ",")
}

// ============ Customer ============
// SearchCustomerIds(context.Context, *SearchCustomerIdsRequest) (*SearchCustomerIdsResponse, error)
// nolint:revive // method name must match proto-generated interface (SearchCustomerIds)
func (s *CustomerQueryService) SearchCustomerIds(ctx context.Context, req *customerpb.SearchCustomerIdsRequest) (
	*customerpb.SearchCustomerIdsResponse, error) {
	log.InfoContextf(ctx, "SearchCustomerIds started, company_id: %d, keyword: %s, business_ids: %v",
		req.GetScope().GetCompanyId(), req.GetKeyword(), req.GetScope().GetBusinessIds())

	if req.GetScope().GetCompanyId() <= 0 || strings.TrimSpace(req.GetKeyword()) == "" {
		log.ErrorContextf(ctx, "SearchCustomerIds validation failed, company_id: %d, keyword: %s",
			req.GetScope().GetCompanyId(), req.GetKeyword())

		return nil, status.Error(codes.InvalidArgument, "company_id and keyword are required")
	}

	ids, err := s.customerSearch.SearchCustomerIDs(
		ctx,
		req.Scope.GetCompanyId(),
		req.Scope.GetBusinessIds(),
		req.GetKeyword(),
	)
	if err != nil {
		log.ErrorContextf(ctx, "SearchCustomerIds failed, error: %v", err)

		return nil, err
	}

	log.InfoContextf(ctx, "SearchCustomerIds completed successfully, found %d customers", len(ids))

	return &customerpb.SearchCustomerIdsResponse{CustomerIds: ids}, nil
}

// nolint:revive // method name must match proto-generated interface (SearchCustomerIdsByLastName)
func (s *CustomerQueryService) SearchCustomerIdsByLastName(
	ctx context.Context,
	req *customerpb.SearchCustomerIdsByLastNameRequest,
) (*customerpb.SearchCustomerIdsByLastNameResponse, error) {
	log.InfoContextf(ctx, "SearchCustomerIdsByLastName started, company_id: %d, keyword: %s, business_ids: %v",
		req.GetScope().GetCompanyId(), req.GetKeyword(), req.GetScope().GetBusinessIds())

	if req.GetScope().GetCompanyId() <= 0 || strings.TrimSpace(req.GetKeyword()) == "" {
		log.ErrorContextf(ctx, "SearchCustomerIdsByLastName validation failed, company_id: %d, keyword: %s",
			req.GetScope().GetCompanyId(), req.GetKeyword())

		return nil, status.Error(codes.InvalidArgument, "company_id and keyword are required")
	}

	ids, err := s.customerSearch.SearchCustomerIDsByLastName(
		ctx,
		req.Scope.GetCompanyId(),
		req.Scope.GetBusinessIds(),
		req.GetKeyword(),
	)
	if err != nil {
		log.ErrorContextf(ctx, "SearchCustomerIdsByLastName failed, error: %v", err)

		return nil, err
	}

	log.InfoContextf(ctx, "SearchCustomerIdsByLastName completed successfully, found %d customers", len(ids))

	return &customerpb.SearchCustomerIdsByLastNameResponse{CustomerIds: ids}, nil
}

// nolint:revive // proto 接口命名固定为 *Ids
func (s *CustomerQueryService) FilterCustomerIds(ctx context.Context, req *customerpb.FilterCustomerIdsRequest) (
	*customerpb.FilterCustomerIdsResponse, error) {
	log.InfoContextf(ctx, "FilterCustomerIds started, company_id: %d, business_ids: %v, filter: %+v",
		req.GetScope().GetCompanyId(), req.GetScope().GetBusinessIds(), req.GetFilter())

	if req.GetScope().GetCompanyId() <= 0 {
		log.ErrorContextf(ctx, "FilterCustomerIds validation failed, company_id: %d",
			req.GetScope().GetCompanyId())

		return nil, status.Error(codes.InvalidArgument, "company_id is required")
	}

	// 调用 logic 层处理协议转换和查询
	ids, err := s.customerQuery.FilterCustomerIDsWithFilter(ctx, req.GetScope(), req.GetFilter())
	if err != nil {
		log.ErrorContextf(ctx, "FilterCustomerIds failed, error: %v", err)

		return nil, err
	}

	log.InfoContextf(ctx, "FilterCustomerIds completed successfully, found %d customers", len(ids))

	return &customerpb.FilterCustomerIdsResponse{CustomerIds: ids}, nil
}

// nolint:revive // proto 接口命名固定为 *Ids
func (s *CustomerQueryService) ListCustomerIds(ctx context.Context, req *customerpb.ListCustomerIdsRequest) (
	*customerpb.ListCustomerIdsResponse, error) {
	log.InfoContextf(ctx, "ListCustomerIds started, company_id: %d, limit: %d, offset: %d",
		req.GetScope().GetCompanyId(), req.GetPage().GetLimit(), req.GetPage().GetOffset())

	if req.GetScope().GetCompanyId() <= 0 || req.GetPage().GetLimit() <= 0 {
		log.ErrorContextf(ctx, "ListCustomerIds validation failed, company_id: %d, limit: %d",
			req.GetScope().GetCompanyId(), req.GetPage().GetLimit())

		return nil, status.Error(codes.InvalidArgument, "company_id and positive limit are required")
	}

	// nolint:revive // proto 使用 Ids 命名
	ids, err := s.customerQuery.ListCustomerIDs(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "ListCustomerIds failed, error: %v", err)

		return nil, err
	}

	log.InfoContextf(ctx, "ListCustomerIds completed successfully, found %d customers", len(ids))

	return &customerpb.ListCustomerIdsResponse{CustomerIds: ids}, nil
}

// nolint:revive // proto 接口命名固定为 *Ids
func (s *CustomerQueryService) ValidateActiveCustomerIds(
	ctx context.Context,
	req *customerpb.ValidateActiveCustomerIdsRequest,
) (*customerpb.ValidateActiveCustomerIdsResponse, error) {
	log.InfoContextf(ctx, "ValidateActiveCustomerIds started, company_id: %d, customer_ids: %v",
		req.GetCompanyId(), req.GetCustomerIds())

	if req.GetCompanyId() <= 0 || len(req.GetCustomerIds()) == 0 {
		log.ErrorContextf(ctx, "ValidateActiveCustomerIds validation failed, company_id: %d, customer_ids count: %d",
			req.GetCompanyId(), len(req.GetCustomerIds()))

		return nil, status.Error(codes.InvalidArgument, "company_id and customer_ids are required")
	}

	// nolint:revive // proto 使用 Ids 命名
	ids, err := s.customerQuery.ValidateActiveCustomerIDs(ctx, req.GetCompanyId(), req.GetCustomerIds())
	if err != nil {
		log.ErrorContextf(ctx, "ValidateActiveCustomerIds failed, error: %v", err)

		return nil, err
	}

	log.InfoContextf(ctx, "ValidateActiveCustomerIds completed successfully, validated %d customers", len(ids))

	return &customerpb.ValidateActiveCustomerIdsResponse{CustomerIds: ids}, nil
}

func (s *CustomerQueryService) GetCustomersBasicInfo(
	ctx context.Context,
	req *customerpb.GetCustomersBasicInfoRequest,
) (*customerpb.GetCustomersBasicInfoResponse, error) {
	log.InfoContextf(ctx, "GetCustomersBasicInfo started, customer_ids: %v", req.GetCustomerIds())

	if len(req.GetCustomerIds()) == 0 {
		log.ErrorContextf(ctx, "GetCustomersBasicInfo validation failed, customer_ids count: %d",
			len(req.GetCustomerIds()))

		return nil, status.Error(codes.InvalidArgument, "customer_ids required")
	}

	items, err := s.customerQuery.GetCustomersBasicInfo(ctx, req.GetCompanyId(), req.GetCustomerIds())
	if err != nil {
		log.ErrorContextf(ctx, "GetCustomersBasicInfo failed, error: %v", err)

		return nil, err
	}

	log.InfoContextf(ctx, "GetCustomersBasicInfo completed successfully, found %d customers", len(items))

	return &customerpb.GetCustomersBasicInfoResponse{Customers: items}, nil
}

// ============ Contact ============

// nolint:revive // proto 接口命名固定为 *Ids
func (s *CustomerQueryService) SearchContactCustomerIds(
	ctx context.Context,
	req *customerpb.SearchContactCustomerIdsRequest,
) (*customerpb.SearchContactCustomerIdsResponse, error) {
	log.InfoContextf(ctx, "SearchContactCustomerIds started, company_id: %d, keyword: %s, name_keyword: %s",
		req.GetScope().GetCompanyId(), req.GetKeyword(), req.GetNameKeyword())

	if req.GetScope().GetCompanyId() <= 0 {
		log.ErrorContextf(ctx, "SearchContactCustomerIds validation failed, company_id: %d",
			req.GetScope().GetCompanyId())

		return nil, status.Error(codes.InvalidArgument, "company_id is required")
	}

	customerIDs, err := s.customerQuery.SearchContactCustomerIDs(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "SearchContactCustomerIds failed, error: %v", err)

		return nil, status.Error(codes.Internal, "failed to search contact customer ids")
	}

	log.InfoContextf(ctx, "SearchContactCustomerIds completed successfully, found %d customers", len(customerIDs))

	return &customerpb.SearchContactCustomerIdsResponse{CustomerIds: customerIDs}, nil
}

// nolint:revive // proto 接口命名固定为 *Ids
func (s *CustomerQueryService) FilterContactCustomerIds(
	ctx context.Context,
	req *customerpb.FilterContactCustomerIdsRequest,
) (*customerpb.FilterContactCustomerIdsResponse, error) {
	log.InfoContextf(ctx, "FilterContactCustomerIds started, company_id: %d, business_ids: %v, customer_ids: %v",
		req.GetScope().GetCompanyId(), req.GetScope().GetBusinessIds(), req.GetScope().GetCustomerIds())

	if req.GetScope().GetCompanyId() <= 0 {
		log.ErrorContextf(ctx, "FilterContactCustomerIds validation failed, company_id: %d",
			req.GetScope().GetCompanyId())

		return nil, status.Error(codes.InvalidArgument, "company_id is required")
	}

	customerIDs, err := s.customerQuery.FilterContactCustomerIDs(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "FilterContactCustomerIds failed, error: %v", err)

		return nil, status.Error(codes.Internal, "failed to filter contact customer ids")
	}

	log.InfoContextf(ctx, "FilterContactCustomerIds completed successfully, found %d customers", len(customerIDs))

	return &customerpb.FilterContactCustomerIdsResponse{CustomerIds: customerIDs}, nil
}

// nolint:revive // proto 接口命名固定为 *Ids
func (s *CustomerQueryService) CountFilterContactCustomerIds(
	ctx context.Context,
	req *customerpb.CountFilterContactCustomerIdsRequest,
) (*customerpb.CountFilterContactCustomerIdsResponse, error) {
	log.InfoContextf(ctx, "CountFilterContactCustomerIds started, company_id: %d, business_ids: %v, customer_ids: %v",
		req.GetScope().GetCompanyId(), req.GetScope().GetBusinessIds(), req.GetScope().GetCustomerIds())

	if req.GetScope().GetCompanyId() <= 0 {
		log.ErrorContextf(ctx, "CountFilterContactCustomerIds validation failed, company_id: %d",
			req.GetScope().GetCompanyId())

		return nil, status.Error(codes.InvalidArgument, "company_id is required")
	}

	customerIDs, err := s.customerQuery.CountFilterContactCustomerIDs(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "CountFilterContactCustomerIds failed, error: %v", err)

		return nil, status.Error(codes.Internal, "failed to count filter contact customer ids")
	}

	log.InfoContextf(ctx, "CountFilterContactCustomerIds completed successfully, found %d customers", len(customerIDs))

	return &customerpb.CountFilterContactCustomerIdsResponse{CustomerIds: customerIDs}, nil
}

func (s *CustomerQueryService) GetPrimaryPhones(ctx context.Context, req *customerpb.GetPrimaryPhonesRequest) (
	*customerpb.PrimaryPhones, error) {
	log.InfoContextf(ctx, "GetPrimaryPhones started, company_id: %d, customer_ids: %v",
		req.GetScope().GetCompanyId(), req.GetCustomerIds())

	if req.GetScope().GetCompanyId() <= 0 {
		log.ErrorContextf(ctx, "GetPrimaryPhones validation failed, company_id: %d",
			req.GetScope().GetCompanyId())

		return nil, status.Error(codes.InvalidArgument, "company_id is required")
	}

	phones, err := s.customerQuery.GetPrimaryPhones(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "GetPrimaryPhones failed, error: %v", err)

		return nil, status.Error(codes.Internal, "failed to get primary phones")
	}

	log.InfoContextf(ctx, "GetPrimaryPhones completed successfully, found %d phone numbers", len(phones))

	return &customerpb.PrimaryPhones{Items: phones}, nil
}

// ============ Address ============

// nolint:revive // proto 接口命名固定为 *Ids
func (s *CustomerQueryService) SearchAddressCustomerIds(
	ctx context.Context,
	req *customerpb.SearchAddressCustomerIdsRequest,
) (*customerpb.SearchAddressCustomerIdsResponse, error) {
	log.InfoContextf(ctx, "SearchAddressCustomerIds started, company_id: %d, keyword: %s, business_ids: %v",
		req.GetScope().GetCompanyId(), req.GetKeyword(), req.GetScope().GetBusinessIds())

	if req.GetScope().GetCompanyId() <= 0 || strings.TrimSpace(req.GetKeyword()) == "" {
		log.ErrorContextf(ctx, "SearchAddressCustomerIds validation failed, company_id: %d, keyword: %s",
			req.GetScope().GetCompanyId(), req.GetKeyword())

		return nil, status.Error(codes.InvalidArgument, "company_id and keyword required")
	}

	customerIDs, err := s.customerQuery.SearchAddressCustomerIDs(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "SearchAddressCustomerIds failed, error: %v", err)

		return nil, status.Error(codes.Internal, "failed to search address customer ids")
	}

	log.InfoContextf(ctx, "SearchAddressCustomerIds completed successfully, found %d customers", len(customerIDs))

	return &customerpb.SearchAddressCustomerIdsResponse{CustomerIds: customerIDs}, nil
}

// nolint:revive // proto 接口命名固定为 *Ids
func (s *CustomerQueryService) FilterCustomerIdsByZip(
	ctx context.Context,
	req *customerpb.FilterCustomerIdsByZipRequest,
) (*customerpb.FilterCustomerIdsByZipResponse, error) {
	log.InfoContextf(ctx, "FilterCustomerIdsByZip started, company_id: %d, zip_list: %v, operator: %s",
		req.GetScope().GetCompanyId(), req.GetZipList(), req.GetOperator())

	if req.GetScope().GetCompanyId() <= 0 || len(req.GetZipList()) == 0 {
		log.ErrorContextf(ctx, "FilterCustomerIdsByZip validation failed, company_id: %d, zip_list count: %d",
			req.GetScope().GetCompanyId(), len(req.GetZipList()))

		return nil, status.Error(codes.InvalidArgument, "company_id and zip_list required")
	}

	customerIDs, err := s.customerQuery.FilterCustomerIDsByZip(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "FilterCustomerIdsByZip failed, error: %v", err)

		return nil, status.Error(codes.Internal, "failed to filter customer ids by zip")
	}

	log.InfoContextf(ctx, "FilterCustomerIdsByZip completed successfully, found %d customers", len(customerIDs))

	return &customerpb.FilterCustomerIdsByZipResponse{CustomerIds: customerIDs}, nil
}

// nolint:revive // proto 接口命名固定为 *Ids
func (s *CustomerQueryService) CountFilterAddressCustomerIds(
	ctx context.Context,
	req *customerpb.CountFilterAddressCustomerIdsRequest,
) (*customerpb.CountFilterAddressCustomerIdsResponse, error) {
	log.InfoContextf(ctx, "CountFilterAddressCustomerIds started, company_id: %d, business_ids: %v, customer_ids: %v",
		req.GetScope().GetCompanyId(), req.GetScope().GetBusinessIds(), req.GetScope().GetCustomerIds())

	if req.GetScope().GetCompanyId() <= 0 {
		log.ErrorContextf(ctx, "CountFilterAddressCustomerIds validation failed, company_id: %d",
			req.GetScope().GetCompanyId())

		return nil, status.Error(codes.InvalidArgument, "company_id required")
	}

	customerIDs, err := s.customerQuery.CountFilterAddressCustomerIDs(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "CountFilterAddressCustomerIds failed, error: %v", err)

		return nil, status.Error(codes.Internal, "failed to count filter address customer ids")
	}

	log.InfoContextf(ctx, "CountFilterAddressCustomerIds completed successfully, found %d customers", len(customerIDs))

	return &customerpb.CountFilterAddressCustomerIdsResponse{CustomerIds: customerIDs}, nil
}

// ============ Pet ============

// nolint:revive // proto 接口命名固定为 *Ids
func (s *CustomerQueryService) SearchPetCustomerIds(ctx context.Context, req *customerpb.SearchPetCustomerIdsRequest) (
	*customerpb.SearchPetCustomerIdsResponse, error) {
	log.InfoContextf(ctx, "SearchPetCustomerIds started, company_id: %d, keyword: %s, business_ids: %v",
		req.GetScope().GetCompanyId(), req.GetKeyword(), req.GetScope().GetBusinessIds())

	if req.GetScope().GetCompanyId() <= 0 || strings.TrimSpace(req.GetKeyword()) == "" {
		log.ErrorContextf(ctx, "SearchPetCustomerIds validation failed, company_id: %d, keyword: %s",
			req.GetScope().GetCompanyId(), req.GetKeyword())

		return nil, status.Error(codes.InvalidArgument, "company_id and keyword required")
	}

	sb := &strings.Builder{}
	sb.WriteString("SELECT customer_id FROM moe_customer_pet WHERE company_id = ?")
	args := []any{req.Scope.GetCompanyId()}
	if len(req.Scope.GetBusinessIds()) > 0 {
		sb.WriteString(" AND business_id IN (" + placeHolders(len(req.Scope.BusinessIds)) + ")")
		for _, id := range req.Scope.GetBusinessIds() {
			args = append(args, id)
		}
	}
	sb.WriteString(" AND (pet_name LIKE CONCAT('%', ?, '%') OR ")
	sb.WriteString("breed LIKE CONCAT('%', ?, '%')) AND life_status = 1 AND status = 1")
	args = append(args, req.GetKeyword(), req.GetKeyword())
	rows, err := s.gorm.WithContext(ctx).Raw(sb.String(), args...).Rows()
	if err != nil {
		log.ErrorContextf(ctx, "SearchPetCustomerIds query failed, error: %v", err)

		return nil, err
	}
	defer rows.Close()
	var ids []int64
	for rows.Next() {
		var id int64
		if err := rows.Scan(&id); err != nil {
			log.ErrorContextf(ctx, "SearchPetCustomerIds scan failed, error: %v", err)

			return nil, err
		}
		ids = append(ids, id)
	}

	log.InfoContextf(ctx, "SearchPetCustomerIds completed successfully, found %d customers", len(ids))

	return &customerpb.SearchPetCustomerIdsResponse{CustomerIds: ids}, nil
}

func (s *CustomerQueryService) SearchPetsFulltext(
	ctx context.Context,
	req *customerpb.SearchPetsFulltextRequest,
) (*customerpb.SearchPetsFulltextResponse, error) {
	log.InfoContextf(ctx, "SearchPetsFulltext started, company_id: %d, term: %s, business_ids: %v",
		req.GetScope().GetCompanyId(), req.GetTerm(), req.GetScope().GetBusinessIds())

	if req.GetScope().GetCompanyId() <= 0 || strings.TrimSpace(req.GetTerm()) == "" {
		log.ErrorContextf(ctx, "SearchPetsFulltext validation failed, company_id: %d, term: %s",
			req.GetScope().GetCompanyId(), req.GetTerm())

		return nil, status.Error(codes.InvalidArgument, "company_id and term required")
	}

	sb := &strings.Builder{}
	sb.WriteString("SELECT id, customer_id FROM moe_customer_pet ")
	sb.WriteString("WHERE MATCH(pet_name) AGAINST(? IN NATURAL LANGUAGE MODE) ")
	sb.WriteString("AND company_id = ? AND status = 1 AND life_status = 1")
	args := []any{req.GetTerm(), req.Scope.GetCompanyId()}
	if len(req.Scope.GetBusinessIds()) > 0 {
		sb.WriteString(" AND business_id IN (" + placeHolders(len(req.Scope.BusinessIds)) + ")")
		for _, id := range req.Scope.GetBusinessIds() {
			args = append(args, id)
		}
	}
	rows, err := s.gorm.WithContext(ctx).Raw(sb.String(), args...).Rows()
	if err != nil {
		log.ErrorContextf(ctx, "SearchPetsFulltext query failed, error: %v", err)

		return nil, err
	}
	defer rows.Close()
	items := make([]*customerpb.PetRecord, 0)
	for rows.Next() {
		var id int64
		var cid int64
		if err := rows.Scan(&id, &cid); err != nil {
			log.ErrorContextf(ctx, "SearchPetsFulltext scan failed, error: %v", err)

			return nil, err
		}
		items = append(items, &customerpb.PetRecord{Id: id, CustomerId: cid})
	}

	log.InfoContextf(ctx, "SearchPetsFulltext completed successfully, found %d pets", len(items))

	return &customerpb.SearchPetsFulltextResponse{Pets: items}, nil
}

// nolint:revive // proto 接口命名固定为 *Ids
func (s *CustomerQueryService) FilterPetCustomerIds(ctx context.Context, req *customerpb.FilterPetCustomerIdsRequest) (
	*customerpb.FilterPetCustomerIdsResponse, error) {
	log.InfoContextf(ctx, "FilterPetCustomerIds started, company_id: %d, business_ids: %v, customer_ids: %v",
		req.GetScope().GetCompanyId(), req.GetScope().GetBusinessIds(), req.GetScope().GetCustomerIds())

	if req.GetScope().GetCompanyId() <= 0 {
		log.ErrorContextf(ctx, "FilterPetCustomerIds validation failed, company_id: %d",
			req.GetScope().GetCompanyId())

		return nil, status.Error(codes.InvalidArgument, "company_id required")
	}

	sb := &strings.Builder{}
	sb.WriteString("SELECT DISTINCT customer_id FROM moe_customer_pet ")
	sb.WriteString("WHERE status = 1 AND life_status = 1 AND company_id = ?")
	args := []any{req.Scope.GetCompanyId()}
	if len(req.Scope.GetBusinessIds()) > 0 {
		sb.WriteString(" AND business_id IN (" + placeHolders(len(req.Scope.BusinessIds)) + ")")
		for _, id := range req.Scope.GetBusinessIds() {
			args = append(args, id)
		}
	}
	if len(req.Scope.GetCustomerIds()) > 0 {
		sb.WriteString(" AND customer_id IN (" + placeHolders(len(req.Scope.CustomerIds)) + ")")
		for _, id := range req.Scope.GetCustomerIds() {
			args = append(args, id)
		}
	}
	rows, err := s.gorm.WithContext(ctx).Raw(sb.String(), args...).Rows()
	if err != nil {
		log.ErrorContextf(ctx, "FilterPetCustomerIds query failed, error: %v", err)

		return nil, err
	}
	defer rows.Close()
	var ids []int64
	for rows.Next() {
		var id int64
		if err := rows.Scan(&id); err != nil {
			log.ErrorContextf(ctx, "FilterPetCustomerIds scan failed, error: %v", err)

			return nil, err
		}
		ids = append(ids, id)
	}

	log.InfoContextf(ctx, "FilterPetCustomerIds completed successfully, found %d customers", len(ids))

	return &customerpb.FilterPetCustomerIdsResponse{CustomerIds: ids}, nil
}

// nolint:revive // proto 接口命名固定为 *Ids
func (s *CustomerQueryService) CountFilterPetCustomerIds(
	ctx context.Context,
	req *customerpb.CountFilterPetCustomerIdsRequest,
) (*customerpb.CountFilterPetCustomerIdsResponse, error) {
	log.InfoContextf(ctx, "CountFilterPetCustomerIds started, company_id: %d, business_ids: %v, customer_ids: %v",
		req.GetScope().GetCompanyId(), req.GetScope().GetBusinessIds(), req.GetScope().GetCustomerIds())

	if req.GetScope().GetCompanyId() <= 0 {
		log.ErrorContextf(ctx, "CountFilterPetCustomerIds validation failed, company_id: %d",
			req.GetScope().GetCompanyId())

		return nil, status.Error(codes.InvalidArgument, "company_id required")
	}

	sb := &strings.Builder{}
	sb.WriteString("SELECT mbc.id FROM moe_business_customer mbc ")
	sb.WriteString("LEFT JOIN moe_customer_pet mcp ON mbc.id = mcp.customer_id ")
	sb.WriteString("AND mcp.status = 1 AND mcp.life_status = 1 WHERE mbc.status = 1 AND mbc.company_id = ?")
	args := []any{req.Scope.GetCompanyId()}
	if len(req.Scope.GetBusinessIds()) > 0 {
		sb.WriteString(" AND mbc.business_id IN (" + placeHolders(len(req.Scope.BusinessIds)) + ")")
		for _, id := range req.Scope.GetBusinessIds() {
			args = append(args, id)
		}
	}
	if len(req.Scope.GetCustomerIds()) > 0 {
		sb.WriteString(" AND mbc.id IN (" + placeHolders(len(req.Scope.CustomerIds)) + ")")
		for _, id := range req.Scope.GetCustomerIds() {
			args = append(args, id)
		}
	}
	sb.WriteString(" GROUP BY mbc.id")
	rows, err := s.gorm.WithContext(ctx).Raw(sb.String(), args...).Rows()
	if err != nil {
		log.ErrorContextf(ctx, "CountFilterPetCustomerIds query failed, error: %v", err)

		return nil, err
	}
	defer rows.Close()
	var ids []int64
	for rows.Next() {
		var id int64
		if err := rows.Scan(&id); err != nil {
			log.ErrorContextf(ctx, "CountFilterPetCustomerIds scan failed, error: %v", err)

			return nil, err
		}
		ids = append(ids, id)
	}

	log.InfoContextf(ctx, "CountFilterPetCustomerIds completed successfully, found %d customers", len(ids))

	return &customerpb.CountFilterPetCustomerIdsResponse{CustomerIds: ids}, nil
}

func (s *CustomerQueryService) GetPetTypeDistribution(
	ctx context.Context,
	req *customerpb.GetPetTypeDistributionRequest,
) (*customerpb.PetTypeCounts, error) {
	log.InfoContextf(ctx, "GetPetTypeDistribution started, company_id: %d, business_ids: %v, customer_ids: %v",
		req.GetScope().GetCompanyId(), req.GetScope().GetBusinessIds(), req.GetScope().GetCustomerIds())

	if req.GetScope().GetCompanyId() <= 0 {
		log.ErrorContextf(ctx, "GetPetTypeDistribution validation failed, company_id: %d",
			req.GetScope().GetCompanyId())

		return nil, status.Error(codes.InvalidArgument, "company_id required")
	}

	sb := &strings.Builder{}
	sb.WriteString("SELECT mcp.pet_type_id AS petTypeId, COUNT(*) AS count FROM moe_customer_pet mcp " +
		"WHERE mcp.status = 1 AND mcp.life_status = 1 AND mcp.company_id = ?")
	args := []any{req.Scope.GetCompanyId()}
	if len(req.Scope.GetBusinessIds()) > 0 {
		sb.WriteString(" AND mcp.business_id IN (" + placeHolders(len(req.Scope.BusinessIds)) + ")")
		for _, id := range req.Scope.GetBusinessIds() {
			args = append(args, id)
		}
	}
	if len(req.Scope.GetCustomerIds()) > 0 {
		sb.WriteString(" AND mcp.customer_id IN (" + placeHolders(len(req.Scope.CustomerIds)) + ")")
		for _, id := range req.Scope.GetCustomerIds() {
			args = append(args, id)
		}
	}
	sb.WriteString(" GROUP BY mcp.pet_type_id")
	rows, err := s.gorm.WithContext(ctx).Raw(sb.String(), args...).Rows()
	if err != nil {
		log.ErrorContextf(ctx, "GetPetTypeDistribution query failed, error: %v", err)

		return nil, err
	}
	defer rows.Close()
	items := make([]*customerpb.PetTypeCount, 0)
	for rows.Next() {
		var t int64
		var c int64
		if err := rows.Scan(&t, &c); err != nil {
			log.ErrorContextf(ctx, "GetPetTypeDistribution scan failed, error: %v", err)

			return nil, err
		}
		items = append(items, &customerpb.PetTypeCount{PetTypeId: t, Count: c})
	}

	log.InfoContextf(ctx, "GetPetTypeDistribution completed successfully, found %d pet types", len(items))

	return &customerpb.PetTypeCounts{Items: items}, nil
}

func (s *CustomerQueryService) ListPetsForCustomers(
	ctx context.Context,
	req *customerpb.ListPetsForCustomersRequest,
) (*customerpb.ListPetsForCustomersResponse, error) {
	log.InfoContextf(ctx, "ListPetsForCustomers started, company_id: %d, customer_ids: %v",
		req.GetScope().GetCompanyId(), req.GetCustomerIds())

	if req.GetScope().GetCompanyId() <= 0 || len(req.GetCustomerIds()) == 0 {
		log.ErrorContextf(ctx, "ListPetsForCustomers validation failed, company_id: %d, customer_ids count: %d",
			req.GetScope().GetCompanyId(), len(req.GetCustomerIds()))

		return nil, status.Error(codes.InvalidArgument, "company_id and customer_ids required")
	}

	sb := &strings.Builder{}
	sb.WriteString("SELECT id, customer_id, pet_name, breed, life_status, evaluation_status FROM moe_customer_pet ")
	sb.WriteString("WHERE status = 1 AND life_status = 1 AND company_id = ? AND customer_id IN (")
	sb.WriteString(placeHolders(len(req.CustomerIds)) + ")")
	args := []any{req.Scope.GetCompanyId()}
	for _, id := range req.CustomerIds {
		args = append(args, id)
	}
	if len(req.Scope.GetBusinessIds()) > 0 {
		sb.WriteString(" AND business_id IN (" + placeHolders(len(req.Scope.BusinessIds)) + ")")
		for _, id := range req.Scope.GetBusinessIds() {
			args = append(args, id)
		}
	}
	rows, err := s.gorm.WithContext(ctx).Raw(sb.String(), args...).Rows()
	if err != nil {
		log.ErrorContextf(ctx, "ListPetsForCustomers query failed, error: %v", err)

		return nil, err
	}
	defer rows.Close()
	items := make([]*customerpb.PetInfo, 0)
	for rows.Next() {
		var it customerpb.PetInfo
		if err := rows.Scan(
			&it.PetId, &it.CustomerId, &it.Pet, &it.Breed,
			&it.LifeStatus, &it.EvaluationStatus,
		); err != nil {
			log.ErrorContextf(ctx, "ListPetsForCustomers scan failed, error: %v", err)

			return nil, err
		}
		items = append(items, &it)
	}

	log.InfoContextf(ctx, "ListPetsForCustomers completed successfully, found %d pets", len(items))

	return &customerpb.ListPetsForCustomersResponse{Pets: items}, nil
}

// ============ Tag Binding ============

// nolint:revive // proto 接口命名固定为 *Ids
func (s *CustomerQueryService) FilterCustomerIdsByTag(
	ctx context.Context,
	req *customerpb.FilterCustomerIdsByTagRequest,
) (*customerpb.FilterCustomerIdsByTagResponse, error) {
	log.InfoContextf(ctx, "FilterCustomerIdsByTag started, company_id: %d, tag_ids: %v, operator: %s",
		req.GetScope().GetCompanyId(), req.GetTagIds(), req.GetOperator())

	if req.GetScope().GetCompanyId() <= 0 || len(req.GetTagIds()) == 0 {
		log.ErrorContextf(ctx, "FilterCustomerIdsByTag validation failed, company_id: %d, tag_ids count: %d",
			req.GetScope().GetCompanyId(), len(req.GetTagIds()))

		return nil, status.Error(codes.InvalidArgument, "company_id and tag_ids required")
	}

	// 调用 logic 层处理业务逻辑
	ids, err := s.customerQuery.FilterCustomerIDsByTag(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "FilterCustomerIdsByTag failed, error: %v", err)

		return nil, err
	}

	log.InfoContextf(ctx, "FilterCustomerIdsByTag completed successfully, found %d customers", len(ids))

	return &customerpb.FilterCustomerIdsByTagResponse{CustomerIds: ids}, nil
}

// ============ Pet Code Binding ============

// nolint:revive // proto 接口命名固定为 *Ids
func (s *CustomerQueryService) FilterCustomerIdsByPetCode(
	ctx context.Context,
	req *customerpb.FilterCustomerIdsByPetCodeRequest,
) (*customerpb.FilterCustomerIdsByPetCodeResponse, error) {
	log.InfoContextf(ctx, "FilterCustomerIdsByPetCode started, company_id: %d, code_ids: %v, operator: %s",
		req.GetScope().GetCompanyId(), req.GetCodeIds(), req.GetOperator())

	if req.GetScope().GetCompanyId() <= 0 || len(req.GetCodeIds()) == 0 {
		log.ErrorContextf(ctx, "FilterCustomerIdsByPetCode validation failed, company_id: %d, code_ids count: %d",
			req.GetScope().GetCompanyId(), len(req.GetCodeIds()))

		return nil, status.Error(codes.InvalidArgument, "company_id and code_ids required")
	}

	sb := &strings.Builder{}
	sb.WriteString("SELECT DISTINCT mcp.customer_id FROM moe_customer_pet mcp LEFT JOIN moe_pet_pet_code_binding cb " +
		"ON cb.pet_id = mcp.id WHERE mcp.company_id = ? AND mcp.status = 1 AND mcp.life_status = 1")
	args := []any{req.Scope.GetCompanyId()}
	if len(req.Scope.GetBusinessIds()) > 0 {
		sb.WriteString(" AND mcp.business_id IN (" + placeHolders(len(req.Scope.BusinessIds)) + ")")
		for _, id := range req.Scope.GetBusinessIds() {
			args = append(args, id)
		}
	}
	if len(req.Scope.GetCustomerIds()) > 0 {
		sb.WriteString(" AND mcp.customer_id IN (" + placeHolders(len(req.Scope.CustomerIds)) + ")")
		for _, id := range req.Scope.GetCustomerIds() {
			args = append(args, id)
		}
	}
	switch req.GetOperator() {
	case customerpb.InNotInOperator_IN:
		sb.WriteString(" AND cb.pet_code_id IN (" + placeHolders(len(req.CodeIds)) + ")")
		for _, id := range req.CodeIds {
			args = append(args, id)
		}
	case customerpb.InNotInOperator_NOT_IN:
		sb.WriteString(" AND (cb.pet_code_id IS NULL OR cb.pet_code_id NOT IN (")
		sb.WriteString(placeHolders(len(req.CodeIds)) + "))")
		for _, id := range req.CodeIds {
			args = append(args, id)
		}
	default:
		log.ErrorContextf(ctx, "FilterCustomerIdsByPetCode invalid operator: %s", req.GetOperator())

		return nil, status.Error(codes.InvalidArgument, "operator required")
	}

	rows, err := s.gorm.WithContext(ctx).Raw(sb.String(), args...).Rows()
	if err != nil {
		log.ErrorContextf(ctx, "FilterCustomerIdsByPetCode query failed, error: %v", err)

		return nil, err
	}
	defer rows.Close()
	var ids []int64
	for rows.Next() {
		var id int64
		if err := rows.Scan(&id); err != nil {
			log.ErrorContextf(ctx, "FilterCustomerIdsByPetCode scan failed, error: %v", err)

			return nil, err
		}
		ids = append(ids, id)
	}

	log.InfoContextf(ctx, "FilterCustomerIdsByPetCode completed successfully, found %d customers", len(ids))

	return &customerpb.FilterCustomerIdsByPetCodeResponse{CustomerIds: ids}, nil
}

// ============ Pet Vaccine Binding ============

// nolint:revive // proto 接口命名固定为 *Ids
func (s *CustomerQueryService) CountFilterCustomerIdsByVaccine(
	ctx context.Context,
	req *customerpb.CountFilterCustomerIdsByVaccineRequest,
) (*customerpb.CountFilterCustomerIdsByVaccineResponse, error) {
	log.InfoContextf(ctx, "CountFilterCustomerIdsByVaccine started, company_id: %d, current_time: %v",
		req.GetScope().GetCompanyId(), req.GetCurrentTime())

	if req.GetScope().GetCompanyId() <= 0 || req.GetCurrentTime() == nil {
		log.ErrorContextf(ctx, "CountFilterCustomerIdsByVaccine validation failed, company_id: %d, current_time: %v",
			req.GetScope().GetCompanyId(), req.GetCurrentTime())

		return nil, status.Error(codes.InvalidArgument, "company_id and current_time required")
	}

	current := req.GetCurrentTime().AsTime()
	_ = current // current is used via binding below
	sb := &strings.Builder{}
	sb.WriteString("SELECT mcp.customer_id FROM moe_customer_pet mcp LEFT JOIN moe_pet_pet_vaccine_binding vb " +
		"ON vb.pet_id = mcp.id AND vb.status = 1 WHERE mcp.company_id = ? AND mcp.status = 1 AND mcp.life_status = 1")
	args := []any{req.Scope.GetCompanyId()}
	if len(req.Scope.GetBusinessIds()) > 0 {
		sb.WriteString(" AND mcp.business_id IN (" + placeHolders(len(req.Scope.BusinessIds)) + ")")
		for _, id := range req.Scope.GetBusinessIds() {
			args = append(args, id)
		}
	}
	if len(req.Scope.GetCustomerIds()) > 0 {
		sb.WriteString(" AND mcp.customer_id IN (" + placeHolders(len(req.Scope.CustomerIds)) + ")")
		for _, id := range req.Scope.GetCustomerIds() {
			args = append(args, id)
		}
	}
	sb.WriteString(" AND vb.expiration_date < ? GROUP BY mcp.customer_id")
	args = append(args, time.Unix(req.CurrentTime.Seconds, int64(req.CurrentTime.Nanos)))
	rows, err := s.gorm.WithContext(ctx).Raw(sb.String(), args...).Rows()
	if err != nil {
		log.ErrorContextf(ctx, "CountFilterCustomerIdsByVaccine query failed, error: %v", err)

		return nil, err
	}
	defer rows.Close()
	var ids []int64
	for rows.Next() {
		var id int64
		if err := rows.Scan(&id); err != nil {
			log.ErrorContextf(ctx, "CountFilterCustomerIdsByVaccine scan failed, error: %v", err)

			return nil, err
		}
		ids = append(ids, id)
	}

	log.InfoContextf(ctx, "CountFilterCustomerIdsByVaccine completed successfully, found %d customers", len(ids))

	return &customerpb.CountFilterCustomerIdsByVaccineResponse{CustomerIds: ids}, nil
}
