syntax = "proto3";

package moego.service.message.v2;

import "moego/models/message/v2/message_models.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/message/v2;messagesvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.message.v2";

// ComplianceService 合规服务
service ComplianceService {
  // 获取客户合规配置
  rpc GetCustomerComplianceConfig(GetCustomerComplianceConfigRequest) returns (GetCustomerComplianceConfigResponse);

  // 设置客户合规设置
  rpc SetCustomerComplianceConfig(SetCustomerComplianceConfigRequest) returns (SetCustomerComplianceConfigResponse);
}

// GetCustomerComplianceConfigRequest 请求
message GetCustomerComplianceConfigRequest {
  // company id
  int64 company_id = 1;
  // customer id
  int64 customer_id = 2;
}

// GetCustomerComplianceConfigResponse 响应
message GetCustomerComplianceConfigResponse {
  // 通知配置
  optional moego.models.message.v2.NotificationConfig notification_config = 1;
}

// SetCustomerComplianceConfigRequest 请求
message SetCustomerComplianceConfigRequest {
  // company id
  int64 company_id = 1;
  // customer id
  int64 customer_id = 2;
  // 通知配置
  moego.models.message.v2.NotificationConfig notification_config = 3;
}

// SetCustomerComplianceConfigResponse 响应
message SetCustomerComplianceConfigResponse {}
