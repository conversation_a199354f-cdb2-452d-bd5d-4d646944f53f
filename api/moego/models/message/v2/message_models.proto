syntax = "proto3";

package moego.models.message.v2;

import "moego/client/appointment/v1/appointment_api.proto";
import "moego/models/message/v1/message_enums.proto";
import "moego/models/message/v2/message_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/message/v2;messagepb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.message.v2";

// 消息模型
message MessageModel {
  // 消息 ID
  uint64 id = 1;
  // UUID，用于处理幂等逻辑。使用 UUID v7
  string uuid = 2;
  // 消息所属对话的 ID
  uint64 chat_id = 3;
  // 消息所属 Business 的 Company ID
  uint64 company_id = 4;
  // 消息所属 Business 的 ID
  uint64 business_id = 5;
  // 消息所属 Customer 的 ID
  uint64 customer_id = 6;

  // 发送方的角色
  Role sender_role = 11;
  // 发送方的 ID
  // 根据 Role 来确定 ID 是什么 ID
  uint64 sender_id = 12;
  // 接收方的角色
  Role receiver_role = 13;
  // 接收方的 ID
  // 根据 Role 来确定 ID 是什么 ID
  uint64 receiver_id = 14;

  // 消息投送的渠道
  Channel channel = 21;
  // 消息内容，为了兼容图片等会把链接放在这里
  string content = 22;

  // 消息内容的版本号
  uint32 version = 31;
  // 消息内容的类型
  ContentType content_type = 32;
  // 消息状态，名字避开 DB 关键字
  MessageStatus message_status = 33;

  // 投送失败的原因，仅当消息状态是投送失败时有值
  string deliver_failed_reason = 41;
  // 元信息
  Metadata metadata = 42;
  // 展示用的额外信息
  DisplayExtraInfo display_extra_info = 43;
  // target type
  moego.models.message.v1.TargetType target_type = 44;

  // 创建时间戳，毫秒
  uint64 create_time = 51;
  // 投送时间戳，毫秒
  uint64 deliver_time = 52;
  // 更新时间戳，毫秒
  uint64 update_time = 53;
  // 删除时间戳，毫秒
  uint64 delete_time = 54;

  // 元信息，不同的 ContentType 有不同的元信息结构
  // 这里套一层是为了让所有的 metadata 都被收到 metadata 字段中，而不是平铺在上一层
  message Metadata {
    // 元信息
    oneof metadata {
      // 纯文本
      MetadataPlaintext plaintext = 1;
      // 图片
      MetadataPicture picture = 2;
    }
  }

  // 纯文本类型的元信息
  message MetadataPlaintext {
    // map of variable key to its value.
    map<string, string> variables = 1;
  }

  // 图片类型的元信息
  message MetadataPicture {
    // MIME type
    string mime_type = 1;
    // 图片宽度，像素
    uint32 width = 2;
    // 图片高度，像素
    uint32 height = 3;
    // 图片大小， bytes
    uint32 file_size = 4;
  }
  // display extra info
  message DisplayExtraInfo {
    // content extra info
    repeated ContentExtraInfo content_extra_infos = 1;

    // content extra info
    message ContentExtraInfo {
      // key
      string key = 1;
      // type
      Type type = 2;

      // metadata
      oneof metadata {
        // appointment
        Appointment appointment = 101;
        // agreement
        Agreement agreement = 102;
        // online booking
        OnlineBooking online_booking = 103;
        // COF
        COF cof = 104;
        // intake form
        IntakeForm intake_form = 105;
        // membership sell
        MembershipSell membership_sell = 106;
      }

      // buf:lint:ignore ENUM_ZERO_VALUE_SUFFIX
      // type of content
      enum Type {
        // unspecified
        UNSPECIFIED = 0;
        // text
        TEXT = 1;
        // link
        LINK = 2;
      }

      // appointment
      message Appointment {
        // appointment id
        int64 appointment_id = 1;
        // client appointment status
        client.appointment.v1.ClientAppointmentStatus client_appointment_status = 2;
      }

      // agreement
      message Agreement {
        // agreement id
        int64 agreement_id = 1;
        // agreement record id
        int64 agreement_record_id = 2;
      }

      // online booking
      message OnlineBooking {
        // business id
        int64 business_id = 1;
      }

      // COF link
      message COF {
        // customer code
        string customer_code = 1;
        // customer id
        int64 customer_id = 2;
      }

      // intake form
      message IntakeForm {
        // intake form id
        int64 intake_form_id = 1;
        // intake form uuid
        string intake_form_uuid = 2;
      }

      // membership sell
      message MembershipSell {
        // token
        string token = 1;
      }
    }
  }
}

// 对话的模型
message ChatModel {
  // 对话 ID
  uint64 id = 1;
  // 对话所属 Business 的 Company ID
  uint64 company_id = 2;
  // 对话所属 Business 的 ID
  uint64 business_id = 3;
  // 对话所属 Customer 的 ID
  uint64 customer_id = 4;

  // 对话的状态
  ChatStatus chat_status = 11;
  // 是否星标
  bool is_starred = 12;
  // 对话中最后一条消息的 ID
  uint64 last_msg_id = 21;
  // 对话中最后一条消息的创建时间
  uint64 last_msg_create_time = 22;
  // 打星标时间戳，毫秒
  uint64 star_time = 31;
  // 封锁时间戳，毫秒
  uint64 block_time = 32;
  // 创建时间戳，毫秒
  uint64 create_time = 33;
  // 更新时间戳，毫秒
  uint64 update_time = 34;
  // 删除时间戳，毫秒
  uint64 delete_time = 35;
}

// 对话的已读记录的模型
message RoleReadChatModel {
  // 无业务意义
  uint64 id = 1;
  // 对话 ID
  uint64 chat_id = 2;
  // 已读记录所属的角色
  Role role = 3;
  // 已读记录所属的 ID
  // 需要结合 Role 确定是什么 ID
  uint64 role_id = 4;

  // 最后的已读消息的 ID
  uint64 last_read_msg_id = 11;
  // 最后的已读时间
  uint64 last_read_time = 12;

  // 创建时间戳，毫秒
  uint64 create_time = 21;
  // 更新时间戳，毫秒
  uint64 update_time = 22;
  // 删除时间戳，毫秒
  uint64 delete_time = 23;
}

// 通知配置
message NotificationConfig {
  // 通知总开关
  bool enabled = 1;
  // 服务相关的通知渠道
  repeated ComplianceChannel service_related_channels = 2;
  // 营销活动的通知渠道
  repeated ComplianceChannel marketing_campaigns_channels = 3;
  // 品牌应用的通知渠道
  bool branded_app_enabled = 4;
}
