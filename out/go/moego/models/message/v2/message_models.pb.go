// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/message/v2/message_models.proto

package messagepb

import (
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/appointment/v1"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/message/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// buf:lint:ignore ENUM_ZERO_VALUE_SUFFIX
// type of content
type MessageModel_DisplayExtraInfo_ContentExtraInfo_Type int32

const (
	// unspecified
	MessageModel_DisplayExtraInfo_ContentExtraInfo_UNSPECIFIED MessageModel_DisplayExtraInfo_ContentExtraInfo_Type = 0
	// text
	MessageModel_DisplayExtraInfo_ContentExtraInfo_TEXT MessageModel_DisplayExtraInfo_ContentExtraInfo_Type = 1
	// link
	MessageModel_DisplayExtraInfo_ContentExtraInfo_LINK MessageModel_DisplayExtraInfo_ContentExtraInfo_Type = 2
)

// Enum value maps for MessageModel_DisplayExtraInfo_ContentExtraInfo_Type.
var (
	MessageModel_DisplayExtraInfo_ContentExtraInfo_Type_name = map[int32]string{
		0: "UNSPECIFIED",
		1: "TEXT",
		2: "LINK",
	}
	MessageModel_DisplayExtraInfo_ContentExtraInfo_Type_value = map[string]int32{
		"UNSPECIFIED": 0,
		"TEXT":        1,
		"LINK":        2,
	}
)

func (x MessageModel_DisplayExtraInfo_ContentExtraInfo_Type) Enum() *MessageModel_DisplayExtraInfo_ContentExtraInfo_Type {
	p := new(MessageModel_DisplayExtraInfo_ContentExtraInfo_Type)
	*p = x
	return p
}

func (x MessageModel_DisplayExtraInfo_ContentExtraInfo_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MessageModel_DisplayExtraInfo_ContentExtraInfo_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_message_v2_message_models_proto_enumTypes[0].Descriptor()
}

func (MessageModel_DisplayExtraInfo_ContentExtraInfo_Type) Type() protoreflect.EnumType {
	return &file_moego_models_message_v2_message_models_proto_enumTypes[0]
}

func (x MessageModel_DisplayExtraInfo_ContentExtraInfo_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MessageModel_DisplayExtraInfo_ContentExtraInfo_Type.Descriptor instead.
func (MessageModel_DisplayExtraInfo_ContentExtraInfo_Type) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_message_v2_message_models_proto_rawDescGZIP(), []int{0, 3, 0, 0}
}

// 消息模型
type MessageModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 消息 ID
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// UUID，用于处理幂等逻辑。使用 UUID v7
	Uuid string `protobuf:"bytes,2,opt,name=uuid,proto3" json:"uuid,omitempty"`
	// 消息所属对话的 ID
	ChatId uint64 `protobuf:"varint,3,opt,name=chat_id,json=chatId,proto3" json:"chat_id,omitempty"`
	// 消息所属 Business 的 Company ID
	CompanyId uint64 `protobuf:"varint,4,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// 消息所属 Business 的 ID
	BusinessId uint64 `protobuf:"varint,5,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// 消息所属 Customer 的 ID
	CustomerId uint64 `protobuf:"varint,6,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// 发送方的角色
	SenderRole Role `protobuf:"varint,11,opt,name=sender_role,json=senderRole,proto3,enum=moego.models.message.v2.Role" json:"sender_role,omitempty"`
	// 发送方的 ID
	// 根据 Role 来确定 ID 是什么 ID
	SenderId uint64 `protobuf:"varint,12,opt,name=sender_id,json=senderId,proto3" json:"sender_id,omitempty"`
	// 接收方的角色
	ReceiverRole Role `protobuf:"varint,13,opt,name=receiver_role,json=receiverRole,proto3,enum=moego.models.message.v2.Role" json:"receiver_role,omitempty"`
	// 接收方的 ID
	// 根据 Role 来确定 ID 是什么 ID
	ReceiverId uint64 `protobuf:"varint,14,opt,name=receiver_id,json=receiverId,proto3" json:"receiver_id,omitempty"`
	// 消息投送的渠道
	Channel Channel `protobuf:"varint,21,opt,name=channel,proto3,enum=moego.models.message.v2.Channel" json:"channel,omitempty"`
	// 消息内容，为了兼容图片等会把链接放在这里
	Content string `protobuf:"bytes,22,opt,name=content,proto3" json:"content,omitempty"`
	// 消息内容的版本号
	Version uint32 `protobuf:"varint,31,opt,name=version,proto3" json:"version,omitempty"`
	// 消息内容的类型
	ContentType ContentType `protobuf:"varint,32,opt,name=content_type,json=contentType,proto3,enum=moego.models.message.v2.ContentType" json:"content_type,omitempty"`
	// 消息状态，名字避开 DB 关键字
	MessageStatus MessageStatus `protobuf:"varint,33,opt,name=message_status,json=messageStatus,proto3,enum=moego.models.message.v2.MessageStatus" json:"message_status,omitempty"`
	// 投送失败的原因，仅当消息状态是投送失败时有值
	DeliverFailedReason string `protobuf:"bytes,41,opt,name=deliver_failed_reason,json=deliverFailedReason,proto3" json:"deliver_failed_reason,omitempty"`
	// 元信息
	Metadata *MessageModel_Metadata `protobuf:"bytes,42,opt,name=metadata,proto3" json:"metadata,omitempty"`
	// 展示用的额外信息
	DisplayExtraInfo *MessageModel_DisplayExtraInfo `protobuf:"bytes,43,opt,name=display_extra_info,json=displayExtraInfo,proto3" json:"display_extra_info,omitempty"`
	// target type
	TargetType v1.TargetType `protobuf:"varint,44,opt,name=target_type,json=targetType,proto3,enum=moego.models.message.v1.TargetType" json:"target_type,omitempty"`
	// 创建时间戳，毫秒
	CreateTime uint64 `protobuf:"varint,51,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// 投送时间戳，毫秒
	DeliverTime uint64 `protobuf:"varint,52,opt,name=deliver_time,json=deliverTime,proto3" json:"deliver_time,omitempty"`
	// 更新时间戳，毫秒
	UpdateTime uint64 `protobuf:"varint,53,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// 删除时间戳，毫秒
	DeleteTime uint64 `protobuf:"varint,54,opt,name=delete_time,json=deleteTime,proto3" json:"delete_time,omitempty"`
}

func (x *MessageModel) Reset() {
	*x = MessageModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_message_v2_message_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MessageModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageModel) ProtoMessage() {}

func (x *MessageModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_message_v2_message_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageModel.ProtoReflect.Descriptor instead.
func (*MessageModel) Descriptor() ([]byte, []int) {
	return file_moego_models_message_v2_message_models_proto_rawDescGZIP(), []int{0}
}

func (x *MessageModel) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *MessageModel) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

func (x *MessageModel) GetChatId() uint64 {
	if x != nil {
		return x.ChatId
	}
	return 0
}

func (x *MessageModel) GetCompanyId() uint64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *MessageModel) GetBusinessId() uint64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *MessageModel) GetCustomerId() uint64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *MessageModel) GetSenderRole() Role {
	if x != nil {
		return x.SenderRole
	}
	return Role_ROLE_UNSPECIFIED
}

func (x *MessageModel) GetSenderId() uint64 {
	if x != nil {
		return x.SenderId
	}
	return 0
}

func (x *MessageModel) GetReceiverRole() Role {
	if x != nil {
		return x.ReceiverRole
	}
	return Role_ROLE_UNSPECIFIED
}

func (x *MessageModel) GetReceiverId() uint64 {
	if x != nil {
		return x.ReceiverId
	}
	return 0
}

func (x *MessageModel) GetChannel() Channel {
	if x != nil {
		return x.Channel
	}
	return Channel_CHANNEL_UNSPECIFIED
}

func (x *MessageModel) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *MessageModel) GetVersion() uint32 {
	if x != nil {
		return x.Version
	}
	return 0
}

func (x *MessageModel) GetContentType() ContentType {
	if x != nil {
		return x.ContentType
	}
	return ContentType_CONTENT_TYPE_UNSPECIFIED
}

func (x *MessageModel) GetMessageStatus() MessageStatus {
	if x != nil {
		return x.MessageStatus
	}
	return MessageStatus_MSG_STATUS_UNSPECIFIED
}

func (x *MessageModel) GetDeliverFailedReason() string {
	if x != nil {
		return x.DeliverFailedReason
	}
	return ""
}

func (x *MessageModel) GetMetadata() *MessageModel_Metadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *MessageModel) GetDisplayExtraInfo() *MessageModel_DisplayExtraInfo {
	if x != nil {
		return x.DisplayExtraInfo
	}
	return nil
}

func (x *MessageModel) GetTargetType() v1.TargetType {
	if x != nil {
		return x.TargetType
	}
	return v1.TargetType(0)
}

func (x *MessageModel) GetCreateTime() uint64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *MessageModel) GetDeliverTime() uint64 {
	if x != nil {
		return x.DeliverTime
	}
	return 0
}

func (x *MessageModel) GetUpdateTime() uint64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *MessageModel) GetDeleteTime() uint64 {
	if x != nil {
		return x.DeleteTime
	}
	return 0
}

// 对话的模型
type ChatModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 对话 ID
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 对话所属 Business 的 Company ID
	CompanyId uint64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// 对话所属 Business 的 ID
	BusinessId uint64 `protobuf:"varint,3,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// 对话所属 Customer 的 ID
	CustomerId uint64 `protobuf:"varint,4,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// 对话的状态
	ChatStatus ChatStatus `protobuf:"varint,11,opt,name=chat_status,json=chatStatus,proto3,enum=moego.models.message.v2.ChatStatus" json:"chat_status,omitempty"`
	// 是否星标
	IsStarred bool `protobuf:"varint,12,opt,name=is_starred,json=isStarred,proto3" json:"is_starred,omitempty"`
	// 对话中最后一条消息的 ID
	LastMsgId uint64 `protobuf:"varint,21,opt,name=last_msg_id,json=lastMsgId,proto3" json:"last_msg_id,omitempty"`
	// 对话中最后一条消息的创建时间
	LastMsgCreateTime uint64 `protobuf:"varint,22,opt,name=last_msg_create_time,json=lastMsgCreateTime,proto3" json:"last_msg_create_time,omitempty"`
	// 打星标时间戳，毫秒
	StarTime uint64 `protobuf:"varint,31,opt,name=star_time,json=starTime,proto3" json:"star_time,omitempty"`
	// 封锁时间戳，毫秒
	BlockTime uint64 `protobuf:"varint,32,opt,name=block_time,json=blockTime,proto3" json:"block_time,omitempty"`
	// 创建时间戳，毫秒
	CreateTime uint64 `protobuf:"varint,33,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// 更新时间戳，毫秒
	UpdateTime uint64 `protobuf:"varint,34,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// 删除时间戳，毫秒
	DeleteTime uint64 `protobuf:"varint,35,opt,name=delete_time,json=deleteTime,proto3" json:"delete_time,omitempty"`
}

func (x *ChatModel) Reset() {
	*x = ChatModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_message_v2_message_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChatModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChatModel) ProtoMessage() {}

func (x *ChatModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_message_v2_message_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChatModel.ProtoReflect.Descriptor instead.
func (*ChatModel) Descriptor() ([]byte, []int) {
	return file_moego_models_message_v2_message_models_proto_rawDescGZIP(), []int{1}
}

func (x *ChatModel) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ChatModel) GetCompanyId() uint64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *ChatModel) GetBusinessId() uint64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *ChatModel) GetCustomerId() uint64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *ChatModel) GetChatStatus() ChatStatus {
	if x != nil {
		return x.ChatStatus
	}
	return ChatStatus_CHAT_STATUS_UNSPECIFIED
}

func (x *ChatModel) GetIsStarred() bool {
	if x != nil {
		return x.IsStarred
	}
	return false
}

func (x *ChatModel) GetLastMsgId() uint64 {
	if x != nil {
		return x.LastMsgId
	}
	return 0
}

func (x *ChatModel) GetLastMsgCreateTime() uint64 {
	if x != nil {
		return x.LastMsgCreateTime
	}
	return 0
}

func (x *ChatModel) GetStarTime() uint64 {
	if x != nil {
		return x.StarTime
	}
	return 0
}

func (x *ChatModel) GetBlockTime() uint64 {
	if x != nil {
		return x.BlockTime
	}
	return 0
}

func (x *ChatModel) GetCreateTime() uint64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *ChatModel) GetUpdateTime() uint64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *ChatModel) GetDeleteTime() uint64 {
	if x != nil {
		return x.DeleteTime
	}
	return 0
}

// 对话的已读记录的模型
type RoleReadChatModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 无业务意义
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 对话 ID
	ChatId uint64 `protobuf:"varint,2,opt,name=chat_id,json=chatId,proto3" json:"chat_id,omitempty"`
	// 已读记录所属的角色
	Role Role `protobuf:"varint,3,opt,name=role,proto3,enum=moego.models.message.v2.Role" json:"role,omitempty"`
	// 已读记录所属的 ID
	// 需要结合 Role 确定是什么 ID
	RoleId uint64 `protobuf:"varint,4,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	// 最后的已读消息的 ID
	LastReadMsgId uint64 `protobuf:"varint,11,opt,name=last_read_msg_id,json=lastReadMsgId,proto3" json:"last_read_msg_id,omitempty"`
	// 最后的已读时间
	LastReadTime uint64 `protobuf:"varint,12,opt,name=last_read_time,json=lastReadTime,proto3" json:"last_read_time,omitempty"`
	// 创建时间戳，毫秒
	CreateTime uint64 `protobuf:"varint,21,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// 更新时间戳，毫秒
	UpdateTime uint64 `protobuf:"varint,22,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// 删除时间戳，毫秒
	DeleteTime uint64 `protobuf:"varint,23,opt,name=delete_time,json=deleteTime,proto3" json:"delete_time,omitempty"`
}

func (x *RoleReadChatModel) Reset() {
	*x = RoleReadChatModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_message_v2_message_models_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RoleReadChatModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RoleReadChatModel) ProtoMessage() {}

func (x *RoleReadChatModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_message_v2_message_models_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RoleReadChatModel.ProtoReflect.Descriptor instead.
func (*RoleReadChatModel) Descriptor() ([]byte, []int) {
	return file_moego_models_message_v2_message_models_proto_rawDescGZIP(), []int{2}
}

func (x *RoleReadChatModel) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *RoleReadChatModel) GetChatId() uint64 {
	if x != nil {
		return x.ChatId
	}
	return 0
}

func (x *RoleReadChatModel) GetRole() Role {
	if x != nil {
		return x.Role
	}
	return Role_ROLE_UNSPECIFIED
}

func (x *RoleReadChatModel) GetRoleId() uint64 {
	if x != nil {
		return x.RoleId
	}
	return 0
}

func (x *RoleReadChatModel) GetLastReadMsgId() uint64 {
	if x != nil {
		return x.LastReadMsgId
	}
	return 0
}

func (x *RoleReadChatModel) GetLastReadTime() uint64 {
	if x != nil {
		return x.LastReadTime
	}
	return 0
}

func (x *RoleReadChatModel) GetCreateTime() uint64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *RoleReadChatModel) GetUpdateTime() uint64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *RoleReadChatModel) GetDeleteTime() uint64 {
	if x != nil {
		return x.DeleteTime
	}
	return 0
}

// 通知配置
type NotificationConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 通知总开关
	Enabled bool `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled,omitempty"`
	// 服务相关的通知渠道
	ServiceRelatedChannels []ComplianceChannel `protobuf:"varint,2,rep,packed,name=service_related_channels,json=serviceRelatedChannels,proto3,enum=moego.models.message.v2.ComplianceChannel" json:"service_related_channels,omitempty"`
	// 营销活动的通知渠道
	MarketingCampaignsChannels []ComplianceChannel `protobuf:"varint,3,rep,packed,name=marketing_campaigns_channels,json=marketingCampaignsChannels,proto3,enum=moego.models.message.v2.ComplianceChannel" json:"marketing_campaigns_channels,omitempty"`
	// 品牌应用的通知渠道
	BrandedAppEnabled bool `protobuf:"varint,4,opt,name=branded_app_enabled,json=brandedAppEnabled,proto3" json:"branded_app_enabled,omitempty"`
}

func (x *NotificationConfig) Reset() {
	*x = NotificationConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_message_v2_message_models_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NotificationConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NotificationConfig) ProtoMessage() {}

func (x *NotificationConfig) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_message_v2_message_models_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NotificationConfig.ProtoReflect.Descriptor instead.
func (*NotificationConfig) Descriptor() ([]byte, []int) {
	return file_moego_models_message_v2_message_models_proto_rawDescGZIP(), []int{3}
}

func (x *NotificationConfig) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

func (x *NotificationConfig) GetServiceRelatedChannels() []ComplianceChannel {
	if x != nil {
		return x.ServiceRelatedChannels
	}
	return nil
}

func (x *NotificationConfig) GetMarketingCampaignsChannels() []ComplianceChannel {
	if x != nil {
		return x.MarketingCampaignsChannels
	}
	return nil
}

func (x *NotificationConfig) GetBrandedAppEnabled() bool {
	if x != nil {
		return x.BrandedAppEnabled
	}
	return false
}

// 元信息，不同的 ContentType 有不同的元信息结构
// 这里套一层是为了让所有的 metadata 都被收到 metadata 字段中，而不是平铺在上一层
type MessageModel_Metadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 元信息
	//
	// Types that are assignable to Metadata:
	//
	//	*MessageModel_Metadata_Plaintext
	//	*MessageModel_Metadata_Picture
	Metadata isMessageModel_Metadata_Metadata `protobuf_oneof:"metadata"`
}

func (x *MessageModel_Metadata) Reset() {
	*x = MessageModel_Metadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_message_v2_message_models_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MessageModel_Metadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageModel_Metadata) ProtoMessage() {}

func (x *MessageModel_Metadata) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_message_v2_message_models_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageModel_Metadata.ProtoReflect.Descriptor instead.
func (*MessageModel_Metadata) Descriptor() ([]byte, []int) {
	return file_moego_models_message_v2_message_models_proto_rawDescGZIP(), []int{0, 0}
}

func (m *MessageModel_Metadata) GetMetadata() isMessageModel_Metadata_Metadata {
	if m != nil {
		return m.Metadata
	}
	return nil
}

func (x *MessageModel_Metadata) GetPlaintext() *MessageModel_MetadataPlaintext {
	if x, ok := x.GetMetadata().(*MessageModel_Metadata_Plaintext); ok {
		return x.Plaintext
	}
	return nil
}

func (x *MessageModel_Metadata) GetPicture() *MessageModel_MetadataPicture {
	if x, ok := x.GetMetadata().(*MessageModel_Metadata_Picture); ok {
		return x.Picture
	}
	return nil
}

type isMessageModel_Metadata_Metadata interface {
	isMessageModel_Metadata_Metadata()
}

type MessageModel_Metadata_Plaintext struct {
	// 纯文本
	Plaintext *MessageModel_MetadataPlaintext `protobuf:"bytes,1,opt,name=plaintext,proto3,oneof"`
}

type MessageModel_Metadata_Picture struct {
	// 图片
	Picture *MessageModel_MetadataPicture `protobuf:"bytes,2,opt,name=picture,proto3,oneof"`
}

func (*MessageModel_Metadata_Plaintext) isMessageModel_Metadata_Metadata() {}

func (*MessageModel_Metadata_Picture) isMessageModel_Metadata_Metadata() {}

// 纯文本类型的元信息
type MessageModel_MetadataPlaintext struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// map of variable key to its value.
	Variables map[string]string `protobuf:"bytes,1,rep,name=variables,proto3" json:"variables,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *MessageModel_MetadataPlaintext) Reset() {
	*x = MessageModel_MetadataPlaintext{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_message_v2_message_models_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MessageModel_MetadataPlaintext) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageModel_MetadataPlaintext) ProtoMessage() {}

func (x *MessageModel_MetadataPlaintext) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_message_v2_message_models_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageModel_MetadataPlaintext.ProtoReflect.Descriptor instead.
func (*MessageModel_MetadataPlaintext) Descriptor() ([]byte, []int) {
	return file_moego_models_message_v2_message_models_proto_rawDescGZIP(), []int{0, 1}
}

func (x *MessageModel_MetadataPlaintext) GetVariables() map[string]string {
	if x != nil {
		return x.Variables
	}
	return nil
}

// 图片类型的元信息
type MessageModel_MetadataPicture struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// MIME type
	MimeType string `protobuf:"bytes,1,opt,name=mime_type,json=mimeType,proto3" json:"mime_type,omitempty"`
	// 图片宽度，像素
	Width uint32 `protobuf:"varint,2,opt,name=width,proto3" json:"width,omitempty"`
	// 图片高度，像素
	Height uint32 `protobuf:"varint,3,opt,name=height,proto3" json:"height,omitempty"`
	// 图片大小， bytes
	FileSize uint32 `protobuf:"varint,4,opt,name=file_size,json=fileSize,proto3" json:"file_size,omitempty"`
}

func (x *MessageModel_MetadataPicture) Reset() {
	*x = MessageModel_MetadataPicture{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_message_v2_message_models_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MessageModel_MetadataPicture) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageModel_MetadataPicture) ProtoMessage() {}

func (x *MessageModel_MetadataPicture) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_message_v2_message_models_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageModel_MetadataPicture.ProtoReflect.Descriptor instead.
func (*MessageModel_MetadataPicture) Descriptor() ([]byte, []int) {
	return file_moego_models_message_v2_message_models_proto_rawDescGZIP(), []int{0, 2}
}

func (x *MessageModel_MetadataPicture) GetMimeType() string {
	if x != nil {
		return x.MimeType
	}
	return ""
}

func (x *MessageModel_MetadataPicture) GetWidth() uint32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *MessageModel_MetadataPicture) GetHeight() uint32 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *MessageModel_MetadataPicture) GetFileSize() uint32 {
	if x != nil {
		return x.FileSize
	}
	return 0
}

// display extra info
type MessageModel_DisplayExtraInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// content extra info
	ContentExtraInfos []*MessageModel_DisplayExtraInfo_ContentExtraInfo `protobuf:"bytes,1,rep,name=content_extra_infos,json=contentExtraInfos,proto3" json:"content_extra_infos,omitempty"`
}

func (x *MessageModel_DisplayExtraInfo) Reset() {
	*x = MessageModel_DisplayExtraInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_message_v2_message_models_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MessageModel_DisplayExtraInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageModel_DisplayExtraInfo) ProtoMessage() {}

func (x *MessageModel_DisplayExtraInfo) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_message_v2_message_models_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageModel_DisplayExtraInfo.ProtoReflect.Descriptor instead.
func (*MessageModel_DisplayExtraInfo) Descriptor() ([]byte, []int) {
	return file_moego_models_message_v2_message_models_proto_rawDescGZIP(), []int{0, 3}
}

func (x *MessageModel_DisplayExtraInfo) GetContentExtraInfos() []*MessageModel_DisplayExtraInfo_ContentExtraInfo {
	if x != nil {
		return x.ContentExtraInfos
	}
	return nil
}

// content extra info
type MessageModel_DisplayExtraInfo_ContentExtraInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// key
	Key string `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	// type
	Type MessageModel_DisplayExtraInfo_ContentExtraInfo_Type `protobuf:"varint,2,opt,name=type,proto3,enum=moego.models.message.v2.MessageModel_DisplayExtraInfo_ContentExtraInfo_Type" json:"type,omitempty"`
	// metadata
	//
	// Types that are assignable to Metadata:
	//
	//	*MessageModel_DisplayExtraInfo_ContentExtraInfo_Appointment_
	//	*MessageModel_DisplayExtraInfo_ContentExtraInfo_Agreement_
	//	*MessageModel_DisplayExtraInfo_ContentExtraInfo_OnlineBooking_
	//	*MessageModel_DisplayExtraInfo_ContentExtraInfo_Cof
	//	*MessageModel_DisplayExtraInfo_ContentExtraInfo_IntakeForm_
	//	*MessageModel_DisplayExtraInfo_ContentExtraInfo_MembershipSell_
	Metadata isMessageModel_DisplayExtraInfo_ContentExtraInfo_Metadata `protobuf_oneof:"metadata"`
}

func (x *MessageModel_DisplayExtraInfo_ContentExtraInfo) Reset() {
	*x = MessageModel_DisplayExtraInfo_ContentExtraInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_message_v2_message_models_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MessageModel_DisplayExtraInfo_ContentExtraInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageModel_DisplayExtraInfo_ContentExtraInfo) ProtoMessage() {}

func (x *MessageModel_DisplayExtraInfo_ContentExtraInfo) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_message_v2_message_models_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageModel_DisplayExtraInfo_ContentExtraInfo.ProtoReflect.Descriptor instead.
func (*MessageModel_DisplayExtraInfo_ContentExtraInfo) Descriptor() ([]byte, []int) {
	return file_moego_models_message_v2_message_models_proto_rawDescGZIP(), []int{0, 3, 0}
}

func (x *MessageModel_DisplayExtraInfo_ContentExtraInfo) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *MessageModel_DisplayExtraInfo_ContentExtraInfo) GetType() MessageModel_DisplayExtraInfo_ContentExtraInfo_Type {
	if x != nil {
		return x.Type
	}
	return MessageModel_DisplayExtraInfo_ContentExtraInfo_UNSPECIFIED
}

func (m *MessageModel_DisplayExtraInfo_ContentExtraInfo) GetMetadata() isMessageModel_DisplayExtraInfo_ContentExtraInfo_Metadata {
	if m != nil {
		return m.Metadata
	}
	return nil
}

func (x *MessageModel_DisplayExtraInfo_ContentExtraInfo) GetAppointment() *MessageModel_DisplayExtraInfo_ContentExtraInfo_Appointment {
	if x, ok := x.GetMetadata().(*MessageModel_DisplayExtraInfo_ContentExtraInfo_Appointment_); ok {
		return x.Appointment
	}
	return nil
}

func (x *MessageModel_DisplayExtraInfo_ContentExtraInfo) GetAgreement() *MessageModel_DisplayExtraInfo_ContentExtraInfo_Agreement {
	if x, ok := x.GetMetadata().(*MessageModel_DisplayExtraInfo_ContentExtraInfo_Agreement_); ok {
		return x.Agreement
	}
	return nil
}

func (x *MessageModel_DisplayExtraInfo_ContentExtraInfo) GetOnlineBooking() *MessageModel_DisplayExtraInfo_ContentExtraInfo_OnlineBooking {
	if x, ok := x.GetMetadata().(*MessageModel_DisplayExtraInfo_ContentExtraInfo_OnlineBooking_); ok {
		return x.OnlineBooking
	}
	return nil
}

func (x *MessageModel_DisplayExtraInfo_ContentExtraInfo) GetCof() *MessageModel_DisplayExtraInfo_ContentExtraInfo_COF {
	if x, ok := x.GetMetadata().(*MessageModel_DisplayExtraInfo_ContentExtraInfo_Cof); ok {
		return x.Cof
	}
	return nil
}

func (x *MessageModel_DisplayExtraInfo_ContentExtraInfo) GetIntakeForm() *MessageModel_DisplayExtraInfo_ContentExtraInfo_IntakeForm {
	if x, ok := x.GetMetadata().(*MessageModel_DisplayExtraInfo_ContentExtraInfo_IntakeForm_); ok {
		return x.IntakeForm
	}
	return nil
}

func (x *MessageModel_DisplayExtraInfo_ContentExtraInfo) GetMembershipSell() *MessageModel_DisplayExtraInfo_ContentExtraInfo_MembershipSell {
	if x, ok := x.GetMetadata().(*MessageModel_DisplayExtraInfo_ContentExtraInfo_MembershipSell_); ok {
		return x.MembershipSell
	}
	return nil
}

type isMessageModel_DisplayExtraInfo_ContentExtraInfo_Metadata interface {
	isMessageModel_DisplayExtraInfo_ContentExtraInfo_Metadata()
}

type MessageModel_DisplayExtraInfo_ContentExtraInfo_Appointment_ struct {
	// appointment
	Appointment *MessageModel_DisplayExtraInfo_ContentExtraInfo_Appointment `protobuf:"bytes,101,opt,name=appointment,proto3,oneof"`
}

type MessageModel_DisplayExtraInfo_ContentExtraInfo_Agreement_ struct {
	// agreement
	Agreement *MessageModel_DisplayExtraInfo_ContentExtraInfo_Agreement `protobuf:"bytes,102,opt,name=agreement,proto3,oneof"`
}

type MessageModel_DisplayExtraInfo_ContentExtraInfo_OnlineBooking_ struct {
	// online booking
	OnlineBooking *MessageModel_DisplayExtraInfo_ContentExtraInfo_OnlineBooking `protobuf:"bytes,103,opt,name=online_booking,json=onlineBooking,proto3,oneof"`
}

type MessageModel_DisplayExtraInfo_ContentExtraInfo_Cof struct {
	// COF
	Cof *MessageModel_DisplayExtraInfo_ContentExtraInfo_COF `protobuf:"bytes,104,opt,name=cof,proto3,oneof"`
}

type MessageModel_DisplayExtraInfo_ContentExtraInfo_IntakeForm_ struct {
	// intake form
	IntakeForm *MessageModel_DisplayExtraInfo_ContentExtraInfo_IntakeForm `protobuf:"bytes,105,opt,name=intake_form,json=intakeForm,proto3,oneof"`
}

type MessageModel_DisplayExtraInfo_ContentExtraInfo_MembershipSell_ struct {
	// membership sell
	MembershipSell *MessageModel_DisplayExtraInfo_ContentExtraInfo_MembershipSell `protobuf:"bytes,106,opt,name=membership_sell,json=membershipSell,proto3,oneof"`
}

func (*MessageModel_DisplayExtraInfo_ContentExtraInfo_Appointment_) isMessageModel_DisplayExtraInfo_ContentExtraInfo_Metadata() {
}

func (*MessageModel_DisplayExtraInfo_ContentExtraInfo_Agreement_) isMessageModel_DisplayExtraInfo_ContentExtraInfo_Metadata() {
}

func (*MessageModel_DisplayExtraInfo_ContentExtraInfo_OnlineBooking_) isMessageModel_DisplayExtraInfo_ContentExtraInfo_Metadata() {
}

func (*MessageModel_DisplayExtraInfo_ContentExtraInfo_Cof) isMessageModel_DisplayExtraInfo_ContentExtraInfo_Metadata() {
}

func (*MessageModel_DisplayExtraInfo_ContentExtraInfo_IntakeForm_) isMessageModel_DisplayExtraInfo_ContentExtraInfo_Metadata() {
}

func (*MessageModel_DisplayExtraInfo_ContentExtraInfo_MembershipSell_) isMessageModel_DisplayExtraInfo_ContentExtraInfo_Metadata() {
}

// appointment
type MessageModel_DisplayExtraInfo_ContentExtraInfo_Appointment struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment id
	AppointmentId int64 `protobuf:"varint,1,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// client appointment status
	ClientAppointmentStatus v11.ClientAppointmentStatus `protobuf:"varint,2,opt,name=client_appointment_status,json=clientAppointmentStatus,proto3,enum=moego.client.appointment.v1.ClientAppointmentStatus" json:"client_appointment_status,omitempty"`
}

func (x *MessageModel_DisplayExtraInfo_ContentExtraInfo_Appointment) Reset() {
	*x = MessageModel_DisplayExtraInfo_ContentExtraInfo_Appointment{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_message_v2_message_models_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MessageModel_DisplayExtraInfo_ContentExtraInfo_Appointment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageModel_DisplayExtraInfo_ContentExtraInfo_Appointment) ProtoMessage() {}

func (x *MessageModel_DisplayExtraInfo_ContentExtraInfo_Appointment) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_message_v2_message_models_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageModel_DisplayExtraInfo_ContentExtraInfo_Appointment.ProtoReflect.Descriptor instead.
func (*MessageModel_DisplayExtraInfo_ContentExtraInfo_Appointment) Descriptor() ([]byte, []int) {
	return file_moego_models_message_v2_message_models_proto_rawDescGZIP(), []int{0, 3, 0, 0}
}

func (x *MessageModel_DisplayExtraInfo_ContentExtraInfo_Appointment) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *MessageModel_DisplayExtraInfo_ContentExtraInfo_Appointment) GetClientAppointmentStatus() v11.ClientAppointmentStatus {
	if x != nil {
		return x.ClientAppointmentStatus
	}
	return v11.ClientAppointmentStatus(0)
}

// agreement
type MessageModel_DisplayExtraInfo_ContentExtraInfo_Agreement struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// agreement id
	AgreementId int64 `protobuf:"varint,1,opt,name=agreement_id,json=agreementId,proto3" json:"agreement_id,omitempty"`
	// agreement record id
	AgreementRecordId int64 `protobuf:"varint,2,opt,name=agreement_record_id,json=agreementRecordId,proto3" json:"agreement_record_id,omitempty"`
}

func (x *MessageModel_DisplayExtraInfo_ContentExtraInfo_Agreement) Reset() {
	*x = MessageModel_DisplayExtraInfo_ContentExtraInfo_Agreement{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_message_v2_message_models_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MessageModel_DisplayExtraInfo_ContentExtraInfo_Agreement) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageModel_DisplayExtraInfo_ContentExtraInfo_Agreement) ProtoMessage() {}

func (x *MessageModel_DisplayExtraInfo_ContentExtraInfo_Agreement) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_message_v2_message_models_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageModel_DisplayExtraInfo_ContentExtraInfo_Agreement.ProtoReflect.Descriptor instead.
func (*MessageModel_DisplayExtraInfo_ContentExtraInfo_Agreement) Descriptor() ([]byte, []int) {
	return file_moego_models_message_v2_message_models_proto_rawDescGZIP(), []int{0, 3, 0, 1}
}

func (x *MessageModel_DisplayExtraInfo_ContentExtraInfo_Agreement) GetAgreementId() int64 {
	if x != nil {
		return x.AgreementId
	}
	return 0
}

func (x *MessageModel_DisplayExtraInfo_ContentExtraInfo_Agreement) GetAgreementRecordId() int64 {
	if x != nil {
		return x.AgreementRecordId
	}
	return 0
}

// online booking
type MessageModel_DisplayExtraInfo_ContentExtraInfo_OnlineBooking struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
}

func (x *MessageModel_DisplayExtraInfo_ContentExtraInfo_OnlineBooking) Reset() {
	*x = MessageModel_DisplayExtraInfo_ContentExtraInfo_OnlineBooking{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_message_v2_message_models_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MessageModel_DisplayExtraInfo_ContentExtraInfo_OnlineBooking) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageModel_DisplayExtraInfo_ContentExtraInfo_OnlineBooking) ProtoMessage() {}

func (x *MessageModel_DisplayExtraInfo_ContentExtraInfo_OnlineBooking) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_message_v2_message_models_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageModel_DisplayExtraInfo_ContentExtraInfo_OnlineBooking.ProtoReflect.Descriptor instead.
func (*MessageModel_DisplayExtraInfo_ContentExtraInfo_OnlineBooking) Descriptor() ([]byte, []int) {
	return file_moego_models_message_v2_message_models_proto_rawDescGZIP(), []int{0, 3, 0, 2}
}

func (x *MessageModel_DisplayExtraInfo_ContentExtraInfo_OnlineBooking) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

// COF link
type MessageModel_DisplayExtraInfo_ContentExtraInfo_COF struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// customer code
	CustomerCode string `protobuf:"bytes,1,opt,name=customer_code,json=customerCode,proto3" json:"customer_code,omitempty"`
	// customer id
	CustomerId int64 `protobuf:"varint,2,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
}

func (x *MessageModel_DisplayExtraInfo_ContentExtraInfo_COF) Reset() {
	*x = MessageModel_DisplayExtraInfo_ContentExtraInfo_COF{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_message_v2_message_models_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MessageModel_DisplayExtraInfo_ContentExtraInfo_COF) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageModel_DisplayExtraInfo_ContentExtraInfo_COF) ProtoMessage() {}

func (x *MessageModel_DisplayExtraInfo_ContentExtraInfo_COF) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_message_v2_message_models_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageModel_DisplayExtraInfo_ContentExtraInfo_COF.ProtoReflect.Descriptor instead.
func (*MessageModel_DisplayExtraInfo_ContentExtraInfo_COF) Descriptor() ([]byte, []int) {
	return file_moego_models_message_v2_message_models_proto_rawDescGZIP(), []int{0, 3, 0, 3}
}

func (x *MessageModel_DisplayExtraInfo_ContentExtraInfo_COF) GetCustomerCode() string {
	if x != nil {
		return x.CustomerCode
	}
	return ""
}

func (x *MessageModel_DisplayExtraInfo_ContentExtraInfo_COF) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

// intake form
type MessageModel_DisplayExtraInfo_ContentExtraInfo_IntakeForm struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// intake form id
	IntakeFormId int64 `protobuf:"varint,1,opt,name=intake_form_id,json=intakeFormId,proto3" json:"intake_form_id,omitempty"`
	// intake form uuid
	IntakeFormUuid string `protobuf:"bytes,2,opt,name=intake_form_uuid,json=intakeFormUuid,proto3" json:"intake_form_uuid,omitempty"`
}

func (x *MessageModel_DisplayExtraInfo_ContentExtraInfo_IntakeForm) Reset() {
	*x = MessageModel_DisplayExtraInfo_ContentExtraInfo_IntakeForm{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_message_v2_message_models_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MessageModel_DisplayExtraInfo_ContentExtraInfo_IntakeForm) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageModel_DisplayExtraInfo_ContentExtraInfo_IntakeForm) ProtoMessage() {}

func (x *MessageModel_DisplayExtraInfo_ContentExtraInfo_IntakeForm) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_message_v2_message_models_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageModel_DisplayExtraInfo_ContentExtraInfo_IntakeForm.ProtoReflect.Descriptor instead.
func (*MessageModel_DisplayExtraInfo_ContentExtraInfo_IntakeForm) Descriptor() ([]byte, []int) {
	return file_moego_models_message_v2_message_models_proto_rawDescGZIP(), []int{0, 3, 0, 4}
}

func (x *MessageModel_DisplayExtraInfo_ContentExtraInfo_IntakeForm) GetIntakeFormId() int64 {
	if x != nil {
		return x.IntakeFormId
	}
	return 0
}

func (x *MessageModel_DisplayExtraInfo_ContentExtraInfo_IntakeForm) GetIntakeFormUuid() string {
	if x != nil {
		return x.IntakeFormUuid
	}
	return ""
}

// membership sell
type MessageModel_DisplayExtraInfo_ContentExtraInfo_MembershipSell struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// token
	Token string `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
}

func (x *MessageModel_DisplayExtraInfo_ContentExtraInfo_MembershipSell) Reset() {
	*x = MessageModel_DisplayExtraInfo_ContentExtraInfo_MembershipSell{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_message_v2_message_models_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MessageModel_DisplayExtraInfo_ContentExtraInfo_MembershipSell) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageModel_DisplayExtraInfo_ContentExtraInfo_MembershipSell) ProtoMessage() {}

func (x *MessageModel_DisplayExtraInfo_ContentExtraInfo_MembershipSell) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_message_v2_message_models_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageModel_DisplayExtraInfo_ContentExtraInfo_MembershipSell.ProtoReflect.Descriptor instead.
func (*MessageModel_DisplayExtraInfo_ContentExtraInfo_MembershipSell) Descriptor() ([]byte, []int) {
	return file_moego_models_message_v2_message_models_proto_rawDescGZIP(), []int{0, 3, 0, 5}
}

func (x *MessageModel_DisplayExtraInfo_ContentExtraInfo_MembershipSell) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

var File_moego_models_message_v2_message_models_proto protoreflect.FileDescriptor

var file_moego_models_message_v2_message_models_proto_rawDesc = []byte{
	0x0a, 0x2c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2f, 0x76, 0x32, 0x2f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x17,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x32, 0x1a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2f, 0x76, 0x32,
	0x2f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0xc8, 0x18, 0x0a, 0x0c, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x75, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x75, 0x69, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x68, 0x61,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x63, 0x68, 0x61, 0x74,
	0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49,
	0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x3e, 0x0a, 0x0b, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x5f, 0x72, 0x6f,
	0x6c, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e,
	0x76, 0x32, 0x2e, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x0a, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x52,
	0x6f, 0x6c, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x42, 0x0a, 0x0d, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x5f, 0x72, 0x6f, 0x6c,
	0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76,
	0x32, 0x2e, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x0c, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72,
	0x52, 0x6f, 0x6c, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x72, 0x65, 0x63, 0x65, 0x69,
	0x76, 0x65, 0x72, 0x49, 0x64, 0x12, 0x3a, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x18, 0x15, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x32,
	0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x16, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x47, 0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x20, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x4d,
	0x0a, 0x0e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x21, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x32,
	0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0d,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x32, 0x0a,
	0x15, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x5f, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x5f,
	0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x29, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x64, 0x65,
	0x6c, 0x69, 0x76, 0x65, 0x72, 0x46, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x52, 0x65, 0x61, 0x73, 0x6f,
	0x6e, 0x12, 0x4a, 0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x2a, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x64, 0x0a,
	0x12, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x65, 0x78, 0x74, 0x72, 0x61, 0x5f, 0x69,
	0x6e, 0x66, 0x6f, 0x18, 0x2b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x2e, 0x76, 0x32, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x2e, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x45, 0x78, 0x74, 0x72, 0x61, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x10, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x45, 0x78, 0x74, 0x72, 0x61, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x44, 0x0a, 0x0b, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x2c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x74,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x33, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x65,
	0x6c, 0x69, 0x76, 0x65, 0x72, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x34, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x0b, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a,
	0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x35, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f,
	0x0a, 0x0b, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x36, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x1a,
	0xc2, 0x01, 0x0a, 0x08, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x57, 0x0a, 0x09,
	0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x50,
	0x6c, 0x61, 0x69, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x48, 0x00, 0x52, 0x09, 0x70, 0x6c, 0x61, 0x69,
	0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x51, 0x0a, 0x07, 0x70, 0x69, 0x63, 0x74, 0x75, 0x72, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x32,
	0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x50, 0x69, 0x63, 0x74, 0x75, 0x72, 0x65, 0x48, 0x00, 0x52,
	0x07, 0x70, 0x69, 0x63, 0x74, 0x75, 0x72, 0x65, 0x42, 0x0a, 0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x1a, 0xb7, 0x01, 0x0a, 0x11, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0x50, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x64, 0x0a, 0x09, 0x76, 0x61,
	0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x46, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x50, 0x6c, 0x61,
	0x69, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x2e, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x09, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73,
	0x1a, 0x3c, 0x0a, 0x0e, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x79,
	0x0a, 0x0f, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x50, 0x69, 0x63, 0x74, 0x75, 0x72,
	0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x69, 0x6d, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x69, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x77,
	0x69, 0x64, 0x74, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x1b, 0x0a, 0x09,
	0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x08, 0x66, 0x69, 0x6c, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x1a, 0xa3, 0x0c, 0x0a, 0x10, 0x44, 0x69,
	0x73, 0x70, 0x6c, 0x61, 0x79, 0x45, 0x78, 0x74, 0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x77,
	0x0a, 0x13, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x65, 0x78, 0x74, 0x72, 0x61, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x47, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x2e, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x45, 0x78, 0x74, 0x72, 0x61, 0x49,
	0x6e, 0x66, 0x6f, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x45, 0x78, 0x74, 0x72, 0x61,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x11, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x45, 0x78, 0x74,
	0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x1a, 0x95, 0x0b, 0x0a, 0x10, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x45, 0x78, 0x74, 0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x60,
	0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x4c, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x2e, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x45, 0x78, 0x74, 0x72, 0x61,
	0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x45, 0x78, 0x74, 0x72,
	0x61, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x12, 0x77, 0x0a, 0x0b, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x18,
	0x65, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x53, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x32, 0x2e,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x44, 0x69, 0x73,
	0x70, 0x6c, 0x61, 0x79, 0x45, 0x78, 0x74, 0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x43, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x45, 0x78, 0x74, 0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x41,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x48, 0x00, 0x52, 0x0b, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x71, 0x0a, 0x09, 0x61, 0x67, 0x72,
	0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x66, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x51, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x2e, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x45, 0x78, 0x74, 0x72, 0x61,
	0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x45, 0x78, 0x74, 0x72,
	0x61, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x48,
	0x00, 0x52, 0x09, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x7e, 0x0a, 0x0e,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x18, 0x67,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x55, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x44, 0x69, 0x73, 0x70,
	0x6c, 0x61, 0x79, 0x45, 0x78, 0x74, 0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x45, 0x78, 0x74, 0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x4f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x48, 0x00, 0x52, 0x0d, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x12, 0x5f, 0x0a, 0x03,
	0x63, 0x6f, 0x66, 0x18, 0x68, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x4b, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x2e, 0x76, 0x32, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x2e, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x45, 0x78, 0x74, 0x72, 0x61, 0x49, 0x6e, 0x66,
	0x6f, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x45, 0x78, 0x74, 0x72, 0x61, 0x49, 0x6e,
	0x66, 0x6f, 0x2e, 0x43, 0x4f, 0x46, 0x48, 0x00, 0x52, 0x03, 0x63, 0x6f, 0x66, 0x12, 0x75, 0x0a,
	0x0b, 0x69, 0x6e, 0x74, 0x61, 0x6b, 0x65, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x69, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x52, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61,
	0x79, 0x45, 0x78, 0x74, 0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x45, 0x78, 0x74, 0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x49, 0x6e, 0x74, 0x61,
	0x6b, 0x65, 0x46, 0x6f, 0x72, 0x6d, 0x48, 0x00, 0x52, 0x0a, 0x69, 0x6e, 0x74, 0x61, 0x6b, 0x65,
	0x46, 0x6f, 0x72, 0x6d, 0x12, 0x81, 0x01, 0x0a, 0x0f, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73,
	0x68, 0x69, 0x70, 0x5f, 0x73, 0x65, 0x6c, 0x6c, 0x18, 0x6a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x56,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x45, 0x78, 0x74,
	0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x45, 0x78,
	0x74, 0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68,
	0x69, 0x70, 0x53, 0x65, 0x6c, 0x6c, 0x48, 0x00, 0x52, 0x0e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72,
	0x73, 0x68, 0x69, 0x70, 0x53, 0x65, 0x6c, 0x6c, 0x1a, 0xa6, 0x01, 0x0a, 0x0b, 0x41, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12,
	0x70, 0x0a, 0x19, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x17, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x1a, 0x5e, 0x0a, 0x09, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x21,
	0x0a, 0x0c, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x49,
	0x64, 0x12, 0x2e, 0x0a, 0x13, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x72,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x11,
	0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x49,
	0x64, 0x1a, 0x30, 0x0a, 0x0d, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x49, 0x64, 0x1a, 0x4b, 0x0a, 0x03, 0x43, 0x4f, 0x46, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64,
	0x1a, 0x5c, 0x0a, 0x0a, 0x49, 0x6e, 0x74, 0x61, 0x6b, 0x65, 0x46, 0x6f, 0x72, 0x6d, 0x12, 0x24,
	0x0a, 0x0e, 0x69, 0x6e, 0x74, 0x61, 0x6b, 0x65, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x69, 0x6e, 0x74, 0x61, 0x6b, 0x65, 0x46, 0x6f,
	0x72, 0x6d, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x10, 0x69, 0x6e, 0x74, 0x61, 0x6b, 0x65, 0x5f, 0x66,
	0x6f, 0x72, 0x6d, 0x5f, 0x75, 0x75, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e,
	0x69, 0x6e, 0x74, 0x61, 0x6b, 0x65, 0x46, 0x6f, 0x72, 0x6d, 0x55, 0x75, 0x69, 0x64, 0x1a, 0x26,
	0x0a, 0x0e, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x53, 0x65, 0x6c, 0x6c,
	0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x2b, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0f,
	0x0a, 0x0b, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x08, 0x0a, 0x04, 0x54, 0x45, 0x58, 0x54, 0x10, 0x01, 0x12, 0x08, 0x0a, 0x04, 0x4c, 0x49, 0x4e,
	0x4b, 0x10, 0x02, 0x42, 0x0a, 0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x22,
	0xd1, 0x03, 0x0a, 0x09, 0x43, 0x68, 0x61, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a,
	0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x1f, 0x0a,
	0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x44,
	0x0a, 0x0b, 0x63, 0x68, 0x61, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x68,
	0x61, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0a, 0x63, 0x68, 0x61, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x72,
	0x65, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x53, 0x74, 0x61, 0x72,
	0x72, 0x65, 0x64, 0x12, 0x1e, 0x0a, 0x0b, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6d, 0x73, 0x67, 0x5f,
	0x69, 0x64, 0x18, 0x15, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x6c, 0x61, 0x73, 0x74, 0x4d, 0x73,
	0x67, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x14, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6d, 0x73, 0x67, 0x5f,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x11, 0x6c, 0x61, 0x73, 0x74, 0x4d, 0x73, 0x67, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x72, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x73, 0x74, 0x61, 0x72, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x20, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x21, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x22, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x23, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x22, 0xba, 0x02, 0x0a, 0x11, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x65, 0x61, 0x64,
	0x43, 0x68, 0x61, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x68, 0x61,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x63, 0x68, 0x61, 0x74,
	0x49, 0x64, 0x12, 0x31, 0x0a, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x1d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x6f, 0x6c, 0x65, 0x52,
	0x04, 0x72, 0x6f, 0x6c, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x72, 0x6f, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x27,
	0x0a, 0x10, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x6d, 0x73, 0x67, 0x5f,
	0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0d, 0x6c, 0x61, 0x73, 0x74, 0x52, 0x65,
	0x61, 0x64, 0x4d, 0x73, 0x67, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0e, 0x6c, 0x61, 0x73, 0x74, 0x5f,
	0x72, 0x65, 0x61, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x0c, 0x6c, 0x61, 0x73, 0x74, 0x52, 0x65, 0x61, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a,
	0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x15, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f,
	0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x16, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x1f, 0x0a, 0x0b, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x17,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x22, 0xb2, 0x02, 0x0a, 0x12, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x64, 0x12, 0x64, 0x0a, 0x18, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x72, 0x65, 0x6c,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x6f,
	0x6d, 0x70, 0x6c, 0x69, 0x61, 0x6e, 0x63, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x52,
	0x16, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x43,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x73, 0x12, 0x6c, 0x0a, 0x1c, 0x6d, 0x61, 0x72, 0x6b, 0x65,
	0x74, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x73, 0x5f, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x2a, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x69, 0x61, 0x6e,
	0x63, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x1a, 0x6d, 0x61, 0x72, 0x6b, 0x65,
	0x74, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x73, 0x43, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x73, 0x12, 0x2e, 0x0a, 0x13, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64,
	0x5f, 0x61, 0x70, 0x70, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x11, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x41, 0x70, 0x70, 0x45, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x64, 0x42, 0x7b, 0x0a, 0x1f, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x32, 0x50, 0x01, 0x5a, 0x56, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72,
	0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65,
	0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x2f, 0x76, 0x32, 0x3b, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_message_v2_message_models_proto_rawDescOnce sync.Once
	file_moego_models_message_v2_message_models_proto_rawDescData = file_moego_models_message_v2_message_models_proto_rawDesc
)

func file_moego_models_message_v2_message_models_proto_rawDescGZIP() []byte {
	file_moego_models_message_v2_message_models_proto_rawDescOnce.Do(func() {
		file_moego_models_message_v2_message_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_message_v2_message_models_proto_rawDescData)
	})
	return file_moego_models_message_v2_message_models_proto_rawDescData
}

var file_moego_models_message_v2_message_models_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_moego_models_message_v2_message_models_proto_msgTypes = make([]protoimpl.MessageInfo, 16)
var file_moego_models_message_v2_message_models_proto_goTypes = []interface{}{
	(MessageModel_DisplayExtraInfo_ContentExtraInfo_Type)(0), // 0: moego.models.message.v2.MessageModel.DisplayExtraInfo.ContentExtraInfo.Type
	(*MessageModel)(nil),                   // 1: moego.models.message.v2.MessageModel
	(*ChatModel)(nil),                      // 2: moego.models.message.v2.ChatModel
	(*RoleReadChatModel)(nil),              // 3: moego.models.message.v2.RoleReadChatModel
	(*NotificationConfig)(nil),             // 4: moego.models.message.v2.NotificationConfig
	(*MessageModel_Metadata)(nil),          // 5: moego.models.message.v2.MessageModel.Metadata
	(*MessageModel_MetadataPlaintext)(nil), // 6: moego.models.message.v2.MessageModel.MetadataPlaintext
	(*MessageModel_MetadataPicture)(nil),   // 7: moego.models.message.v2.MessageModel.MetadataPicture
	(*MessageModel_DisplayExtraInfo)(nil),  // 8: moego.models.message.v2.MessageModel.DisplayExtraInfo
	nil,                                    // 9: moego.models.message.v2.MessageModel.MetadataPlaintext.VariablesEntry
	(*MessageModel_DisplayExtraInfo_ContentExtraInfo)(nil),                // 10: moego.models.message.v2.MessageModel.DisplayExtraInfo.ContentExtraInfo
	(*MessageModel_DisplayExtraInfo_ContentExtraInfo_Appointment)(nil),    // 11: moego.models.message.v2.MessageModel.DisplayExtraInfo.ContentExtraInfo.Appointment
	(*MessageModel_DisplayExtraInfo_ContentExtraInfo_Agreement)(nil),      // 12: moego.models.message.v2.MessageModel.DisplayExtraInfo.ContentExtraInfo.Agreement
	(*MessageModel_DisplayExtraInfo_ContentExtraInfo_OnlineBooking)(nil),  // 13: moego.models.message.v2.MessageModel.DisplayExtraInfo.ContentExtraInfo.OnlineBooking
	(*MessageModel_DisplayExtraInfo_ContentExtraInfo_COF)(nil),            // 14: moego.models.message.v2.MessageModel.DisplayExtraInfo.ContentExtraInfo.COF
	(*MessageModel_DisplayExtraInfo_ContentExtraInfo_IntakeForm)(nil),     // 15: moego.models.message.v2.MessageModel.DisplayExtraInfo.ContentExtraInfo.IntakeForm
	(*MessageModel_DisplayExtraInfo_ContentExtraInfo_MembershipSell)(nil), // 16: moego.models.message.v2.MessageModel.DisplayExtraInfo.ContentExtraInfo.MembershipSell
	(Role)(0),                        // 17: moego.models.message.v2.Role
	(Channel)(0),                     // 18: moego.models.message.v2.Channel
	(ContentType)(0),                 // 19: moego.models.message.v2.ContentType
	(MessageStatus)(0),               // 20: moego.models.message.v2.MessageStatus
	(v1.TargetType)(0),               // 21: moego.models.message.v1.TargetType
	(ChatStatus)(0),                  // 22: moego.models.message.v2.ChatStatus
	(ComplianceChannel)(0),           // 23: moego.models.message.v2.ComplianceChannel
	(v11.ClientAppointmentStatus)(0), // 24: moego.client.appointment.v1.ClientAppointmentStatus
}
var file_moego_models_message_v2_message_models_proto_depIdxs = []int32{
	17, // 0: moego.models.message.v2.MessageModel.sender_role:type_name -> moego.models.message.v2.Role
	17, // 1: moego.models.message.v2.MessageModel.receiver_role:type_name -> moego.models.message.v2.Role
	18, // 2: moego.models.message.v2.MessageModel.channel:type_name -> moego.models.message.v2.Channel
	19, // 3: moego.models.message.v2.MessageModel.content_type:type_name -> moego.models.message.v2.ContentType
	20, // 4: moego.models.message.v2.MessageModel.message_status:type_name -> moego.models.message.v2.MessageStatus
	5,  // 5: moego.models.message.v2.MessageModel.metadata:type_name -> moego.models.message.v2.MessageModel.Metadata
	8,  // 6: moego.models.message.v2.MessageModel.display_extra_info:type_name -> moego.models.message.v2.MessageModel.DisplayExtraInfo
	21, // 7: moego.models.message.v2.MessageModel.target_type:type_name -> moego.models.message.v1.TargetType
	22, // 8: moego.models.message.v2.ChatModel.chat_status:type_name -> moego.models.message.v2.ChatStatus
	17, // 9: moego.models.message.v2.RoleReadChatModel.role:type_name -> moego.models.message.v2.Role
	23, // 10: moego.models.message.v2.NotificationConfig.service_related_channels:type_name -> moego.models.message.v2.ComplianceChannel
	23, // 11: moego.models.message.v2.NotificationConfig.marketing_campaigns_channels:type_name -> moego.models.message.v2.ComplianceChannel
	6,  // 12: moego.models.message.v2.MessageModel.Metadata.plaintext:type_name -> moego.models.message.v2.MessageModel.MetadataPlaintext
	7,  // 13: moego.models.message.v2.MessageModel.Metadata.picture:type_name -> moego.models.message.v2.MessageModel.MetadataPicture
	9,  // 14: moego.models.message.v2.MessageModel.MetadataPlaintext.variables:type_name -> moego.models.message.v2.MessageModel.MetadataPlaintext.VariablesEntry
	10, // 15: moego.models.message.v2.MessageModel.DisplayExtraInfo.content_extra_infos:type_name -> moego.models.message.v2.MessageModel.DisplayExtraInfo.ContentExtraInfo
	0,  // 16: moego.models.message.v2.MessageModel.DisplayExtraInfo.ContentExtraInfo.type:type_name -> moego.models.message.v2.MessageModel.DisplayExtraInfo.ContentExtraInfo.Type
	11, // 17: moego.models.message.v2.MessageModel.DisplayExtraInfo.ContentExtraInfo.appointment:type_name -> moego.models.message.v2.MessageModel.DisplayExtraInfo.ContentExtraInfo.Appointment
	12, // 18: moego.models.message.v2.MessageModel.DisplayExtraInfo.ContentExtraInfo.agreement:type_name -> moego.models.message.v2.MessageModel.DisplayExtraInfo.ContentExtraInfo.Agreement
	13, // 19: moego.models.message.v2.MessageModel.DisplayExtraInfo.ContentExtraInfo.online_booking:type_name -> moego.models.message.v2.MessageModel.DisplayExtraInfo.ContentExtraInfo.OnlineBooking
	14, // 20: moego.models.message.v2.MessageModel.DisplayExtraInfo.ContentExtraInfo.cof:type_name -> moego.models.message.v2.MessageModel.DisplayExtraInfo.ContentExtraInfo.COF
	15, // 21: moego.models.message.v2.MessageModel.DisplayExtraInfo.ContentExtraInfo.intake_form:type_name -> moego.models.message.v2.MessageModel.DisplayExtraInfo.ContentExtraInfo.IntakeForm
	16, // 22: moego.models.message.v2.MessageModel.DisplayExtraInfo.ContentExtraInfo.membership_sell:type_name -> moego.models.message.v2.MessageModel.DisplayExtraInfo.ContentExtraInfo.MembershipSell
	24, // 23: moego.models.message.v2.MessageModel.DisplayExtraInfo.ContentExtraInfo.Appointment.client_appointment_status:type_name -> moego.client.appointment.v1.ClientAppointmentStatus
	24, // [24:24] is the sub-list for method output_type
	24, // [24:24] is the sub-list for method input_type
	24, // [24:24] is the sub-list for extension type_name
	24, // [24:24] is the sub-list for extension extendee
	0,  // [0:24] is the sub-list for field type_name
}

func init() { file_moego_models_message_v2_message_models_proto_init() }
func file_moego_models_message_v2_message_models_proto_init() {
	if File_moego_models_message_v2_message_models_proto != nil {
		return
	}
	file_moego_models_message_v2_message_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_message_v2_message_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MessageModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_message_v2_message_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChatModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_message_v2_message_models_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RoleReadChatModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_message_v2_message_models_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NotificationConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_message_v2_message_models_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MessageModel_Metadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_message_v2_message_models_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MessageModel_MetadataPlaintext); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_message_v2_message_models_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MessageModel_MetadataPicture); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_message_v2_message_models_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MessageModel_DisplayExtraInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_message_v2_message_models_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MessageModel_DisplayExtraInfo_ContentExtraInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_message_v2_message_models_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MessageModel_DisplayExtraInfo_ContentExtraInfo_Appointment); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_message_v2_message_models_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MessageModel_DisplayExtraInfo_ContentExtraInfo_Agreement); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_message_v2_message_models_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MessageModel_DisplayExtraInfo_ContentExtraInfo_OnlineBooking); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_message_v2_message_models_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MessageModel_DisplayExtraInfo_ContentExtraInfo_COF); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_message_v2_message_models_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MessageModel_DisplayExtraInfo_ContentExtraInfo_IntakeForm); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_message_v2_message_models_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MessageModel_DisplayExtraInfo_ContentExtraInfo_MembershipSell); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_message_v2_message_models_proto_msgTypes[4].OneofWrappers = []interface{}{
		(*MessageModel_Metadata_Plaintext)(nil),
		(*MessageModel_Metadata_Picture)(nil),
	}
	file_moego_models_message_v2_message_models_proto_msgTypes[9].OneofWrappers = []interface{}{
		(*MessageModel_DisplayExtraInfo_ContentExtraInfo_Appointment_)(nil),
		(*MessageModel_DisplayExtraInfo_ContentExtraInfo_Agreement_)(nil),
		(*MessageModel_DisplayExtraInfo_ContentExtraInfo_OnlineBooking_)(nil),
		(*MessageModel_DisplayExtraInfo_ContentExtraInfo_Cof)(nil),
		(*MessageModel_DisplayExtraInfo_ContentExtraInfo_IntakeForm_)(nil),
		(*MessageModel_DisplayExtraInfo_ContentExtraInfo_MembershipSell_)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_message_v2_message_models_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   16,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_message_v2_message_models_proto_goTypes,
		DependencyIndexes: file_moego_models_message_v2_message_models_proto_depIdxs,
		EnumInfos:         file_moego_models_message_v2_message_models_proto_enumTypes,
		MessageInfos:      file_moego_models_message_v2_message_models_proto_msgTypes,
	}.Build()
	File_moego_models_message_v2_message_models_proto = out.File
	file_moego_models_message_v2_message_models_proto_rawDesc = nil
	file_moego_models_message_v2_message_models_proto_goTypes = nil
	file_moego_models_message_v2_message_models_proto_depIdxs = nil
}
