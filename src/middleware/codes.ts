/* This file is generated by "/usr/local/bin/node /Users/<USER>/sources/src/github.com/MoeGolibrary/Boarding_Desktop/scripts/build_error_codes.js --no-fetch", please do not edit it. */

import { createEnum } from '../store/utils/createEnum';

export const ErrorCodes = createEnum({
  ACCOUNT_NOT_FOUND: [20004, 'Account not found'],
  APPLY_PACKAGE_CHANGED: [51005, 'Applied package is changed. Check again.'],
  APPOINTMENT_AGREEMENT_SEND_CONTENT_NOT_CONFIG: [30009, 'Appointment agreement send not config'],
  APPOINTMENT_INVALID_STATUS: [51008, 'Appointment status is invalid.'],
  APPOINTMENT_NOT_FOUND: [30005, 'Appointment not found'],
  APPOINTMENT_PET_NOT_EXIST: [30008, 'Appointment pet not exits'],
  APPOINTMENT_SIGN_OVER: [30007, 'Appointment sign over'],
  BIRTHDAY_FORMAT_ERROR: [30012, 'pet birthday format error'],
  BOOK_ONLINE_NAME_INVALID: [40404, 'business name is invalid'],
  BUSINESS_IS_EMPTY: [60000, 'BusinessId is required parameter'],
  CART_ALREADY_COMPLETED: [60011, 'This cart is already invoiced'],
  CART_ALREADY_PROCESSING: [60010, 'This cart is already started payment'],
  CART_NOT_FOUND: [60005, 'Cart not found'],
  CATEGORY_NOT_FOUND: [60002, 'Category not found'],
  CHECK_IN_ERROR: [20001, 'Check in is not allowed'],
  CHECK_OUT_ERROR: [20002, 'Check out is not allowed'],
  CUSTOMER_NOT_FOUND: [30003, 'Customer not found'],
  DEFAULT_METHOD_OPER_FALSE: [20012, 'The default method cannot be modified'],
  DELETE_ROLE_ERROR: [20010, 'Some staff have this role ,can not delete'],
  EMAIL_EXIST: [30001, 'Email is already in use'],
  EMAIL_EXISTS_MULTI: [30011, 'Multiple accounts are queried and cannot log in'],
  EMAIL_NOT_EXIST: [30000, 'Account does not exist.'],
  FILE_SIZE_EXCEEDS_LIMIT: [20013, 'File size exceeds limit'],
  GROOMING_NOT_FOUND: [51003, 'Grooming is not found'],
  INTERNAL: [0, 'System busy, please try again later.'],
  INVALID_CART_ID: [60009, 'cartId or itemId is invalid'],
  INVALID_CHECK_NUMBER: [7006, 'Check number is invalid'],
  INVALID_CODE: [20005, 'Invalid Code'],
  INVALID_DISCOUNT_TYPE: [60012, "Discount type is invalid (valid type: 'percentage', 'amount')"],
  INVALID_VALUE_TYPE: [51004, "Value type is invalid (valid type: 'percentage', 'amount')"],
  INVOICE_INVALID_STATUS: [51006, 'Invoice status is invalid.'],
  INVOICE_NOT_FOUND: [51003, 'Invoice is not found'],
  MESSAGE_AUTO_TEMPLATE_NOT_CONFIG: [80100, 'business auto template not config '],
  MESSAGE_CANNOT_DELETE_OTHER_STAFF_MESSAGE: [80106, "Cannot delete other staff's message."],
  MESSAGE_SEND_EMAIL_FAILED: [80105, 'Send email failed.'],
  MESSAGE_SEND_EMAIL_IS_NULL: [80102, 'There is no email for this client.'],
  MESSAGE_SEND_PHONE_FAILED: [80104, 'Send phone number failed.'],
  MESSAGE_SEND_PHONE_IS_NULL: [80101, 'There is no phone number for this client.'],
  MESSAGE_SEND_REVIEW_IS_NULL: [80103, 'send message review not config'],
  NAME_IS_EMPTY: [60013, 'Name is empty'],
  NAME_IS_EXIST: [60006, 'Name is already in use'],
  NOTIFACTION_TYPE_NOT_FOUND: [80108, 'Unknown notification type.'],
  NOT_PAID: [60008, 'Not paid'],
  ORDERID_EXISTS: [10010, 'Order id already exists'],
  PACKAGE_NOT_FOUND: [60003, 'Package not found'],
  PASSWORD_ERROR: [20003, 'Email or password error, please input correct information.'],
  PASSWORD_UPDATE_OLDPWD_ERROR: [20002, 'Old password error.'],
  STAFF_BINDING_ERROR4: [20009, 'Account already exists in this business'],
  PAYMENT_METHOD_NAME_EXISTS: [20011, 'method already exists'],
  PAYMENT_NOT_FOUND: [7009, 'Payment is not found'],
  PAY_AMOUNT_INVALID: [7001, 'Pay amount is invalid'],
  PAY_DATA_INVALID: [7005, 'Pay data is invalid'],
  PAY_INVOICE_ID_EMPTY: [7003, 'invoiceId is empty'],
  PAY_METHOD_INVALID: [7004, 'Pay method is invalid'],
  PAY_MODULE_EMPTY: [7002, 'Pay module is empty'],
  PERMISSION_NOT_ENOUGH: [20019, 'No permission to complete the operation'],
  PET_CODE_EXISTS: [30010, 'pet code is already in use'],
  PET_HAVE_APPOINTMENT: [30006, 'this is pet have appointment'],
  PET_NOT_FOUND: [30004, 'Pet not found'],
  PHONE_EXIST: [30002, 'Phone number is already in use'],
  PRIMARY_ID_EMPTY: [10016, 'primary id is empty'],
  PRODUCT_NOT_FOUND: [60004, 'Product not found'],
  REFUND_AMOUNT_INVALID: [7007, 'Refund amount is invalid'],
  ROOM_ERROR: [10015, 'Room is not available'],
  SERVICE_CATEGORY_NAME_IS_EXIST: [51001, 'Name is already in use'],
  SERVICE_CATEGORY_NOT_FOUND: [51003, 'No service category found'],
  SERVICE_HAVE_BINDING: [51009, 'Cannot delete this service, since it is associated with a future appointment.'],
  SERVICE_NAME_IS_EXIST: [51002, 'Name is already in use'],
  SERVICE_NOT_FOUND: [51007, 'Service is not found.'],
  SKU_IS_EXIST: [60007, 'SKU is already in use'],
  SQUARE_API_CLIENT_INIT_FAILED: [41000, 'square api client init failed'],
  STAFF_IS_EMPTY: [60014, 'staff id is required parameter'],
  STRIPE_ACCOUNT_NOT_FOUND: [7011, 'Please setup your bank account first.'],
  STRIPE_EXCEPTION: [7010, 'StripeException'],
  STRIPE_INTENT_NOT_FOUND: [7008, 'Stripe intent is not found'],
  SUCCESS: [200, 'Operation is successful !'],
  SUPPLIER_NOT_FOUND: [60001, 'Supplier not found'],
  TAG_NAME_EXIST: [20015, 'Tag name is already in use'],
  TAG_NAME_NULL: [20016, 'Tag name is null'],
  TAX_IS_USED: [20014, 'Cannot delete. There are services associated with this tax.'],
  THE_LAST_DELETED: [20017, 'The last payment method cannot be deleted'],
  THE_LAST_INACTIVE: [20018, 'The last payment method cannot be closed'],
  SMART_SCHEDULE_RULE_ALREADY_EXISTS: [20038, 'Rule already exists, please change it.'],
  UNAUTHORIZED_ERROR: [401, 'no auth'],
  GOOGLE_CALENDAR_CONNECT_ERROR: [51028, 'Google calendar sync has been disconnected. Please link again if you need.'],
  LODGING_TYPE_IN_USE: [160000, 'Lodging type is in use'],
  LODGING_UNIT_IN_USE: [160002, 'Lodging unit is in use'],
  REFUND_FAILED: [70044, 'Refund failed'],
});
