import { useFeatureIsOn } from '@growthbook/growthbook-react';
import { useSelector } from 'amos';
import { memo, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { PATH_ANNUAL_CONTRACT, PATH_MOEGO_PAY_CONTRACT } from './router/paths';
import { selectViewportFitInMobileEnable } from './store/company/company.selectors';
import { GrowthBookFeatureList } from './utils/growthBook/growthBook.config';

const BLACK_LIST = [
  // contract links can be opened in mobile browser
  PATH_MOEGO_PAY_CONTRACT,
  PATH_ANNUAL_CONTRACT,
];

/**
 * 一些特定商家的临时方案，为了及时满足业务需求或者是临时的性能优化，防止商家直接流失。
 */
export const TempFeature = memo(() => {
  const enablePerformanceOptimize = useFeatureIsOn(GrowthBookFeatureList.EnablePerformanceOptimize);
  const [viewportFitInMobile] = useSelector(selectViewportFitInMobileEnable());
  const { pathname } = useLocation();

  useEffect(() => {
    if (enablePerformanceOptimize) {
      window.dataLayer = [];
      // @ts-expect-error disable gtm for performance in same user
      window.dataLayer.push = function () {};
    }
  }, [enablePerformanceOptimize]);

  useEffect(() => {
    if (viewportFitInMobile) {
      const phoneScale = BLACK_LIST.some((v) => v.match(pathname)) ? 1 : window.screen.width / 750;
      const value = `width=device-width,initial-scale=${phoneScale},minimum-scale=${phoneScale},maximum-scale=${phoneScale},user-scalable=yes`;
      document.querySelector("meta[name='viewport']")?.setAttribute('content', value);
    }
  }, [viewportFitInMobile, pathname]);

  return null;
});
