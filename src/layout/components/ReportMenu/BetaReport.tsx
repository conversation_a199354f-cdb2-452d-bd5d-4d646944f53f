import { Tag, Text, cn } from '@moego/ui';
import React, { memo } from 'react';

export interface BetaReportProps {
  className?: string;
  children?: React.ReactNode;
  label?: string;
}

export const BetaReport = memo<BetaReportProps>(function BetaReport(props) {
  const { className, children, label = 'Beta' } = props;

  return (
    <span className={cn('moe-flex moe-flex-row moe-items-center moe-gap-x-[4px]', className)}>
      <Text
        as="span"
        variant="small"
        className={cn(
          'moe-whitespace-nowrap',
          'group-[.narrow-active]:moe-font-bold',
          'group-[.active]:moe-text-brand',
        )}
      >
        {children}
      </Text>
      <Tag className="moe-px-[4px]" color="warning" label={label} />
    </span>
  );
});
