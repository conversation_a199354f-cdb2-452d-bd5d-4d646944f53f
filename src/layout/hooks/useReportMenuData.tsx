import React, { useMemo } from 'react';
import SvgNavReportsSvg from '../../assets/svg/nav-reports.svg';
import { useAccessPayrollReport } from '../../container/ReportV2/hooks/useAccessPayrollReport';
import { useAccessReport } from '../../container/ReportV2/hooks/useAccessReport';
import { useCustomizedReportsInfo, useReportV2Info } from '../../container/ReportV2/hooks/useReportV2Info';
import {
  PATH_REPORT_DASHBOARD,
  PATH_REPORT_INSIGHTS_CUSTOM_REPORTS_LIST,
  PATH_REPORT_INSIGHTS_CUSTOM_REPORTS_TREE_TABLE,
  PATH_REPORT_INSIGHTS_DASHBOARD,
  PATH_REPORT_INSIGHTS_REPORTS,
  PATH_REPORT_LEADERBOARD,
  PATH_REPORT_PAYROLL,
  PATH_REPORT_REPORTS,
} from '../../router/paths';
import { truly } from '../../store/utils/utils';
import { BetaReport } from '../components/ReportMenu/BetaReport';
import { ReportSunset } from '../components/ReportMenu/ReportSunset';
import { ReportV2Icon } from '../components/ReportMenu/ReportV2Icon';
import { type NavMenuItemData } from './useNavMenuData';

export const useReportMenuData = (): NavMenuItemData[] => {
  const { allowReportV2 } = useReportV2Info();
  const { isAccessPayrollReport } = useAccessPayrollReport();
  const { isAccessReportWithoutPayroll } = useAccessReport();
  const { allowCustomizedReports } = useCustomizedReportsInfo();

  return useMemo(() => {
    // 无 accessReport 权限或 accessPayrollReport 权限，隐藏入口
    if (!isAccessReportWithoutPayroll && !isAccessPayrollReport) {
      return [];
    }

    /**
     * 旧 report 菜单跳转逻辑：
     * 1. 有 accessReport，跳转至 dashboard
     * 2. 无 accessReport，有 accessPayrollReport，跳转至 payroll
     * 3. 无 accessReport，无 accessPayrollReport，空白菜单
     */
    const oldReportLink = isAccessReportWithoutPayroll ? PATH_REPORT_DASHBOARD.build() : PATH_REPORT_PAYROLL.build();

    return !allowReportV2
      ? [
          {
            id: 'insights',
            icon: SvgNavReportsSvg,
            title: 'Insights',
            for: 'reports/sunset',
            children: [
              {
                id: 'reports/sunset',
                title: <ReportSunset />,
                link: oldReportLink,
              },
            ],
            limit: ['reportNormal', 'leaderBoard'],
          },
        ]
      : [
          {
            id: 'insights',
            for: isAccessReportWithoutPayroll ? 'reports/dashboard' : 'reports/sunset',
            icon: <ReportV2Icon />,
            title: 'Insights',
            limit: ['reportNormal', 'leaderBoard'],
            children: [
              // 仅在有 viewBusinessReport 权限时展示
              isAccessReportWithoutPayroll && {
                id: 'reports/dashboard',
                title: 'Dashboard',
                link: PATH_REPORT_INSIGHTS_DASHBOARD.build(),
              },
              allowCustomizedReports
                ? {
                    id: 'reports/customized',
                    title: 'Reports',
                    link: PATH_REPORT_INSIGHTS_CUSTOM_REPORTS_LIST.build(),
                    subPaths: [PATH_REPORT_INSIGHTS_REPORTS.path, PATH_REPORT_INSIGHTS_CUSTOM_REPORTS_TREE_TABLE.path],
                  }
                : {
                    id: 'reports/favorite',
                    title: 'Reports',
                    link: PATH_REPORT_INSIGHTS_REPORTS.build({ panel: 'favorite' }),
                  },
              {
                id: 'reports/sunset',
                title: <BetaReport label="Legacy">Reports</BetaReport>,
                link: oldReportLink,
                subPaths: [PATH_REPORT_REPORTS.path, PATH_REPORT_PAYROLL.path, PATH_REPORT_LEADERBOARD.path],
              },
            ].filter(truly),
          },
        ];
  }, [allowReportV2, allowCustomizedReports, isAccessReportWithoutPayroll, isAccessPayrollReport]);
};
