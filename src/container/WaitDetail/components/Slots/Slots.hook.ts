import { useSelector } from 'amos';
import dayjs from 'dayjs';
import { useEffect, useMemo } from 'react';
import { useSetState } from 'react-use';
import { currentBusinessIdBox } from '../../../../store/business/business.boxes';
import { isNormal } from '../../../../store/utils/identifier';
import { selectWaitListPlaceholderEventCard } from '../../../../store/waitList/waitList.selectors';
import { useBool } from '../../../../utils/hooks/useBool';
import { groomingPetInfoToQuickAddPetService } from '../../utils/groomingPetInfoToQuickAddPetService';
import { defaultState, getSlotId } from './Slot.utils';
import { type SlotsProps, type SlotsState } from './Slots.props';

export const useSlotsProps = (magicModeSlot?: SlotsProps['slot']) => {
  const isFromMagicMode = Boolean(magicModeSlot);

  const magicModeSlotWithId = useMemo(
    () => (magicModeSlot ? { ...magicModeSlot, id: getSlotId(magicModeSlot) } : null),
    [magicModeSlot],
  );

  return {
    isFromMagicMode,
    magicModeSlotWithId,
  };
};

export const useSlotPlaceholder = (onChange: SlotsProps['onChange']) => {
  const isCustomize = useBool(false);
  const [businessId] = useSelector(currentBusinessIdBox);
  const [waitListPlaceholder] = useSelector(selectWaitListPlaceholderEventCard(businessId));
  const { cardId: hasPlaceholderCard, date, startTime, staffId } = waitListPlaceholder;

  const customerSlotValue = useMemo(() => {
    return {
      date: date ? dayjs(date).setMinutes(startTime) : undefined,
      staffId: isNormal(staffId) ? staffId : undefined,
    };
  }, [date, startTime, staffId]);

  useEffect(() => {
    // 同步拖拽和状态
    const { isCustom = false, date, startTime, endTime, staffId, slotId } = waitListPlaceholder;
    isCustomize.as(isCustom);
    onChange({
      date,
      startTime,
      endTime,
      staffId,
      id: slotId,
    });
  }, [waitListPlaceholder]);

  return {
    hasPlaceholderCard,
    customerSlotValue,
    isCustomize,
    orderIndex: waitListPlaceholder.orderIndex,
  };
};

export const useSlotValue = (detail?: SlotsProps['detail']) => {
  const { isFromMagicMode } = useSlotsProps();
  const [state, setState] = useSetState<SlotsState>({ ...defaultState });
  const { list } = state;

  const { forwardList, restList } = useMemo(() => {
    const index = isFromMagicMode ? 0 : 2;
    return {
      forwardList: list.slice(0, index),
      restList: list.slice(index),
    };
  }, [list, isFromMagicMode]);

  const isNeedSelectServiceFirst = useMemo(() => {
    if (!detail) return true;
    const petServices = groomingPetInfoToQuickAddPetService(detail.petList);
    return petServices?.some((item) => item.serviceList?.every((service) => !isNormal(service.serviceId))) ?? false;
  }, [detail]);

  return {
    forwardList,
    restList,
    isNeedSelectServiceFirst,
    hasAvailableSlot: list.length > 0,
    state,
    setState,
  };
};
