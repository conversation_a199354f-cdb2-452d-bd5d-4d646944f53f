import { MinorChevronLeftOutlined } from '@moego/icons-react';
import { <PERSON><PERSON>, But<PERSON>, Heading, Text } from '@moego/ui';
import { useMemoizedFn } from 'ahooks';
import { useDispatch, useSelector } from 'amos';
import React, { useEffect, useMemo, useRef } from 'react';
import { useHistory, useParams } from 'react-router';
import { useHasMembershipPermission } from '../../../../components/MessageSendBox/components/hooks/useHasMembershipPermission';
import {
  type MembershipSubscriptionDetailParams,
  PATH_CUSTOMER_MEMBERSHIP_SUBSCRIPTION,
  PATH_MS_INVOICE_HISTORY,
  useCustomerId,
} from '../../../../router/paths';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import { selectSubscriptionAndMembershipBySubscriptionId } from '../../../../store/membership/membership.selectors';
import {
  getACHCustomerPaymentMethodList,
  getStripeCustomer,
} from '../../../../store/stripe/actions/public/stripe.actions';
import { type StripePaymentMethodRecord } from '../../../../store/stripe/stripe.boxes';
import { selectStripePaymentMethodACHList } from '../../../../store/stripe/stripe.selectors';
import { isNormal } from '../../../../store/utils/identifier';
import { type ForceString } from '../../../../utils/RoutePath';
import { MembershipIncludeItems, type MembershipIncludeItemsRef } from './component/MembershipIncludeItems';
import { SubscriptionButton } from './component/SubscriptionButton';
import { SubscriptionPageCard } from './component/SubscriptionPageCard';
import { SubscriptionPauseButton } from './component/SubscriptionPauseButton';
import { SubscriptionTag } from './component/SubscriptionTag';
import { useRefreshSubscription } from './hooks/useRefreshSubscription';
import { type SubscriptionAndMembershipProps } from './types';
import { ChangePaymentMethodButton } from './component/ChangePaymentMethodButton';
import { SubscriptionStatus } from '@moego/api-web/moego/models/subscription/v1/subscription_models';
import { useRecurringPaymentMethodList } from '../../../../query/payment/useRecurringPaymentMethodList';

interface PaymentInfo {
  cardInfo: StripePaymentMethodRecord | undefined;
  bankAccountInfo: StripePaymentMethodRecord | undefined;
  onChangePaymentMethod: () => Promise<unknown> | undefined;
}

interface PageTitleProps {
  clientId: string | number;
  subscriptionId: string | number;
  membershipId?: string | number;
}

function PageTitle(props: PageTitleProps) {
  const { clientId, subscriptionId, membershipId } = props;
  const customerId = useCustomerId();
  const history = useHistory();

  const goToInvoiceHistory = useMemoizedFn(() => {
    window.open(
      PATH_MS_INVOICE_HISTORY.queried({
        clientId,
        subscriptionId,
        membershipId,
      }),
    );
  });

  return (
    <div className="moe-flex moe-justify-between moe-items-center">
      <div
        onClick={() => {
          history.push(PATH_CUSTOMER_MEMBERSHIP_SUBSCRIPTION.build({ customerId }));
        }}
        className="moe-flex moe-cursor-pointer moe-items-center"
      >
        <MinorChevronLeftOutlined className="moe-mr-xs" />
        <Text variant="regular">Back to memberships</Text>
      </div>
      <Button onPress={goToInvoiceHistory} variant="tertiary-legacy">
        View invoice history
      </Button>
    </div>
  );
}

function MembershipTitle({
  membership,
  subscription,
  onRefresh,
}: SubscriptionAndMembershipProps & { onRefresh: () => void }) {
  const hasRenewOrCancelPermission = useHasMembershipPermission('operateMembership');

  return (
    <div className="moe-flex moe-justify-between moe-mb-m moe-items-start moe-px-[16px]">
      <div className="left moe-pt-2">
        <div className="moe-flex moe-items-center">
          <Heading size="4" className="moe-mr-s">
            {membership.name}
          </Heading>
          <SubscriptionTag subscription={subscription} />
        </div>
        {membership.description && (
          <Text variant="regular" className="moe-mt-xs moe-whitespace-pre-line">
            {membership.description}
          </Text>
        )}
      </div>
      {hasRenewOrCancelPermission && (
        <div className="right moe-flex moe-gap-2">
          <SubscriptionPauseButton subscription={subscription} membership={membership} />
          <SubscriptionButton subscription={subscription} membership={membership} onRefresh={onRefresh} />
        </div>
      )}
    </div>
  );
}

interface MembershipInfoFormConfig {
  title: string;
  value: string;
  action?: React.ReactNode;
}

function MembershipInfo({
  subscription,
  cardInfo,
  bankAccountInfo,
  onChangePaymentMethod,
}: SubscriptionAndMembershipProps & PaymentInfo) {
  const [business] = useSelector(selectCurrentBusiness);

  const cardLast4 = cardInfo?.card?.last4 || bankAccountInfo?.us_bank_account?.last4;

  const data: MembershipInfoFormConfig[] = [
    {
      title: 'Start date',
      value: business.formatDate(subscription.createdAt),
    },
    {
      title: 'Subscription price',
      value: `${business.formatAmount(subscription.price)}`,
    },
    // {
    //   title: 'Billing cycle',
    //   value: `${subscription.billingCycleName()}(${business.formatDate(
    //     subscription.validityPeriod.startTime,
    //   )} - ${business.formatDate(subscription.validityPeriod.endTime)})`,
    // },
    // {
    //   title: 'Next bill date',
    //   value: business.formatDate(subscription.nextBillingDate) || '',
    // },
    // {
    //   title: 'Membership ID',
    //   value: subscription.membershipId,
    // },
    // {
    //   title: 'Purchase date',
    //   value: business.formatDate(subscription.createdAt) || '',
    // },
    {
      title: 'Payment card',
      value: cardLast4 ? `Card ending in ····${cardLast4}` : '-',
      action: <ChangePaymentMethodButton subscription={subscription} onChangeSuccess={onChangePaymentMethod} />,
    },
  ];

  return (
    <div className="moe-grid moe-gap-x-s moe-gap-y-m moe-grid-cols-3 moe-pb-[40px] moe-px-[16px]">
      {data.map((item, index) => {
        return (
          <div className="membership-item" key={index}>
            <Text variant="caption" className="moe-mb-xxs moe-text-tertiary">
              {item.title}
            </Text>
            <Text variant="regular-short" className="moe-text-primary moe-flex moe-items-center moe-gap-x-xxs">
              {item.value}
              {item.action}
            </Text>
          </div>
        );
      })}
    </div>
  );
}

export function MembershipSubscriptionDetail() {
  const subscriptionId = useParams<ForceString<MembershipSubscriptionDetailParams>>().subscriptionId;
  const dispatch = useDispatch();
  const customerId = useCustomerId();
  const membershipIncludeItemsRef = useRef<MembershipIncludeItemsRef>(null);
  const [{ subscription, membership }, bankAccountsList] = useSelector(
    selectSubscriptionAndMembershipBySubscriptionId(subscriptionId),
    selectStripePaymentMethodACHList(customerId),
  );

  const { data: recurringPaymentMethodList } = useRecurringPaymentMethodList({ customerId: customerId.toString() });

  const refreshSubscription = useRefreshSubscription(subscriptionId);

  const bankAccountInfo = useMemo(() => {
    return bankAccountsList.find((bankAccount) => bankAccount.id === subscription.latestCardOnFileId);
  }, [bankAccountsList, subscription]);

  const cardInfo = useMemo(() => {
    return recurringPaymentMethodList?.find((method) => method.id === subscription.latestCardOnFileId);
  }, [recurringPaymentMethodList, subscription]);

  const refreshMembershipIncludeItems = useMemoizedFn(() => {
    if (membershipIncludeItemsRef.current) {
      membershipIncludeItemsRef.current.fetchParkCycleData();
    }
  });

  useEffect(() => {
    refreshSubscription();

    if (isNormal(customerId)) {
      dispatch([getStripeCustomer(customerId), getACHCustomerPaymentMethodList(customerId)]);
    }
  }, [subscriptionId]);

  return (
    <SubscriptionPageCard
      title={
        <PageTitle
          clientId={customerId}
          subscriptionId={subscription.internalSubscriptionId}
          membershipId={membership.id}
        />
      }
    >
      <MembershipTitle subscription={subscription} membership={membership} onRefresh={refreshMembershipIncludeItems} />
      {subscription.subStatus === SubscriptionStatus.CANCELLED && !!subscription.cancelReason && (
        <Alert color="warning" isBordered isCloseable={false} className="moe-mb-6" title="Membership canceled">
          <div>
            <Text variant="regular-short">{subscription.cancelReason}</Text>
          </div>
        </Alert>
      )}
      <MembershipInfo
        subscription={subscription}
        membership={membership}
        cardInfo={cardInfo}
        bankAccountInfo={bankAccountInfo}
        onChangePaymentMethod={refreshSubscription}
      />
      {!!membership.id && (
        <MembershipIncludeItems
          subscription={subscription}
          membershipId={membership.id}
          ref={membershipIncludeItemsRef}
        />
      )}
    </SubscriptionPageCard>
  );
}
