import { useFeatureIsOn } from '@growthbook/growthbook-react';
import { enumToOptions, useSerialCallback } from '@moego/finance-utils';
import {
  AmountCalcType,
  assertModel,
  PrefabPaymentChannel,
  RealmType,
  RefundReason,
  type TypeofRefundReason,
  useOrderV2,
  validatePositiveAmount,
} from '@moego/finance-web-kit';
import { MinorInfoOutlined } from '@moego/icons-react';
import {
  Alert,
  Form,
  Input,
  Link,
  Modal,
  Radio,
  RadioGroup,
  LegacySelect as Select,
  Spin,
  Text,
  Tooltip,
  useForm,
  useWatch,
} from '@moego/ui';
import { useSelector } from 'amos';
import React, { memo, useState } from 'react';
import { Condition } from '../../../../components/Condition';
import { PATH_CUSTOMER_CREDIT_CARDS } from '../../../../router/paths';
import { FinanceKit } from '../../../../service/finance-kit';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import { type AsyncFunction } from '../../../../types/common';
import { GrowthBookFeatureList } from '../../../../utils/growthBook/growthBook.config';
import { RefundAlertWithStoreCredit } from '../../shared/RefundAlertWithStoreCredit';
import { RefundInProcessAlert } from './RefundInProcessAlert';

const REFUND_REASON_LIST = enumToOptions(RefundReason);
export interface RefundByPaymentModalProps {
  onSave?: AsyncFunction<{
    amount: number;
    reason: TypeofRefundReason;
    reasonDescription?: string;
  }>;
  orderId: string;
  orderPaymentId: string;
  refundableAmount: number;
  customerId: string | number;
  onClose: () => void;
  visible: boolean;
}

const CHANNEL_LIST = [
  {
    id: 'cash',
    name: 'Cash',
  },
  {
    id: 'store-credit',
    name: 'Store Credit',
    tooltip:
      'Store credit can be used to pay invoice. To view or edit store credit balance, please go to  Clients & Pets > Payments.',
  },
];

export const RefundByPaymentModal = memo<RefundByPaymentModalProps>((props) => {
  const { onSave, onClose, refundableAmount, visible, customerId, orderId, orderPaymentId } = props;
  const [business] = useSelector(selectCurrentBusiness);
  const canStoreCredit = useFeatureIsOn(GrowthBookFeatureList.Credit);
  const form = useForm({
    mode: 'all',
    defaultValues: {
      amount: refundableAmount,
      reason: '' as TypeofRefundReason,
      reasonDescription: '',
    },
  });
  const [refundChannel, setRefundChannel] = useState<string>(CHANNEL_LIST[0].id);
  const path = PATH_CUSTOMER_CREDIT_CARDS.build({ customerId: Number(customerId) });
  const isSelectedStoreCredit = refundChannel === CHANNEL_LIST[1].id;
  const { state: orderDetail, loading: orderDetailLoading } = useOrderV2(FinanceKit, orderId);

  const curPayment = orderDetail?.payments.find((payment) => payment.id === orderPaymentId);
  const haveUseStoreCredit = !!orderDetail?.promotions.find((promotion) => !!promotion.storeCredit);
  const isHaveCVFee =
    curPayment && assertModel(curPayment, RealmType.OrderPayment) && !curPayment.paymentConvenienceFee.isZero();
  const isDepositOrderAndPayByCash =
    orderDetail?.order.isDepositOrder &&
    orderDetail.payments.find(
      (payment) =>
        assertModel(payment, RealmType.OrderPayment) &&
        payment.paymentMethodId === PrefabPaymentChannel.Cash.toString(),
    ) &&
    canStoreCredit;
  const loading = orderDetailLoading;

  const reason = useWatch({ control: form.control, name: 'reason' });

  const handleSubmit = useSerialCallback(async () => {
    const isValid = await form.trigger();
    if (!isValid) {
      return;
    }

    const data = form.getValues();
    await onSave?.({ ...data, amount: +data.amount });
    onClose();
  });

  return (
    <Modal
      isOpen={visible}
      size="s"
      title="Issue refund"
      confirmText="Issue refund"
      onClose={onClose}
      autoCloseOnConfirm={false}
      confirmButtonProps={{
        color: 'danger',
        isDisabled: !form.formState.isValid,
        isLoading: handleSubmit.isBusy(),
      }}
      onConfirm={handleSubmit}
    >
      {loading ? (
        <div className="moe-flex moe-items-center moe-justify-center moe-h-full">
          <Spin isLoading />
        </div>
      ) : (
        <div className={'moe-flex moe-flex-col moe-gap-m'}>
          {/* 避免初次 submit 的时候显示 */}
          {handleSubmit.isBusy() ? null : <RefundInProcessAlert orderId={orderId} />}
          {isHaveCVFee ? (
            <div className="moe-rounded-s moe-flex moe-items-center moe-gap-xs moe-p-s moe-bg-neutral-sunken-0">
              <MinorInfoOutlined className="moe-text-secondary" />
              <Text variant={'small'} className={'moe-text-secondary'}>
                Convenience fee paid by client will be charged on your account and shown as &apos;convenience fee
                withhold&apos; in report.
              </Text>
            </div>
          ) : null}
          <Form form={form} footer={null}>
            <Form.Item
              name="amount"
              label={'Refund amount'}
              rules={{
                required: true,
                validate: (value) => {
                  return validatePositiveAmount({
                    value,
                    name: 'refund',
                    calcType: AmountCalcType.AMOUNT,
                    maxAmount: refundableAmount,
                  });
                },
              }}
            >
              <Input
                description={`Maximum refund amount: ${business.formatAmount(refundableAmount)}`}
                isRequired
                prefix={business.currencySymbol || business.currencyCode}
              />
            </Form.Item>
            {isDepositOrderAndPayByCash ? (
              <RadioGroup
                defaultValue={refundChannel}
                label="Refund to"
                isRequired
                onChange={(value) => {
                  setRefundChannel(value);
                }}
              >
                {CHANNEL_LIST.map((channel) => {
                  return (
                    <Radio
                      key={channel.id}
                      value={channel.id}
                      classNames={{
                        contentText: 'moe-flex moe-items-center moe-gap-x-xxs',
                      }}
                    >
                      <Text variant="regular-short">{channel.name}</Text>
                      {channel.tooltip ? (
                        <Tooltip content={channel.tooltip}>
                          <MinorInfoOutlined className="moe-text-icon-tertiary" />
                        </Tooltip>
                      ) : null}
                    </Radio>
                  );
                })}
              </RadioGroup>
            ) : null}
            {isSelectedStoreCredit ? (
              <Alert color="warning" isCloseable={false} isRounded>
                <Text variant="small">
                  Store credit refunds are not automatically applied right now. Please go to{' '}
                  <Link variant="small" className="moe-underline" href={path} target="_blank">
                    Clients & Pets &gt; Payments
                  </Link>{' '}
                  to manually add store credit back to this client&apos;s account.
                </Text>
              </Alert>
            ) : null}
            {haveUseStoreCredit ? <RefundAlertWithStoreCredit customerId={customerId} /> : null}
            <Form.Item label={'Refund reason'} name="reason">
              <Select options={REFUND_REASON_LIST} isClearable placeholder="Select reason" />
            </Form.Item>
            <Condition if={reason === RefundReason.Others}>
              <Form.Item name="reasonDescription">
                <Input placeholder="Please enter reason" className="-moe-mt-s" />
              </Form.Item>
            </Condition>
          </Form>
        </div>
      )}
    </Modal>
  );
});
