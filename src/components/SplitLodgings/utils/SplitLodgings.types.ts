import type dayjs from 'dayjs';

export interface SplitLodgingItemValue {
  lodgingId?: string;
  lodgingName?: string;
  price?: number;
  startDate?: string;
  startTime?: number;
  endDate?: string;
  endTime?: number;
  isApplicable?: boolean;
}

export interface SplitLodgingServiceRange {
  startDate?: string;
  startTime?: number;
  endDate?: string;
  endTime?: number;
}

export enum SplitLodgingChangeType {
  Date,
  Time,
  LodgingId,
  Price,
}

export interface ValidateSplitLodgingsParams {
  splitLodgings: SplitLodgingItemValue[];
  serviceRange: SplitLodgingServiceRange;
}

export interface ISplitLodgingItemChangePayload {
  index: number;
  type: SplitLodgingChangeType;
  value: {
    lodgingId?: string;
    lodgingName?: string;
    isApplicable?: boolean;
    endDate?: dayjs.Dayjs | null;
    endTime?: dayjs.Dayjs | null;
    price?: number;
  };
}

export interface UpdateSplitLodgingsParams {
  serviceRange: SplitLodgingServiceRange;
  serviceId: number;
}

export const SplitLodgingError = 'SplitLodgingError';
