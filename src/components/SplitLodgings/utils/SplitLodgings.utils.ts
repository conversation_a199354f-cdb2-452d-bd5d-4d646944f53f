import { type useForm } from '@moego/ui';
import dayjs from 'dayjs';
import { isNil, update } from 'lodash';
import {
  SplitLodgingChangeType,
  type SplitLodgingItemValue,
  type SplitLodgingServiceRange,
  type ValidateSplitLodgingsParams,
} from './SplitLodgings.types';

export const isChangeLodgingDate = (type: SplitLodgingChangeType): type is SplitLodgingChangeType.Date => {
  return type === SplitLodgingChangeType.Date;
};

export const isChangeLodgingTime = (type: SplitLodgingChangeType): type is SplitLodgingChangeType.Time => {
  return type === SplitLodgingChangeType.Time;
};

export const isChangeLodgingId = (type: SplitLodgingChangeType): type is SplitLodgingChangeType.LodgingId => {
  return type === SplitLodgingChangeType.LodgingId;
};

export const validateSplitLodgings = (params: ValidateSplitLodgingsParams) => {
  const { splitLodgings, serviceRange } = params;
  const { startDate, endDate } = serviceRange;

  // 如果 service 还没有选择开始或者结束时间则必须先选择时间范围
  if (!endDate || !startDate) {
    return 'Please select start and end date';
  }

  // 如果只有一个 lodging 则需要检查 lodgingId
  if (splitLodgings.length === 1) {
    const [first] = splitLodgings;
    if (!first.lodgingId) {
      return 'Please select a lodging';
    }
  }

  if (splitLodgings.length > 1) {
    // 检查每个 lodging 的 date 和 time 是否选择，以及相邻 lodging 的衔接
    for (let i = 0; i < splitLodgings.length; i++) {
      const current = splitLodgings[i];
      const next = splitLodgings[i + 1];

      // 检查当前 lodging 的必要字段
      if (isNil(current.lodgingId)) {
        return 'Please select a lodging for all periods';
      }
      if (isNil(current.startDate) || isNil(current.startTime)) {
        return 'Please select start date and time for all periods';
      }
      if (isNil(current.endDate) || isNil(current.endTime)) {
        return 'Please select end date and time for all periods';
      }
      if (isNil(current.price)) {
        return 'Please enter price for all periods';
      }

      // 检查相邻 lodging 的衔接
      if (next) {
        const currentEnd = dayjs(current.endDate).set('minute', current.endTime || 0);
        const nextStart = dayjs(next.startDate).set('minute', next.startTime || 0);

        // 检查时间是否匹配
        if (!currentEnd.isSame(nextStart)) {
          return 'The end time of one lodging must match the start time of the next lodging';
        }

        // 检查日期间隔是否至少为 1 天
        const nextEndDay = dayjs(next.endDate);
        const currentEndDay = dayjs(current.endDate);
        if (nextEndDay.diff(currentEndDay, 'day') < 1) {
          return 'There must be at least 1 day between two adjacent lodgings';
        }
      }
    }

    // 检查第一个 lodging 的 date 是否是 serviceRange 第一天
    const firstLodging = splitLodgings[0];
    if (!dayjs(firstLodging.startDate).isSame(dayjs(startDate), 'day')) {
      return 'The selected dates haven’t covered the entire stay.';
    }

    // 检查最后一个 lodging 的 date 是否是 serviceRange 的最后一天
    const lastLodging = splitLodgings[splitLodgings.length - 1];
    if (!dayjs(lastLodging.endDate).isSame(dayjs(endDate), 'day')) {
      return 'The selected dates haven’t covered the entire stay.';
    }
  }

  return undefined;
};

export const syncServiceDateTimeUpdate = (
  lodgings: SplitLodgingItemValue[],
  serviceRange: Required<SplitLodgingServiceRange>,
  form?: ReturnType<typeof useForm>,
): SplitLodgingItemValue[] => {
  // 处理空数组的情况
  if (!lodgings.length) {
    return lodgings;
  }

  let result = [...lodgings];
  const firstLodging = result[0];
  const lastLodging = result[result.length - 1];

  // 检查是否有任何 lodging 的时间范围与 serviceRange 重叠
  const hasOverlappingLodging = lodgings.some((lodging) => {
    const lodgingStartDate = dayjs(lodging.startDate);
    const lodgingEndDate = dayjs(lodging.endDate);
    const serviceStartDate = dayjs(serviceRange.startDate);
    const serviceEndDate = dayjs(serviceRange.endDate);

    return (
      (lodgingStartDate.isSameOrBefore(serviceEndDate) && lodgingEndDate.isSameOrAfter(serviceStartDate)) ||
      (serviceStartDate.isSameOrBefore(lodgingEndDate) && serviceEndDate.isSameOrAfter(lodgingStartDate))
    );
  });

  // 返回第一个 lodging 并清空时间字段的辅助函数
  const returnFirstLodging = () => {
    return [
      {
        ...firstLodging,
        startDate: undefined,
        startTime: undefined,
        endDate: undefined,
        endTime: undefined,
      },
    ];
  };

  // 如果没有重叠的 lodging，返回第一个 lodging 并清空时间字段
  if (!hasOverlappingLodging) {
    return returnFirstLodging();
  }

  // 处理服务开始日期在第一个 lodging 开始日期之前的情况
  if (serviceRange.startDate && dayjs(serviceRange.startDate).isBefore(dayjs(firstLodging.startDate))) {
    return update(result, '[0]', (item) => ({
      ...item,
      startDate: serviceRange.startDate,
      startTime: serviceRange.startTime,
    }));
  }

  // 处理服务开始日期在第一个 lodging 开始日期之后的情况
  if (serviceRange.startDate && dayjs(serviceRange.startDate).isAfter(dayjs(firstLodging.startDate))) {
    // 找到第一个符合条件的 lodging => lodging 的开始时间晚于或等于 serviceRange 的开始时间加一天
    const targetIndex = result.findIndex((lodging) =>
      dayjs(lodging.startDate).isSameOrAfter(dayjs(serviceRange.startDate).add(1, 'day')),
    );

    // 如果没找到符合条件的 lodging，返回第一个 lodging 并清空时间字段
    if (targetIndex === -1) {
      return returnFirstLodging();
    }

    // 将 lodging 之前的所有 lodgings 都删掉
    result = result.slice(targetIndex);
    // 更新 lodging 的 startDate 和 startTime 为 serviceRange 的 startDate 和 startTime
    result = update(result, '[0]', (item) => ({
      ...item,
      startDate: serviceRange.startDate,
      startTime: serviceRange.startTime,
    }));
    return result;
  }

  // 处理服务结束日期在最后一个 lodging 结束日期之前的情况
  if (serviceRange.endDate && dayjs(serviceRange.endDate).isBefore(dayjs(lastLodging.endDate))) {
    // 找到最后一个符合条件的 lodging => lodging 的结束时间早于或等于 serviceRange 的结束时间减一天
    const targetIndex = result.findIndex((lodging) =>
      dayjs(lodging.endDate).isSameOrBefore(dayjs(serviceRange.endDate).subtract(1, 'day')),
    );

    // 如果没找到符合条件的 lodging，返回第一个 lodging 并清空时间字段
    if (targetIndex === -1) {
      return returnFirstLodging();
    }

    // 将 lodging 之后的 lodgings 都删掉
    result = result.slice(0, targetIndex + 1);
    // 更新 lodging 的 endDate 和 endTime 为 serviceRange 的 endDate 和 endTime
    result = update(result, `[${result.length - 1}]`, (item) => ({
      ...item,
      endDate: serviceRange.endDate,
      endTime: serviceRange.endTime,
    }));
    return result;
  }

  // 处理服务结束日期在最后一个 lodging 结束日期之后的情况
  if (serviceRange.endDate && dayjs(serviceRange.endDate).isAfter(dayjs(lastLodging.endDate))) {
    return update(result, `[${result.length - 1}]`, (item) => ({
      ...item,
      endDate: serviceRange.endDate,
      endTime: serviceRange.endTime,
    }));
  }

  // 更新开始和结束时间
  update(result, '[0].startTime', () => serviceRange.startTime);
  update(result, `[${result.length - 1}].endTime`, () => serviceRange.endTime);

  return result;
};
