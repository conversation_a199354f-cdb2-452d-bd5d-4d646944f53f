import { initDayjs } from '../../../init/initDayjs';
import { type SplitLodgingItemValue, type SplitLodgingServiceRange } from './SplitLodgings.types';
import { syncServiceDateTimeUpdate, validateSplitLodgings } from './SplitLodgings.utils';

// 初始化 dayjs
initDayjs();

describe('validateSplitLodgings', () => {
  // 测试用例1: 服务范围未选择开始或结束日期
  test('应该返回错误当服务范围未选择开始或结束日期', () => {
    const params = {
      splitLodgings: [],
      serviceRange: {
        startDate: undefined,
        endDate: undefined,
      } as SplitLodgingServiceRange,
    };

    expect(validateSplitLodgings(params)).toBe('Please select start and end date');
  });

  // 测试用例2: 服务范围只选择了开始日期
  test('应该返回错误当服务范围只选择了开始日期', () => {
    const params = {
      splitLodgings: [],
      serviceRange: {
        startDate: '2023-01-01',
        endDate: undefined,
      } as SplitLodgingServiceRange,
    };

    expect(validateSplitLodgings(params)).toBe('Please select start and end date');
  });

  // 测试用例3: 服务范围只选择了结束日期
  test('应该返回错误当服务范围只选择了结束日期', () => {
    const params = {
      splitLodgings: [],
      serviceRange: {
        startDate: undefined,
        endDate: '2023-01-05',
      } as SplitLodgingServiceRange,
    };

    expect(validateSplitLodgings(params)).toBe('Please select start and end date');
  });

  // 测试用例4: 只有一个住宿且未选择住宿ID
  test('应该返回错误当只有一个住宿且未选择住宿ID', () => {
    const params = {
      splitLodgings: [
        {
          lodgingId: undefined,
        } as SplitLodgingItemValue,
      ],
      serviceRange: {
        startDate: '2023-01-01',
        endDate: '2023-01-05',
      } as SplitLodgingServiceRange,
    };

    expect(validateSplitLodgings(params)).toBe('Please select a lodging');
  });

  // 测试用例5: 只有一个住宿且已选择住宿ID - 有效
  test('应该返回undefined当只有一个住宿且已选择住宿ID', () => {
    const params = {
      splitLodgings: [
        {
          lodgingId: '123',
        } as SplitLodgingItemValue,
      ],
      serviceRange: {
        startDate: '2023-01-01',
        endDate: '2023-01-05',
      } as SplitLodgingServiceRange,
    };

    expect(validateSplitLodgings(params)).toBeUndefined();
  });

  // 测试用例6: 多个住宿 - 缺少住宿ID
  test('应该返回错误当多个住宿中有缺少住宿ID', () => {
    const params = {
      splitLodgings: [
        {
          lodgingId: '123',
          startDate: '2023-01-01',
          startTime: 0,
          endDate: '2023-01-02',
          endTime: 0,
          price: 100,
        } as SplitLodgingItemValue,
        {
          lodgingId: undefined,
          startDate: '2023-01-02',
          startTime: 0,
          endDate: '2023-01-03',
          endTime: 0,
          price: 100,
        } as SplitLodgingItemValue,
      ],
      serviceRange: {
        startDate: '2023-01-01',
        endDate: '2023-01-03',
      } as SplitLodgingServiceRange,
    };

    expect(validateSplitLodgings(params)).toBe('Please select a lodging for all periods');
  });

  // 测试用例7: 多个住宿 - 缺少开始日期或时间
  test('应该返回错误当多个住宿中有缺少开始日期或时间', () => {
    const params = {
      splitLodgings: [
        {
          lodgingId: '123',
          startDate: undefined,
          startTime: 0,
          endDate: '2023-01-02',
          endTime: 0,
          price: 100,
        } as SplitLodgingItemValue,
        {
          lodgingId: '456',
          startDate: '2023-01-02',
          startTime: 0,
          endDate: '2023-01-03',
          endTime: 0,
          price: 100,
        } as SplitLodgingItemValue,
      ],
      serviceRange: {
        startDate: '2023-01-01',
        endDate: '2023-01-03',
      } as SplitLodgingServiceRange,
    };

    expect(validateSplitLodgings(params)).toBe('Please select start date and time for all periods');
  });

  // 测试用例8: 多个住宿 - 缺少结束日期或时间
  test('应该返回错误当多个住宿中有缺少结束日期或时间', () => {
    const params = {
      splitLodgings: [
        {
          lodgingId: '123',
          startDate: '2023-01-01',
          startTime: 0,
          endDate: '2023-01-02',
          endTime: undefined,
          price: 100,
        } as SplitLodgingItemValue,
        {
          lodgingId: '456',
          startDate: '2023-01-02',
          startTime: 0,
          endDate: '2023-01-03',
          endTime: 0,
          price: 100,
        } as SplitLodgingItemValue,
      ],
      serviceRange: {
        startDate: '2023-01-01',
        endDate: '2023-01-03',
      } as SplitLodgingServiceRange,
    };

    expect(validateSplitLodgings(params)).toBe('Please select end date and time for all periods');
  });

  // 测试用例9: 多个住宿 - 缺少价格
  test('应该返回错误当多个住宿中有缺少价格', () => {
    const params = {
      splitLodgings: [
        {
          lodgingId: '123',
          startDate: '2023-01-01',
          startTime: 0,
          endDate: '2023-01-02',
          endTime: 0,
          price: undefined,
        } as SplitLodgingItemValue,
        {
          lodgingId: '456',
          startDate: '2023-01-02',
          startTime: 0,
          endDate: '2023-01-03',
          endTime: 0,
          price: 100,
        } as SplitLodgingItemValue,
      ],
      serviceRange: {
        startDate: '2023-01-01',
        endDate: '2023-01-03',
      } as SplitLodgingServiceRange,
    };

    expect(validateSplitLodgings(params)).toBe('Please enter price for all periods');
  });

  // 测试用例10: 多个住宿 - 相邻住宿时间不匹配
  test('应该返回错误当相邻住宿时间不匹配', () => {
    const params = {
      splitLodgings: [
        {
          lodgingId: '123',
          startDate: '2023-01-01',
          startTime: 0,
          endDate: '2023-01-02',
          endTime: 60, // 1:00
          price: 100,
        } as SplitLodgingItemValue,
        {
          lodgingId: '456',
          startDate: '2023-01-02',
          startTime: 30, // 0:30，与上一个住宿的结束时间不匹配
          endDate: '2023-01-03',
          endTime: 0,
          price: 100,
        } as SplitLodgingItemValue,
      ],
      serviceRange: {
        startDate: '2023-01-01',
        endDate: '2023-01-03',
      } as SplitLodgingServiceRange,
    };

    expect(validateSplitLodgings(params)).toBe(
      'The end time of one lodging must match the start time of the next lodging',
    );
  });

  // 测试用例11: 多个住宿 - 日期间隔小于1天
  test('应该返回错误当日期间隔小于1天', () => {
    const params = {
      splitLodgings: [
        {
          lodgingId: '123',
          startDate: '2023-01-01',
          startTime: 0,
          endDate: '2023-01-02',
          endTime: 0,
          price: 100,
        } as SplitLodgingItemValue,
        {
          lodgingId: '456',
          startDate: '2023-01-02', // 与上一个住宿的结束时间匹配
          startTime: 0,
          endDate: '2023-01-02', // 与第一个住宿的结束日期相同，导致间隔小于1天
          endTime: 0,
          price: 100,
        } as SplitLodgingItemValue,
      ],
      serviceRange: {
        startDate: '2023-01-01',
        endDate: '2023-01-02',
      } as SplitLodgingServiceRange,
    };

    expect(validateSplitLodgings(params)).toBe('There must be at least 1 day between two adjacent lodgings');
  });

  // 测试用例12: 多个住宿 - 第一个住宿不是从服务范围的第一天开始
  test('应该返回错误当第一个住宿不是从服务范围的第一天开始', () => {
    const params = {
      splitLodgings: [
        {
          lodgingId: '123',
          startDate: '2023-01-02', // 不是从服务范围的第一天开始
          startTime: 0,
          endDate: '2023-01-03',
          endTime: 0,
          price: 100,
        } as SplitLodgingItemValue,
        {
          lodgingId: '456',
          startDate: '2023-01-03',
          startTime: 0,
          endDate: '2023-01-04',
          endTime: 0,
          price: 100,
        } as SplitLodgingItemValue,
      ],
      serviceRange: {
        startDate: '2023-01-01',
        endDate: '2023-01-04',
      } as SplitLodgingServiceRange,
    };

    expect(validateSplitLodgings(params)).toBe('The selected dates haven’t covered the entire stay.');
  });

  // 测试用例13: 多个住宿 - 最后一个住宿不是到服务范围的最后一天结束
  test('应该返回错误当最后一个住宿不是到服务范围的最后一天结束', () => {
    const params = {
      splitLodgings: [
        {
          lodgingId: '123',
          startDate: '2023-01-01',
          startTime: 0,
          endDate: '2023-01-02',
          endTime: 0,
          price: 100,
        } as SplitLodgingItemValue,
        {
          lodgingId: '456',
          startDate: '2023-01-02',
          startTime: 0,
          endDate: '2023-01-03', // 不是到服务范围的最后一天结束
          endTime: 0,
          price: 100,
        } as SplitLodgingItemValue,
      ],
      serviceRange: {
        startDate: '2023-01-01',
        endDate: '2023-01-04',
      } as SplitLodgingServiceRange,
    };

    expect(validateSplitLodgings(params)).toBe('The selected dates haven’t covered the entire stay.');
  });

  // 测试用例14: 多个住宿 - 所有条件都满足，应该返回undefined
  test('应该返回undefined当所有条件都满足', () => {
    const params = {
      splitLodgings: [
        {
          lodgingId: '123',
          startDate: '2023-01-01',
          startTime: 0,
          endDate: '2023-01-02',
          endTime: 0,
          price: 100,
        } as SplitLodgingItemValue,
        {
          lodgingId: '456',
          startDate: '2023-01-02',
          startTime: 0,
          endDate: '2023-01-03',
          endTime: 0,
          price: 100,
        } as SplitLodgingItemValue,
        {
          lodgingId: '789',
          startDate: '2023-01-03',
          startTime: 0,
          endDate: '2023-01-04',
          endTime: 0,
          price: 100,
        } as SplitLodgingItemValue,
      ],
      serviceRange: {
        startDate: '2023-01-01',
        endDate: '2023-01-04',
      } as SplitLodgingServiceRange,
    };

    expect(validateSplitLodgings(params)).toBeUndefined();
  });
});

describe('syncServiceDateTimeUpdate', () => {
  const mockServiceRange: Required<SplitLodgingServiceRange> = {
    startDate: '2023-01-01',
    startTime: 0,
    endDate: '2023-01-05',
    endTime: 0,
  };

  test('应该返回空数组当输入为空数组时', () => {
    const result = syncServiceDateTimeUpdate([], mockServiceRange);
    expect(result).toEqual([]);
  });

  test('应该返回第一个 lodging 并清空时间字段当没有重叠的 lodging 时', () => {
    const splitLodgings: SplitLodgingItemValue[] = [
      {
        lodgingId: '123',
        lodgingName: 'Room 1',
        price: 100,
        startDate: '2023-02-01',
        startTime: 0,
        endDate: '2023-02-05',
        endTime: 0,
        isApplicable: true,
      },
    ];

    const result = syncServiceDateTimeUpdate(splitLodgings, mockServiceRange);
    expect(result).toHaveLength(1);
    expect(result[0].lodgingId).toBe('123');
    expect(result[0].startDate).toBeUndefined();
    expect(result[0].startTime).toBeUndefined();
    expect(result[0].endDate).toBeUndefined();
    expect(result[0].endTime).toBeUndefined();
  });

  test('应该正确同步服务开始日期在第一个 lodging 开始日期之前的情况', () => {
    const splitLodgings: SplitLodgingItemValue[] = [
      {
        lodgingId: '123',
        lodgingName: 'Room 1',
        price: 100,
        startDate: '2023-01-02',
        startTime: 0,
        endDate: '2023-01-05',
        endTime: 0,
        isApplicable: true,
      },
    ];

    const newServiceRange = {
      ...mockServiceRange,
      startDate: '2023-01-01',
      startTime: 30,
    };

    const result = syncServiceDateTimeUpdate(splitLodgings, newServiceRange);
    expect(result[0].startDate).toBe('2023-01-01');
    expect(result[0].startTime).toBe(30);
  });

  test('应该正确同步服务开始日期在第一个 lodging 开始日期之后的情况', () => {
    const splitLodgings: SplitLodgingItemValue[] = [
      {
        lodgingId: '123',
        lodgingName: 'Room 1',
        price: 100,
        startDate: '2022-12-30',
        startTime: 0,
        endDate: '2023-01-02',
        endTime: 0,
        isApplicable: true,
      },
      {
        lodgingId: '456',
        lodgingName: 'Room 2',
        price: 150,
        startDate: '2023-01-03',
        startTime: 0,
        endDate: '2023-01-05',
        endTime: 0,
        isApplicable: true,
      },
    ];

    const newServiceRange = {
      ...mockServiceRange,
      startDate: '2023-01-01',
      startTime: 30,
    };

    const result = syncServiceDateTimeUpdate(splitLodgings, newServiceRange);
    expect(result).toHaveLength(1);
    expect(result[0].startDate).toBe('2023-01-01');
    expect(result[0].startTime).toBe(30);
  });

  test('应该正确同步服务结束日期在最后一个 lodging 结束日期之前的情况', () => {
    const splitLodgings: SplitLodgingItemValue[] = [
      {
        lodgingId: '123',
        lodgingName: 'Room 1',
        price: 100,
        startDate: '2023-01-01',
        startTime: 0,
        endDate: '2023-01-03',
        endTime: 0,
        isApplicable: true,
      },
      {
        lodgingId: '456',
        lodgingName: 'Room 2',
        price: 150,
        startDate: '2023-01-04',
        startTime: 0,
        endDate: '2023-01-10',
        endTime: 0,
        isApplicable: true,
      },
    ];

    const newServiceRange = {
      ...mockServiceRange,
      endDate: '2023-01-05',
      endTime: 30,
    };

    const result = syncServiceDateTimeUpdate(splitLodgings, newServiceRange);
    expect(result).toHaveLength(1);
    expect(result[0].endDate).toBe('2023-01-05');
    expect(result[0].endTime).toBe(30);
  });

  test('应该正确同步服务结束日期在最后一个 lodging 结束日期之后的情况', () => {
    const splitLodgings: SplitLodgingItemValue[] = [
      {
        lodgingId: '123',
        lodgingName: 'Room 1',
        price: 100,
        startDate: '2023-01-01',
        startTime: 0,
        endDate: '2023-01-03',
        endTime: 0,
        isApplicable: true,
      },
    ];

    const newServiceRange = {
      ...mockServiceRange,
      endDate: '2023-01-05',
      endTime: 30,
    };

    const result = syncServiceDateTimeUpdate(splitLodgings, newServiceRange);
    expect(result[0].endDate).toBe('2023-01-05');
    expect(result[0].endTime).toBe(30);
  });

  test('应该保持其他字段不变', () => {
    const splitLodgings: SplitLodgingItemValue[] = [
      {
        lodgingId: '123',
        lodgingName: 'Room 1',
        price: 100,
        startDate: '2023-01-01',
        startTime: 0,
        endDate: '2023-01-05',
        endTime: 0,
        isApplicable: true,
      },
    ];

    const newServiceRange = {
      ...mockServiceRange,
      startTime: 30,
      endTime: 30,
    };

    const result = syncServiceDateTimeUpdate(splitLodgings, newServiceRange);
    expect(result[0].lodgingId).toBe('123');
    expect(result[0].lodgingName).toBe('Room 1');
    expect(result[0].price).toBe(100);
    expect(result[0].isApplicable).toBe(true);
  });
});
