import { type useForm } from '@moego/ui';
import dayjs from 'dayjs';
import { isNil, isUndefined } from 'lodash';
import { type SplitLodgingItemValue } from '../utils/SplitLodgings.types';

export const useSyncSplitLodgingsToFormValue = (form?: ReturnType<typeof useForm>, formPrefix?: string) => {
  return (value: SplitLodgingItemValue[]) => {
    if (isUndefined(form)) return;
    if (value.length === 1) {
      const [firstLodging] = value;
      form.setValue(`${formPrefix}splitLodgings`, []);
      form.setValue(`${formPrefix}lodgingId`, firstLodging.lodgingId);
      form.setValue(`${formPrefix}lodgingName`, firstLodging.lodgingName);
    } else {
      form.setValue(`${formPrefix}lodgingId`, undefined);
      form.setValue(`${formPrefix}lodgingName`, undefined);

      // 这里用 for 循环是因为如果直接 setValue 数组的话消费的地方没办法正确更新
      for (let index = 0; index < value.length; index++) {
        const item = value[index];
        form.setValue(
          `${formPrefix}splitLodgings.${index}.startDate`,
          isNil(item.startDate) ? null : dayjs(item.startDate),
        );
        form.setValue(`${formPrefix}splitLodgings.${index}.endDate`, isNil(item.endDate) ? null : dayjs(item.endDate));
        form.setValue(
          `${formPrefix}splitLodgings.${index}.startTime`,
          isUndefined(item.startTime) ? null : dayjs(item.startTime).setMinutes(item.startTime),
        );
        form.setValue(
          `${formPrefix}splitLodgings.${index}.endTime`,
          isUndefined(item.endTime) ? null : dayjs(item.endTime).setMinutes(item.endTime),
        );
      }
    }
  };
};
