import { type Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import { isNil } from 'lodash';
import { useLatestCallback } from '../../../utils/hooks/useLatestCallback';
import { type SplitLodgingItemValue, type SplitLodgingServiceRange } from '../utils/SplitLodgings.types';

interface GetDisableRangeParams {
  date: Dayjs;
  index: number;
  items: SplitLodgingItemValue[];
}

export const useGetDisableRange = (serviceRange: SplitLodgingServiceRange) => {
  return useLatestCallback((params: GetDisableRangeParams) => {
    const { index, items, date } = params;
    const serviceStartDate = dayjs(serviceRange.startDate).add(1, 'day');
    const serviceEndDate = dayjs(serviceRange.endDate);

    // 当前 item 可选范围是 [prevItemEndDate + 1 day, serviceEndDate]
    const prevItemEndDate = items[index - 1]?.endDate;
    if (!isNil(prevItemEndDate)) {
      const prevItemEndDateDayjs = dayjs(prevItemEndDate);
      if (date.isBefore(prevItemEndDateDayjs.add(1, 'day'), 'day')) {
        return true;
      }
    }

    // 检查是否在服务时间范围内
    if (date.isBefore(serviceStartDate, 'day') || date.isAfter(serviceEndDate, 'day')) {
      return true;
    }

    return false;
  });
};
