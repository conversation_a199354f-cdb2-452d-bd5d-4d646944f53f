import { type CustomizedServiceByPet } from '@moego/api-web/moego/api/offering/v1/service_api';
import { ServiceItemType, ServiceType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { useDispatch, useSelector } from 'amos';
import { isNil } from 'lodash';
import { useEffect, useState } from 'react';
import { getMultiplePetsAppointmentService } from '../../../container/Appt/store/appt.api';
import { selectCurrentBusiness } from '../../../store/business/business.selectors';
import { lodgingUnitMapBox } from '../../../store/lodging/lodgingUnit.boxes';
import { isNormal } from '../../../store/utils/identifier';
import { useCancelableCallback } from '../../../utils/hooks/useCancelableCallback';
import { useLatestCallback } from '../../../utils/hooks/useLatestCallback';

interface UseGetIsApplicableLodgingParams {
  petId: number;
  serviceId: number;
}

/**
 * 判断 lodging 是否可以适用于 service
 * 这个关系是 setting 里的绑定关系和时间无关 只要在 setting 中配置了 lodging 适用于 service 那么就认为可以适用
 *
 * @param {UseGetIsApplicableLodgingParams} params
 * @returns
 */
export const useGetIsApplicableLodging = (params: UseGetIsApplicableLodgingParams) => {
  const dispatch = useDispatch();
  const { serviceId, petId } = params;
  const [business, lodgingUnitMap] = useSelector(selectCurrentBusiness, lodgingUnitMapBox);
  const [availablePetServices, setAvailablePetServices] = useState<CustomizedServiceByPet[]>([]);

  const getApplicableServiceList = useCancelableCallback(async (signal: AbortSignal) => {
    const { petServices } = await dispatch(
      getMultiplePetsAppointmentService(
        {
          inactive: false,
          onlyAvailable: false,
          petIds: [String(petId)],
          businessId: String(business.id),
          serviceType: ServiceType.SERVICE,
          serviceItemType: ServiceItemType.BOARDING,
          selectedServiceIds: [String(serviceId)],
          selectedServiceItemType: ServiceItemType.BOARDING,
        },
        signal,
      ),
    );
    setAvailablePetServices(petServices);
  });

  useEffect(() => {
    if (isNormal(serviceId) && isNormal(petId)) {
      getApplicableServiceList();
    }
  }, [serviceId, petId]);

  return useLatestCallback((lodgingId: string | undefined) => {
    if (getApplicableServiceList.isBusy() || isNil(lodgingId)) {
      return true;
    }

    const petService = availablePetServices.find((petService) => petService.petId === String(petId));
    if (!petService) {
      return true;
    }

    let isApplicable = true;
    petService.categories.forEach((category) => {
      const service = category.services.find((service) => service.id === String(serviceId));

      if (service && service.lodgingFilter) {
        const lodgingUnit = lodgingUnitMap.mustGetItem(lodgingId);
        if (!service.customizedLodgings.includes(lodgingUnit.lodgingTypeId)) {
          isApplicable = false;
        }
      }
    });

    return isApplicable;
  });
};
