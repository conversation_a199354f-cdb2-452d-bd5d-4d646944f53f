import dayjs from 'dayjs';
import { isNil } from 'lodash';
import { useLatestCallback } from '../../../utils/hooks/useLatestCallback';
import { type SplitLodgingServiceRange } from '../utils/SplitLodgings.types';

/**
 * 当 lodging 的 date 满足 service 结束日期时不可更改 time
 *
 * @param {SplitLodgingServiceRange} serviceRange
 * @returns
 */
export const useGetTimeIsDisabled = (serviceRange: SplitLodgingServiceRange) => {
  return useLatestCallback((endDate: string | undefined) => {
    if (isNil(endDate)) {
      return false;
    }
    const serviceEndDate = dayjs(serviceRange.endDate);
    return dayjs(endDate).isSame(serviceEndDate, 'day');
  });
};
