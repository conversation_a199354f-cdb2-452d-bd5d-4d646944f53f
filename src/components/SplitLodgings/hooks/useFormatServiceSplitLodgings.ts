import { <PERSON><PERSON><PERSON> } from '@moego/finance-utils';
import { useSelector } from 'amos';
import { isUndefined } from 'lodash';
import { type FormatServiceSplitLodgingsParams } from '../../../container/Appt/components/SelectServiceDetail/components/Service/ServiceBoarding/ServiceBoarding.types';
import { lodgingUnitMapBox } from '../../../store/lodging/lodgingUnit.boxes';
import { type SplitLodgingItemValue } from '../utils/SplitLodgings.types';

export const useFormatServiceSplitLodgings = () => {
  const [lodgingUnitMap] = useSelector(lodgingUnitMapBox);

  return (params: FormatServiceSplitLodgingsParams): SplitLodgingItemValue[] => {
    const { splitLodgings, lodgingId, lodgingName, servicePrice } = params;

    if (splitLodgings && splitLodgings.length > 1) {
      return splitLodgings.map((item) => ({
        ...item,
        lodgingName: item.lodgingId && lodgingUnitMap.mustGetItem(item.lodgingId).name,
        price: !isUndefined(item.price) ? MoeMoney.fromMoney(item.price).valueOf() : undefined,
      }));
    }

    return [
      {
        lodgingId,
        lodgingName,
        price: servicePrice ?? 0,
        isApplicable: true,
        startDate: undefined,
        startTime: undefined,
        endDate: undefined,
        endTime: undefined,
      },
    ];
  };
};
