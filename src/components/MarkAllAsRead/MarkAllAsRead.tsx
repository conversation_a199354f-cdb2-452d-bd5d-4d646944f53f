import { SvgIcon } from '@moego/business-components';
import React, { memo } from 'react';
import SvgIconCheckSquareSvg from '../../assets/svg/icon-check-square.svg';
import { NavTextButton } from '../../layout/components/Navbar/NavButton.style';

interface IMarkAllAsReadProps {
  className?: string;
  svgSize: number;
  onClick: () => void;
}

export const MarkAllAsRead = memo(({ className, svgSize, onClick }: IMarkAllAsReadProps) => {
  return (
    <NavTextButton className={`read-all ${className || ''}`} onClick={onClick}>
      <SvgIcon src={SvgIconCheckSquareSvg} size={svgSize} />
      Mark all as read
    </NavTextButton>
  );
});
