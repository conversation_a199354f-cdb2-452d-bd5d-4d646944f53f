import observeRect from '@reach/observe-rect';
import { Popover } from 'antd';
import { type PopoverProps } from 'antd/es/popover';
import { composeRef } from 'rc-util/lib/ref';
import React, { Children, cloneElement, isValidElement, useEffect, useRef, useState } from 'react';
import { createPortal } from 'react-dom';
import { useUnmount } from 'react-use';
import { OnboardingPopoverContainer } from './OnboardingPopover.styles';

interface Inset {
  top: number;
  bottom: number;
  left: number;
  right: number;
}

export type OnboardingPopoverProps = PopoverProps & {
  title?: string;
  message: string;
  onClickConfirm?: () => void;
  showIgnore?: boolean;
  onClickIgnore?: () => void;
  showHighlight?: boolean;
  highlightInset?: Partial<Inset>;
  offset?: number[];
};

// TODO(Perqin, P2): Migrate PayoutDetailPopover to this component.
/**
 * @deprecated
 *
 * just used by StripeIntegrationCards
 *
 * use OnboardingPopoverV2 instead
 */
export const OnboardingPopover = ({
  title,
  message,
  onClickConfirm,
  showIgnore,
  onClickIgnore,
  children,
  showHighlight,
  highlightInset,
  visible,
  offset = [0, 0],
  ...rest
}: OnboardingPopoverProps) => {
  let childNode = Children.only(children);
  const childNodeRef = useRef<HTMLElement>();
  const [childRect, setChildRect] = useState<DOMRect>(new DOMRect(0, 0, 0, 0));
  const inset = {
    left: highlightInset?.left ?? 0,
    right: highlightInset?.right ?? 0,
    top: highlightInset?.top ?? 0,
    bottom: highlightInset?.bottom ?? 0,
  };

  /**
   * 问题：
   * 自定义 Popover 样式 要使用单独的 popupContainer element，否则无法覆盖箭头区域
   * 若 popupContainer element 挂载在 row 下，会导致 Popover 内容 hover 后 row 也跟着 hover，比较奇怪
   * 搞个 portal 挂在 body 隔离一下
   */
  const popContainerRef = useRef<HTMLDivElement>();
  if (!popContainerRef.current) {
    popContainerRef.current = document.createElement('div');
    document.body.appendChild(popContainerRef.current);
  }
  useUnmount(() => {
    if (popContainerRef.current) {
      document.body.removeChild(popContainerRef.current);
    }
  });

  const containerRef = useRef<HTMLDivElement>(null);
  const popOverContent = (
    <div className="!moe-w-[280px] !moe-px-[20px] !moe-pt-[16px] !moe-pb-[20px] !moe-rounded-[6px] !moe-bg-[#3d414b]">
      {title && (
        <div className="!moe-mb-[16px] !moe-text-[20px] !moe-leading-[24px] !moe-font-bold !moe-text-[#fff]">
          {title}
        </div>
      )}
      <div className="!moe-text-sm !moe-text-[#fff] !moe-font-medium">{message}</div>
      <div className="!moe-mt-[22px] !moe-flex !moe-justify-end">
        {showIgnore && (
          <div
            className="!moe-cursor-pointer !moe-mr-[12px] !moe-px-[8px] !moe-py-[6px] !moe-rounded-[56px] !moe-border-[1px] !moe-border-[#DEE1E6] !moe-bg-[#fff] !moe-text-xs !moe-font-bold !moe-text-[#333333]"
            onClick={onClickIgnore}
          >
            Not now
          </div>
        )}
        <div
          className="!moe-cursor-pointer !moe-px-[8px] !moe-py-[6px] !moe-rounded-[56px] !moe-border-[1px] !moe-border-[#DEE1E6] !moe-bg-brand-bold !moe-text-xs !moe-font-bold !moe-text-[#fff]"
          onClick={onClickConfirm}
        >
          Activate
        </div>
      </div>
    </div>
  );

  if (isValidElement(childNode)) {
    const props = {
      ref: composeRef(childNodeRef, (childNode as any).ref),
    };
    childNode = cloneElement(childNode, props);
  }
  useEffect(() => {
    let observer: ReturnType<typeof observeRect>;
    if (visible && childNodeRef.current) {
      observer = observeRect(childNodeRef.current, (rect) => setChildRect(rect));
      observer.observe();
    }
    return () => {
      observer && observer.unobserve();
    };
  }, [visible, childNodeRef.current]);

  return (
    <>
      {createPortal(<OnboardingPopoverContainer ref={containerRef} />, popContainerRef.current)}
      <Popover
        placement="right"
        align={{ offset: [inset.left + offset[0], offset[1]] }}
        content={popOverContent}
        getPopupContainer={() => containerRef.current!}
        visible={visible}
        {...rest}
      >
        {childNode}
      </Popover>
      {visible && (
        <div className="!moe-fixed !moe-inset-0 !moe-z-10">
          <svg width="100%" height="100%">
            <mask id="myMask">
              <rect x="0" y="0" width="100%" height="100%" fill="white" />
              <rect
                x={childRect.x - inset.left}
                y={childRect.y - inset.top}
                rx="8"
                ry="8"
                width={childRect.width + inset.left + inset.right}
                height={childRect.height + inset.top + inset.bottom}
                fill="black"
              />
            </mask>
            <rect x="0" y="0" width="100%" height="100%" fill="black" fillOpacity="0.1" mask="url(#myMask)" />
          </svg>
        </div>
      )}
    </>
  );
};
