// move from staff/OnboardingPopover
import React, { type ReactElement, type ReactNode, useEffect, useRef } from 'react';
import SvgIconCloseSvg from '../../assets/svg/icon-close.svg';
import { SvgIcon } from '../../components/Icon/Icon';
import { Tooltip, type TooltipProps } from '../../components/Popup/Tooltip';
import { useBool } from '../../utils/hooks/useBool';

interface OnboardingPopoverProps extends Omit<TooltipProps, 'overlay'> {
  onClose: (e: React.MouseEvent) => void;
  onConfirm?: () => void;
  closeText?: string;
  confirmText?: string;
  visibleCloseBtn?: boolean;
  visibleConfirmBtn?: boolean;
  visibleCloseIcon?: boolean;
  children: ReactElement;
  content: ReactNode;
}

export const OnboardingPopover = ({
  children,
  onClose,
  onConfirm,
  closeText = 'Skip',
  visibleCloseBtn = true,
  visibleConfirmBtn = true,
  visibleCloseIcon = true,
  confirmText = 'Next',
  title,
  content,
  visible,
  placement,
  width,
  ...popProps
}: OnboardingPopoverProps) => {
  const ref = useRef<HTMLDivElement>(null);
  const show = useBool();

  useEffect(() => {
    if (visible) {
      ref.current?.scrollIntoView({ behavior: 'smooth', inline: 'center', block: 'center' });
      setTimeout(show.open, 500);
    } else {
      show.close();
    }
  }, [visible]);

  return (
    <Tooltip
      width={width}
      placement={placement}
      visible={show.value}
      overlay={
        <div>
          <div className="!moe-flex !moe-justify-between !moe-items-center !moe-mb-[14px]">
            <span>{title}</span>
            {visibleCloseIcon && (
              <SvgIcon src={SvgIconCloseSvg} onClick={onClose} className="!moe-cursor-pointer" size={20} />
            )}
          </div>
          {content}
          <div className="!moe-mt-[16px] !moe-flex !moe-justify-end">
            {visibleCloseBtn && (
              <span
                onClick={onClose}
                className="!moe-px-[20px] !moe-py-[6px] !moe-border-0 !moe-border-solid !moe-border-[#CDCDCD] !moe-bg-white !moe-text-[#000] !moe-font-bold !moe-cursor-pointer !moe-rounded-full"
              >
                {closeText}
              </span>
            )}
            {visibleConfirmBtn && (
              <span
                onClick={onConfirm}
                className="!moe-ml-[8px] !moe-px-[20px] !moe-py-[6px] !moe-bg-[#f15a2b] !moe-text-white !moe-font-bold !moe-cursor-pointer !moe-rounded-full"
              >
                {confirmText}
              </span>
            )}
          </div>
        </div>
      }
      theme="black"
      {...popProps}
    >
      <div ref={ref} className="!moe-inline-block">
        {children}
      </div>
    </Tooltip>
  );
};
