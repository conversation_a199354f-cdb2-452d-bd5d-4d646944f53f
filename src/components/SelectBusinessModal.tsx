import { Heading, Modal, type ModalProps } from '@moego/ui';
import { useSelector } from 'amos';
import React, { memo, useEffect } from 'react';
import { CommonTestIds } from '../config/testIds/common';
import { currentBusinessIdBox } from '../store/business/business.boxes';
import { isNormal } from '../store/utils/identifier';
import { SingleLocationSelector } from './Business/SingleLocationSelector';
import { useDefaultLocationValue } from './Business/hooks/useLocationValue';
import { type LocationControlSceneKeys } from './WithFeature/useNewAccountStructure';

export interface SelectBusinessModalProps extends Omit<ModalProps, 'onConfirm'> {
  scene: LocationControlSceneKeys;
  onConfirm?: (value: string) => void;
  confirmButtonProps?: Partial<ModalProps['confirmButtonProps']>;
  confirmText?: string;
}

export const SelectBusinessModal = memo<SelectBusinessModalProps>((props) => {
  const { isOpen, onClose, onConfirm, scene, confirmButtonProps, confirmText } = props;
  const [businessId] = useSelector(currentBusinessIdBox);
  const [locationId, setLocationId] = useDefaultLocationValue({
    scene,
    preferred: 'current',
  });

  useEffect(() => {
    if (isNormal(businessId)) {
      setLocationId(String(businessId));
    }
  }, [businessId]);

  return (
    <Modal
      className="moe-w-[540px]"
      isOpen={isOpen}
      isBlockScroll
      isDismissable
      showCloseButton
      onClose={onClose}
      onConfirm={() => onConfirm?.(locationId!)}
      confirmButtonProps={{
        isDisabled: !locationId,
        ...confirmButtonProps,
      }}
      confirmText={confirmText}
      size="m"
      title="Select a business"
    >
      <div>
        <Heading className="moe-mb-[4px]" as="h6" size="6">
          Business
        </Heading>
        <div data-testid={CommonTestIds.SwitchBusinessBtn}>
          <SingleLocationSelector
            scene={scene}
            value={locationId}
            onChange={setLocationId}
            menuPosition="fixed"
            className="moe-w-[100%]"
          />
        </div>
      </div>
    </Modal>
  );
});
