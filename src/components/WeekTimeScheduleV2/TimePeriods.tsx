import classNames from 'classnames';
import React from 'react';
import { useControllableValue } from '../../utils/hooks/useControlledValue';
import { useLatestCallback } from '../../utils/hooks/useLatestCallback';
import { SelectTimePeriod } from './SelectTimePeriod';
import type { TimePeriod } from './types';

export interface TimePeriodsProps {
  value?: TimePeriod[];
  defaultValue?: TimePeriod[];
  onChange?: (v: TimePeriod[]) => void;
  onAdd?: (v: TimePeriod[]) => void;
  /** 新增下一项的默认时间 */
  nextPeriodValue?: (v: TimePeriod[]) => TimePeriod | void;
  addable?: (v: TimePeriod[], i: number) => boolean;
  deletable?: (v: TimePeriod[], i: number) => boolean;
  maxTime?: number;
  minuteStep?: number;
  className?: string;
  timeFormat?: string;
}

export function TimePeriods(props: TimePeriodsProps) {
  const {
    defaultValue = [{ startTime: 0, endTime: 60 }],
    maxTime = 1440,
    onAdd,
    nextPeriodValue,
    addable,
    deletable,
    minuteStep,
    className,
    timeFormat,
  } = props;
  const [values, setValues] = useControllableValue<TimePeriod[]>(props, { defaultValue });
  const allSize = values.length;
  const canAdd = values[allSize - 1]?.endTime < maxTime;

  const checkTimePeriodDisabled = useLatestCallback((v: number, index: number, pos: keyof TimePeriod) => {
    return pos === 'startTime'
      ? v >= values[index].endTime || (index > 0 && v < values[index - 1].endTime)
      : v <= values[index].startTime || (index !== allSize - 1 && v > values[index + 1].startTime);
  });

  const updateTimeRange = useLatestCallback((v: TimePeriod, index: number) => {
    const newValue = values.slice();
    newValue[index] = { ...v };
    setValues(newValue);
  });
  const moreThanOne = allSize > 1;

  return (
    <div className={classNames('moe-inline-flex moe-flex-col moe-gap-y-[16px]', className)}>
      {values.map((item, curIndex) => {
        const key = `${item.startTime}-${item.endTime}`;
        const isLast = curIndex === allSize - 1;
        return (
          <SelectTimePeriod
            timeFormat={timeFormat}
            minuteStep={minuteStep}
            key={key}
            value={item}
            onChange={(newItem) => updateTimeRange(newItem, curIndex)}
            startDisabled={(v) => checkTimePeriodDisabled(v, curIndex, 'startTime')}
            endDisabled={(v) => checkTimePeriodDisabled(v, curIndex, 'endTime')}
            deletable={deletable?.(values, curIndex) ?? moreThanOne}
            addable={addable?.(values, curIndex) ?? (isLast && canAdd)}
            onDelete={() => {
              const newRanges = values.filter((item, index) => index !== curIndex);
              setValues(newRanges);
            }}
            onAdd={() => {
              const last = values[values.length - 1];
              const newTimeRange = values.concat([
                nextPeriodValue?.(values) ?? {
                  // 最后结束时间 +5分钟
                  startTime: Math.min(last.endTime + 5, maxTime),
                  // 最后结束时间 +1小时5分钟
                  endTime: Math.min(last.endTime + 65, maxTime),
                },
              ]);
              setValues(newTimeRange);
              onAdd?.(newTimeRange);
            }}
          />
        );
      })}
    </div>
  );
}
