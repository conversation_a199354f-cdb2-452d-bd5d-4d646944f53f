import { MinorCopyOutlined } from '@moego/icons-react';
import { <PERSON><PERSON><PERSON><PERSON>on, Switch as MoeSwitch } from '@moego/ui';
import React, { type PropsWithChildren } from 'react';
import { type FullWeekDay, FullWeekDayList } from '../../store/onlineBooking/models/OnlineBookingPreference';
import { useControllableValue } from '../../utils/hooks/useControlledValue';
import { Switch } from '../SwitchCase';
import { TimePeriods, type TimePeriodsProps } from './TimePeriods';
import type { TimePeriod } from './types';

export interface WeekTimePeriodRowProps extends Pick<TimePeriodsProps, 'value' | 'onChange'> {
  day: number;
  renderEmpty?: (day: number) => React.ReactNode;
  onCopy?: (days: FullWeekDay) => void;
  onCheckChange?: (checked: boolean) => void;
  getDefaultTimePeriod?: () => TimePeriod[];
  timeFormat?: string;
}

// default = 09:00 ~ 19:00
export const DefaultWorkingHours = {
  StartTime: 540, // 09:00
  EndTime: 1140, // 19:00
};

export function WeekTimePeriodRow(props: PropsWithChildren<WeekTimePeriodRowProps>) {
  const {
    day,
    renderEmpty,
    onCopy,
    getDefaultTimePeriod = () => [{ startTime: DefaultWorkingHours.StartTime, endTime: DefaultWorkingHours.EndTime }], // default = 09:00 ~ 19:00
    children,
    onCheckChange,
    timeFormat,
  } = props;
  const dayOfWeek = FullWeekDayList[day];
  const [controlledValue, setValue] = useControllableValue<TimePeriod[]>(props, { defaultValue: [] });
  const value = Array.isArray(controlledValue) ? controlledValue : [];
  const hasValue = value.length > 0;

  return (
    <div className="moe-w-full moe-flex moe-py-[20px] moe-gap-x-[50px] moe-justify-between moe-items-center">
      <div className="moe-flex moe-mr-[80px]">
        <div className="moe-w-[152px] moe-pt-[7px] moe-select-none">
          <MoeSwitch
            isSelected={hasValue}
            onChange={(e) => {
              setValue(e ? getDefaultTimePeriod() : []);
              onCheckChange?.(e);
            }}
          >
            {dayOfWeek}
          </MoeSwitch>
        </div>
        <div>
          <Switch>
            <Switch.Case if={hasValue}>
              <div className="moe-flex moe-relative moe-gap-x-[12px]">
                <TimePeriods value={value} onChange={setValue} timeFormat={timeFormat} />
                {children}
              </div>
            </Switch.Case>
            <Switch.Case else>
              <div className="moe-font-medium moe-text-[14px] moe-text-[#999] moe-leading-[18px] moe-pt-[8px]">
                {renderEmpty?.(day) ?? `Not working at ${dayOfWeek}`}
              </div>
            </Switch.Case>
          </Switch>
        </div>
      </div>
      <IconButton icon={<MinorCopyOutlined />} onPress={() => onCopy?.(dayOfWeek)}></IconButton>
    </div>
  );
}
