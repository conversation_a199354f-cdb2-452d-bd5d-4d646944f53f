import { MinorPlusOutlined, MinorTrashOutlined } from '@moego/icons-react';
import { IconButton } from '@moego/ui';
import classNames from 'classnames';
import React from 'react';
import { useControllableValue } from '../../utils/hooks/useControlledValue';
import { Condition } from '../Condition';
import { TimePicker } from '../form/TimePicker';
import type { TimePeriod } from './types';

export interface SelectTimePeriodProps {
  className?: string;
  disabled?: boolean;
  value?: TimePeriod;
  minuteStep?: number;
  onChange?: (range: TimePeriod) => void;
  startDisabled?: (value: number) => boolean;
  endDisabled?: (value: number) => boolean;
  deletable?: boolean;
  addable?: boolean;
  onDelete?: () => void;
  onAdd?: () => void;
  timeFormat?: string;
}

export function SelectTimePeriod(props: SelectTimePeriodProps) {
  const {
    className,
    deletable,
    startDisabled,
    endDisabled,
    onDelete,
    addable,
    onAdd,
    disabled,
    minuteStep,
    timeFormat,
  } = props;
  const [value, setValue] = useControllableValue<TimePeriod>(props, {
    defaultValue: { startTime: 0, endTime: 60 },
  });

  return (
    <div className={classNames('moe-flex moe-items-center moe-gap-x-[16px]', className)}>
      <div className="moe-flex moe-items-center moe-justify-between moe-gap-x-[16px]">
        <TimePicker
          className="moe-w-[172px]"
          minuteStep={minuteStep}
          isDisabled={disabled}
          value={value.startTime}
          disabledTimes={startDisabled}
          format={timeFormat}
          onChange={(v) => setValue((pre) => ({ ...pre, startTime: v! }))}
          classNames={{
            clearIcon: '!moe-hidden',
            calendarIcon: '!moe-flex',
          }}
        />
        <div className="moe-text-regular-short moe-text-tertiary moe-shrink-0">to</div>
        <TimePicker
          className="moe-w-[172px]"
          minuteStep={minuteStep}
          isDisabled={disabled}
          value={value.endTime}
          disabledTimes={endDisabled}
          format={timeFormat}
          onChange={(v) => setValue((pre) => ({ ...pre, endTime: v! }))}
          classNames={{
            clearIcon: '!moe-hidden',
            calendarIcon: '!moe-flex',
          }}
        />
      </div>
      <Condition if={!disabled}>
        <div className="moe-absolute moe-right-[-16px] moe-gap-x-[12px] moe-flex moe-translate-x-full">
          <Condition if={deletable}>
            <IconButton icon={<MinorTrashOutlined />} onPress={onDelete}></IconButton>
          </Condition>
          <Condition if={addable}>
            <IconButton icon={<MinorPlusOutlined />} onPress={onAdd}></IconButton>
          </Condition>
        </div>
      </Condition>
    </div>
  );
}
