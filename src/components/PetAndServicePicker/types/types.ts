import { type AppointmentPetFeedingScheduleDef } from '@moego/api-web/moego/models/appointment/v1/appointment_pet_feeding_schedule_defs';
import { type AppointmentPetMedicationScheduleDef } from '@moego/api-web/moego/models/appointment/v1/appointment_pet_medication_schedule_defs';
import { type ServiceEntry } from '../../ServiceApplicablePicker/types/serviceEntry';

export interface RenderServiceProps extends ServiceEntry {
  feedings?: Partial<AppointmentPetFeedingScheduleDef>[];
  medications?: Partial<AppointmentPetMedicationScheduleDef>[];
}

export interface RenderService extends RenderServiceProps {
  children?: RenderServiceProps[];
}

export interface PetServicePriceContentProps {
  priceContent?: (serviceId: string, defaultContent: React.ReactNode) => React.ReactNode;
}
