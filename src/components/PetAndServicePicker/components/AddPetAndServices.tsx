import { MajorPlusOutlined } from '@moego/icons-react';
import { Heading, IconButton } from '@moego/ui';
import React from 'react';
import { ApptTestIds } from '../../../config/testIds/apptDrawer';
import { Tooltip } from '../../Popup/Tooltip';

export interface AddPetAndServicesProps {
  onClick?: () => void;
  overlay?: React.ReactNode;
  disabledOverlay?: boolean;
}

export function AddPetAndServices(props: AddPetAndServicesProps) {
  const { overlay, disabledOverlay, onClick } = props;

  return (
    <Tooltip
      disabled={disabledOverlay}
      placement="top"
      overlay={overlay}
      width={320}
      overlayInnerStyle={{ padding: 0, width: 320 }}
    >
      <div
        className="moe-flex moe-items-center moe-gap-x-[16px] moe-p-s moe-border moe-border-button moe-border-solid moe-rounded-m moe-cursor-pointer hover:moe-bg-[#F2F3F6]"
        onClick={onClick}
        data-testid={ApptTestIds.ApptNewPetServicesBtn}
      >
        <IconButton variant="primary" icon={<MajorPlusOutlined />} onPress={onClick} />
        <Heading size="5" className="moe-text-[16px]  moe-leading-[20px] moe-select-none moe-font-bold moe-text-[#666]">
          Pets and services
        </Heading>
      </div>
    </Tooltip>
  );
}
