import { type ServiceItemType, ServiceType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import React from 'react';
import { PetAddOnItem } from '../../ServiceApplicablePicker/components/ServiceCard/PetAddOnItem';
import { PetServiceItem } from '../../ServiceApplicablePicker/components/ServiceCard/PetServiceItem';
import { ServiceCard } from '../../ServiceApplicablePicker/components/ServiceCard/ServiceCard';
import { ServiceChildAddon } from '../../ServiceApplicablePicker/components/ServiceCard/ServiceChildAddon';
import { Switch } from '../../SwitchCase';
import { type RenderService } from '../types/types';
import { type PetServiceCardProps } from './PetServiceCard';

export interface ServiceCardWithOrderProps
  extends Pick<PetServiceCardProps, 'disabled' | 'suffix' | 'priceContent' | 'petId'> {
  mainServiceItemType: ServiceItemType;
  serviceItemType: ServiceItemType;
  list: RenderService[];
}

export const ServiceCardWithOrder = (props: ServiceCardWithOrderProps) => {
  const { list, disabled, serviceItemType, mainServiceItemType, petId, priceContent, suffix } = props;
  return (
    <ServiceCard childClassName="moe-flex moe-flex-col moe-gap-y-[12px]" disabled={disabled}>
      {() => {
        return (
          <>
            {/* service / addon 有order 控制顺序，grooming report 需要跟它隔离开 */}
            <div className="moe-flex moe-flex-col moe-gap-y-8px-150">
              {list.map((s) => (
                <Switch key={s.id}>
                  <Switch.Case if={s.serviceType === ServiceType.SERVICE}>
                    <React.Fragment>
                      {/* 对于一个service组件的渲染，也许需要关心整体本身是一个什么类型的appt，所以传入mainServiceItemType */}
                      <PetServiceItem
                        service={s}
                        mainServiceItemType={mainServiceItemType}
                        priceContent={priceContent}
                      />
                      <ServiceChildAddon list={s.children} mainServiceItemType={mainServiceItemType} />
                    </React.Fragment>
                  </Switch.Case>
                  <Switch.Case else>
                    <PetAddOnItem service={s} mainServiceItemType={mainServiceItemType} />
                  </Switch.Case>
                </Switch>
              ))}
            </div>
            {suffix?.(petId, serviceItemType)}
          </>
        );
      }}
    </ServiceCard>
  );
};
