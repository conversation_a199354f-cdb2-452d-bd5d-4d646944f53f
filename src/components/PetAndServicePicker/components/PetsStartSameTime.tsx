import { Checkbox, Text } from '@moego/ui';
import React, { memo } from 'react';
import { ApptTestIds } from '../../../config/testIds/apptDrawer';

export interface PetsStartSameTimeProps {
  isStartSameTime?: boolean;
  isDisabled?: boolean;
  onStartSameTimeChange?: (value: boolean) => void;
}

export const PetsStartSameTime = memo<PetsStartSameTimeProps>((props) => {
  const { onStartSameTimeChange, isStartSameTime, isDisabled } = props;

  return (
    <Checkbox
      isDisabled={isDisabled}
      isSelected={isStartSameTime}
      onChange={onStartSameTimeChange}
      data-testid={ApptTestIds.ApptMultiPetStartSameTimeCb}
    >
      <Text variant="small">Multi pets start at the same time</Text>
    </Checkbox>
  );
});
