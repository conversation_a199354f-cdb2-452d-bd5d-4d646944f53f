import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { findLastIndex } from 'lodash';
import { isNormal } from '../../../store/utils/identifier';
import { type ServiceEntry } from '../../ServiceApplicablePicker/types/serviceEntry';
import { type RenderService } from '../types/types';
import { useSortServiceListByOrder } from '../../../container/Appt/hooks/useSortServiceListByOrder';
import { useCallback } from 'react';

/**
 * 根据service分组来展示，核心规则就是，如果有associatedId，说明是addon，就放到对应的service下面
 * 否则就是顶级service，当然顶级service也有可能是addon，但是没有associatedId
 */
export const useGroupByService = () => {
  const sortServiceListByOrder = useSortServiceListByOrder();

  return useCallback(
    (services: ServiceEntry[]) => {
      const map: Record<string, RenderService['children']> = {};
      const result: RenderService[] = [];

      services.forEach((service) => {
        const { associatedId, id, serviceId } = service;
        if (!isNormal(serviceId)) return;
        map[id] = map[id] ?? [];
        if (associatedId) {
          map[associatedId] = map[associatedId] ?? [];
          map[associatedId]!.push(service);
        } else {
          result.push({
            ...service,
            children: map[id],
          });
        }
      });
      const groupedServiceList = sortServiceListByOrder(result);
      const lastDaycareIndex = findLastIndex(
        groupedServiceList,
        (service) => service.serviceItemType === ServiceItemType.DAYCARE,
      );
      const lastBoardingIndex = findLastIndex(
        groupedServiceList,
        (service) => service.serviceItemType === ServiceItemType.BOARDING,
      );

      return {
        groupedServiceList,
        isShowMoreAddonOrService: services
          .filter((s) => !s.associatedId)
          .some((service) => service.serviceItemType === ServiceItemType.GROOMING),
        lastDaycareIndex,
        lastBoardingIndex,
      };
    },
    [sortServiceListByOrder],
  );
};
