import React, { type ReactNode } from 'react';
import ImageOrangeThinkingCatPng from '../assets/image/orange-thinking-cat.png';

interface Props {
  message: string;
  buttons: ReactNode;
}

export const BundleSaleError = ({ message, buttons }: Props) => {
  return (
    <div className="moe-flex moe-flex-col moe-items-center moe-gap-[24px]">
      <img src={ImageOrangeThinkingCatPng} width={140} height={140} />
      <div className="moe-max-w-[580px] moe-mx-[20px] moe-text-center">
        <div className="moe-text-[20px] moe-leading-[24px] moe-font-bold moe-text-[#333]">Coming soon...</div>
        <div className="moe-mt-[8px] moe-text-[14px] moe-leading-[18px] moe-font-medium moe-text-[#666]">{message}</div>
      </div>
      <div className="moe-flex moe-gap-[12px]">{buttons}</div>
    </div>
  );
};
