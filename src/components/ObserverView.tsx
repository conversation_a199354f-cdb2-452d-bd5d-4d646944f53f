import { datadogRum } from '@datadog/browser-rum';
import React, { useEffect, useRef, useCallback, useMemo } from 'react';

class ObserverManager {
  private static instance: ObserverManager;
  private observers = new Map<string, IntersectionObserver>();
  private callbacks = new Map<string, Set<(entry: IntersectionObserverEntry) => void>>();

  static getInstance(): ObserverManager {
    if (!ObserverManager.instance) {
      ObserverManager.instance = new ObserverManager();
    }
    return ObserverManager.instance;
  }

  private createObserver(threshold: number): IntersectionObserver {
    return new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          const elementId = this.getElementId(entry.target);
          const callbacks = this.callbacks.get(elementId);
          callbacks?.forEach((callback) => callback(entry));
        });
      },
      { threshold },
    );
  }

  private getElementId(element: Element): string {
    return element.getAttribute('data-observer-id') || '';
  }

  subscribe(elementId: string, threshold: number, callback: (entry: IntersectionObserverEntry) => void): () => void {
    const key = `${elementId}-${threshold}`;

    if (!this.observers.has(key)) {
      this.observers.set(key, this.createObserver(threshold));
    }

    if (!this.callbacks.has(elementId)) {
      this.callbacks.set(elementId, new Set());
    }

    this.callbacks.get(elementId)!.add(callback);

    return () => {
      this.callbacks.get(elementId)?.delete(callback);
      if (this.callbacks.get(elementId)?.size === 0) {
        this.callbacks.delete(elementId);
        // 当没有回调时，清理对应的 observer
        this.cleanupObserver(key);
      }
    };
  }

  observe(element: Element, elementId: string, threshold: number): void {
    const key = `${elementId}-${threshold}`;
    const observer = this.observers.get(key);
    if (observer) {
      observer.observe(element);
    }
  }

  unobserve(element: Element, elementId: string, threshold: number): void {
    const key = `${elementId}-${threshold}`;
    const observer = this.observers.get(key);
    if (observer) {
      observer.unobserve(element);
    }
  }

  private cleanupObserver(key: string): void {
    const observer = this.observers.get(key);
    if (observer) {
      observer.disconnect();
      this.observers.delete(key);
    }
  }
}

export interface ObserverViewProps {
  children: React.ReactNode;
  intersectRatio?: number; // 与当前可是区域交叉比例，可以理解为当看见该组件多少的时候进行曝光上报。
  once?: boolean; // 是否只上报一次 默认ture
  viewActionName: string; // 曝光上报的action name
  clickActionName?: string; // 点击动作上报的action name
  extra?: Record<string, string | number>; // 额外参数
  as?: React.ElementType; //视为什么元素 // 默认div
  className?: string;
  style?: React.CSSProperties;
  minVisibleTime?: number; // 最小可见时间（毫秒），只有停留足够时间才算曝光，默认 500ms
}

export const ObserverView = React.forwardRef<HTMLElement, ObserverViewProps>(
  (
    {
      children,
      intersectRatio = 0.5,
      once = true,
      viewActionName,
      clickActionName,
      extra,
      as: Component = 'div',
      className,
      style,
      minVisibleTime = 500, // 默认 500ms
      ...props
    },
    ref,
  ) => {
    const elementRef = useRef<HTMLElement | null>(null);
    const observerManager = useMemo(() => ObserverManager.getInstance(), []);
    const elementId = useMemo(() => `observer-${Math.random().toString(36).substr(2, 9)}`, []);
    const hasReported = useRef(false);
    const visibleStartTime = useRef<number | null>(null);
    const timeoutRef = useRef<NodeJS.Timeout | null>(null);

    const handleIntersection = useCallback(
      (entry: IntersectionObserverEntry) => {
        if (entry.isIntersecting && !hasReported.current) {
          visibleStartTime.current = Date.now();

          // 设置定时器，只有停留足够时间才算曝光
          timeoutRef.current = setTimeout(() => {
            if (visibleStartTime.current && !hasReported.current) {
              hasReported.current = true;

              datadogRum.addAction(viewActionName, {
                intersectRatio,
                extra,
                visibleTime: Date.now() - visibleStartTime.current,
              });

              if (once) {
                // 如果是一次性上报，取消订阅
                const element = elementRef.current;
                if (element) {
                  observerManager.unobserve(element, elementId, intersectRatio);
                }
              }
            }
          }, minVisibleTime);
        } else if (!entry.isIntersecting) {
          // 元素离开可视区域，清除定时器和时间记录
          if (timeoutRef.current) {
            clearTimeout(timeoutRef.current);
            timeoutRef.current = null;
          }
          visibleStartTime.current = null;
        }
      },
      [viewActionName, intersectRatio, extra, once, observerManager, elementId, minVisibleTime],
    );

    useEffect(() => {
      const element = elementRef.current;
      if (!element) return;

      element.setAttribute('data-observer-id', elementId);

      const unsubscribe = observerManager.subscribe(elementId, intersectRatio, handleIntersection);
      observerManager.observe(element, elementId, intersectRatio);

      return () => {
        unsubscribe();
        observerManager.unobserve(element, elementId, intersectRatio);
        // 清理定时器
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
        }
      };
    }, [elementId, intersectRatio, handleIntersection, observerManager]);

    const combinedRef = useCallback(
      (node: HTMLElement | null) => {
        elementRef.current = node;
        if (typeof ref === 'function') {
          ref(node);
        } else if (ref) {
          ref.current = node;
        }
      },
      [ref],
    );

    return (
      <Component ref={combinedRef} className={className} style={style} data-dd-action-name={clickActionName} {...props}>
        {children}
      </Component>
    );
  },
);
