import { Button, Radio, Tooltip } from 'antd';
import React, { type CSSProperties, memo, useState } from 'react';
import { createEnum } from '../../store/utils/createEnum';
import { useSerialCallback } from '../../utils/hooks/useSerialCallback';
import { AlertCard } from '../Alert/AlertCard';
import { type ModalProps } from '../Modal/Modal';
import { ModalRepeatApplyView } from './ModalRepeatApply.style';

const { Group } = Radio;

export const RepeatTicketApplyTypes = createEnum({
  ONE: [1, 'Only this one'],
  FOLLOWING: [2, 'Apply to this and all following {mode}'],
  ALL: [3, 'Apply to all {mode} in this repeat series'],
});

export interface ModalRepeatApplyProps extends ModalProps {
  onConfirm: (value: number) => void;
  autoCloseAfterConfirm?: boolean;
  mode?: 'appointments' | 'blocks';
  msg?: React.ReactNode;
  preAuthReleaseMsg?: React.ReactNode;
}

export const ModalRepeatApply = memo<ModalRepeatApplyProps>(
  ({
    onConfirm,
    autoCloseAfterConfirm = true,
    mode = 'appointments',
    onClose,
    msg,
    preAuthReleaseMsg,
    ...restProps
  }) => {
    const initialValue = RepeatTicketApplyTypes.ONE;
    const [value, setValue] = useState(initialValue);
    const radioStyle = {
      display: 'block',
    } as CSSProperties;

    const handleConfirm = useSerialCallback(async () => {
      await onConfirm(value);
      autoCloseAfterConfirm && onClose();
    });

    return (
      <ModalRepeatApplyView
        width="600px"
        title="Save update"
        onClose={onClose}
        wrapperStyle={{
          zIndex: 5000,
        }}
        bodyStyle={{ padding: '24px 32px' }}
        {...restProps}
        footer={
          <>
            <Button onClick={() => onClose()} shape="round" className="!moe-text-[#333] !moe-shadow-none">
              Cancel
            </Button>
            <Button
              onClick={handleConfirm}
              loading={handleConfirm.isBusy()}
              shape="round"
              type="primary"
              className="!moe-ml-[16px] !moe-shadow-none"
            >
              Confirm
            </Button>
          </>
        }
      >
        <div className="save-update-content">
          {Boolean(msg || preAuthReleaseMsg) && <AlertCard message={[msg, preAuthReleaseMsg]} />}

          <Group
            onChange={(e) => {
              const v = e.target.value;
              setValue(v);
            }}
            value={value}
            className="!moe-pl-[16px]"
            defaultValue={initialValue}
          >
            <Tooltip title={`This ${mode.replace(/s$/, '')} will be deleted from repeat series`}>
              <Radio style={radioStyle} value={RepeatTicketApplyTypes.ONE}>
                Only this one
              </Radio>
            </Tooltip>
            <Radio style={radioStyle} value={RepeatTicketApplyTypes.FOLLOWING}>
              Apply to this and all following {mode}
            </Radio>
            <Radio style={radioStyle} value={RepeatTicketApplyTypes.ALL}>
              Apply to all {mode} in this repeat series
            </Radio>
          </Group>
        </div>
      </ModalRepeatApplyView>
    );
  },
);
