import styled from 'styled-components';
import { Modal } from '../Modal/Modal';

export const ModalRepeatApplyView = styled(Modal)`
  .ant-modal-body {
    padding: 0;
    border-radius: 8px;
    color: #4a4a4a;
    background-color: #fff;

    .save-update-content {
      padding: 35px 80px;

      .ant-radio-group {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        align-items: center;

        .ant-radio-wrapper {
          & + .ant-radio-wrapper {
            margin-top: 8px;
          }
        }
      }
    }
  }

  .save-update-content {
    .ant-radio-group {
      display: flex;
      flex-direction: column;
      row-gap: 16px;
    }
  }
`;
