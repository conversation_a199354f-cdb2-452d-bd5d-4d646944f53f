import classNames from 'classnames';
import React, { memo } from 'react';
import { printFullName } from '../../store/customer/customer.boxes';
import { Avatar, type AvatarProps } from '../Avatar/Avatar';
import { Tooltip } from '../Popup/Tooltip';

export interface ReviewDetailStaffListProps {
  size?: AvatarProps['size'];
  className?: string;
  avatarClassName?: string;
  staffList?: { avatarPath?: string; firstName?: string; lastName?: string }[];
  /* 是否展示tool tip */
  tip?: boolean;
  /** staff多于1个，是否简写 */
  abbreviation?: boolean;
}

export const MultiStaffWithAvatar = memo(
  ({
    className,
    staffList = [],
    size = '20px',
    avatarClassName = '!moe-border !moe-border-solid !moe-border-white !moe-cursor-default',
    tip,
    abbreviation,
  }: ReviewDetailStaffListProps) => {
    const staffSize = staffList.length;
    const staffNameText = staffList.map((staff) => staff.firstName ?? '').join(',\u{20}');
    const toolTipTxt = staffList
      .map((staff) => printFullName(staff.firstName ?? '', staff.lastName ?? ''))
      .join(',\u{20}');
    const summaryTxt =
      abbreviation && staffSize > 1 ? `${staffList[0].firstName ?? ''}, +${staffSize - 1}` : staffNameText;

    return (
      <div
        className={classNames(
          className,
          'moe-flex moe-items-center moe-text-[12px] moe-text-[#333] moe-leading-[16px]',
        )}
      >
        {staffList.map((staff, index) => {
          return (
            <div className="moe-bg-white moe-rounded-[20px] moe-mr-[-8px]" key={index}>
              <Avatar src={staff.avatarPath} key={index} size={size} className={avatarClassName} />
            </div>
          );
        })}
        <Tooltip
          theme="black"
          disabled={!tip}
          overlay={<div className="moe-text-[12px] moe-w-[254px]">{toolTipTxt}</div>}
          width={254}
          placement="topRight"
        >
          <div className="moe-ml-[12px] moe-cursor-default moe-text-[14px] moe-leading-[24px] moe-truncate moe-max-w-[150px]">
            {summaryTxt}
          </div>
        </Tooltip>
      </div>
    );
  },
);
