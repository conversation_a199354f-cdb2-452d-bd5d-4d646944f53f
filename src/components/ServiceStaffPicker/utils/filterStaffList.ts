import { type ListServiceStaffsResultStaffWithServicePriceAndDurationView } from '@moego/api-web/moego/api/offering/v1/service_staff_api';

type PartialKeys<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

export interface StaffWithServicePriceDurationView
  extends PartialKeys<ListServiceStaffsResultStaffWithServicePriceAndDurationView, 'servicePrice' | 'serviceDuration'> {
  isDisabled?: boolean;
}

export interface FilterStaffListParams {
  isAvailable: boolean;
  filterStaffIds?: number[];
  staffList?: StaffWithServicePriceDurationView[];
}

export const filterStaffList = ({ staffList, isAvailable, filterStaffIds }: FilterStaffListParams) => {
  return (
    staffList?.filter(
      (staff) =>
        staff.isAvailable === isAvailable && (filterStaffIds ? filterStaffIds.includes(Number(staff.staffId)) : true),
    ) ?? []
  );
};

export const isSelectedStaff = (staff: StaffWithServicePriceDurationView, targetStaffId?: number) => {
  return staff.staffId === targetStaffId?.toString();
};
