import React, { memo } from 'react';
import { TagServiceDuration } from '../../ServiceApplicablePicker/components/TagService/TagServiceDuration';
import { TagServicePrice } from '../../ServiceApplicablePicker/components/TagService/TagServicePrice';
import { type StaffWithServicePriceDurationView } from '../utils/filterStaffList';

export interface ServicePriceDurationForStaffProps {
  servicePriceDuration: StaffWithServicePriceDurationView;
}

export const ServicePriceDurationForStaff = memo<ServicePriceDurationForStaffProps>(({ servicePriceDuration }) => {
  const { servicePrice, serviceDuration, priceOverrideType, durationOverrideType } = servicePriceDuration;

  return (
    <div className="moe-flex moe-gap-xs moe-flex-shrink-0">
      <TagServicePrice price={servicePrice} overrideType={priceOverrideType} />
      <TagServiceDuration duration={serviceDuration} overrideType={durationOverrideType} />
    </div>
  );
});
