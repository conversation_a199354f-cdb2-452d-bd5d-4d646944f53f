import { Checkbox, Text } from '@moego/ui';
import React, { memo } from 'react';
import SvgIconMultipleStaffSvg from '../../../assets/svg/icon-multiple-staff.svg';
import { ApptTestIds } from '../../../config/testIds/apptDrawer';
import { Condition } from '../../Condition';
import { SvgIcon } from '../../Icon/Icon';
import { Tooltip } from '../../Popup/Tooltip';
import { MultipleStaffId } from '../utils/multipleStaffId';
import { AssignMultiStaffTip } from './AssignMultiStaffTip';

const AssignToMultiStaffTip = 'Assign to Multi-staff';

export interface SelectMultiStaffFooterProps {
  showMultiStaffOption: boolean;
  showUpgradeForMultiStaffTip: boolean;
  showPriceDuration?: boolean;
  onlyAvailableStaff?: boolean;
  onClick?: (multipleStaffId: number) => void;
  onUpgrade?: () => void;
  onCheckOnlyAvailableStaff?: (checked: boolean) => void;
}

export const SelectMultiStaffFooter = memo<SelectMultiStaffFooterProps>((props) => {
  const {
    showMultiStaffOption,
    showUpgradeForMultiStaffTip,
    showPriceDuration,
    onlyAvailableStaff,
    onClick,
    onUpgrade,
    onCheckOnlyAvailableStaff,
  } = props;

  return (
    <div className="moe-flex moe-justify-end moe-items-center">
      <Condition if={showMultiStaffOption}>
        <Tooltip
          disabled={!showUpgradeForMultiStaffTip}
          overlay={<AssignMultiStaffTip onUpgrade={onUpgrade} />}
          width={258}
          placement="left"
          align={{ offset: [-4, 0] }}
        >
          <div
            className="moe-flex-1 moe-flex moe-items-center moe-gap-x-[5px]  moe-cursor-pointer hover:moe-bg-neutral-sunken-0 moe-rounded-s moe-px-spacing-xs moe-py-[10px]"
            title={AssignToMultiStaffTip}
            onClick={() => {
              if (showUpgradeForMultiStaffTip) {
                return;
              }
              onClick?.(MultipleStaffId);
            }}
            data-testid={ApptTestIds.ApptEditPetServiceSelectMultiStaffBtn}
          >
            <SvgIcon src={SvgIconMultipleStaffSvg} size={18} />
            {AssignToMultiStaffTip}
          </div>
        </Tooltip>
      </Condition>
      <Condition if={showMultiStaffOption && showPriceDuration}>
        <div className="moe-h-[20px] moe-mx-xs moe-border-r moe-border-divider" />
      </Condition>
      <Condition if={showPriceDuration}>
        <Checkbox isSelected={onlyAvailableStaff} onChange={onCheckOnlyAvailableStaff}>
          <Text variant="small" className="moe-text-primary moe-pr-spacing-xs">
            Only show applicable staff
          </Text>
        </Checkbox>
      </Condition>
    </div>
  );
});
