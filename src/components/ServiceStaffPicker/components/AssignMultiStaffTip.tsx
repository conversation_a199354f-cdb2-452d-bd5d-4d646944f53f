import React from 'react';
import IconIconCrownSvg from '../../../assets/icon/icon-crown.svg';
import { NavPrimaryButton } from '../../../layout/components/Navbar/NavButton.style';
import { ImgIcon } from '../../Icon/Icon';

export interface AssignMultiStaffTipProps {
  onUpgrade?: () => void;
}

export function AssignMultiStaffTip(props: AssignMultiStaffTipProps) {
  const { onUpgrade } = props;

  return (
    <div>
      <div className="!moe-flex !moe-items-start !moe-gap-x-[7px]">
        <ImgIcon className="!moe-mr-[0px] !moe-mt-[-2px]" src={IconIconCrownSvg} width={24} />
        <div>
          <div className="!moe-text-[16px] !moe-text-[#333] !moe-leading-[20px]">Assign to Multi-staff</div>
          <div className="!moe-mt-[8px] !moe-text-[14px] !moe-text-[#666] !moe-leading-[18px]">
            Upgrade to streamline collaboration across multiple staff!
          </div>
        </div>
      </div>
      <NavPrimaryButton className="!moe-mt-[16px] !moe-w-full !moe-justify-center" onClick={onUpgrade}>
        Upgrade
      </NavPrimaryButton>
    </div>
  );
}
