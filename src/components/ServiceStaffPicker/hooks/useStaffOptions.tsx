import React, { useMemo } from 'react';
import { printFullName } from '../../../store/customer/customer.boxes';
import { ServicePriceDurationForStaff } from '../components/ServicePriceDurationForStaff';
import { type StaffWithServicePriceDurationView } from '../utils/filterStaffList';

export interface UseStaffOptionsProps {
  availableStaffList: StaffWithServicePriceDurationView[];
  unavailableStaffList: StaffWithServicePriceDurationView[];
  showDesc: boolean;
}

export const useStaffOptions = ({ availableStaffList, unavailableStaffList, showDesc }: UseStaffOptionsProps) => {
  const mapStaffList = (staffInfoWithServicePriceDuration: StaffWithServicePriceDurationView) => {
    const { staffId, firstName, lastName, isDisabled = false } = staffInfoWithServicePriceDuration;
    return {
      value: Number(staffId),
      label: printFullName(firstName, lastName),
      isDisabled,
      description:
        showDesc && !isDisabled ? (
          <ServicePriceDurationForStaff servicePriceDuration={staffInfoWithServicePriceDuration} />
        ) : undefined,
    };
  };

  return useMemo(() => {
    return [availableStaffList, unavailableStaffList].map((group, index) => {
      return {
        label: index ? 'Unavailable staff' : '',
        options: group.map(mapStaffList),
      };
    });
  }, [availableStaffList, unavailableStaffList, showDesc]);
};
