import { useSelector } from 'amos';
import dayjs from 'dayjs';
import { useMemo } from 'react';
import { getShowOnCalendarStaff } from '../../../store/calendarLatest/actions/public/calendar.actions';
import { selectBusinessStaffs } from '../../../store/staff/staff.selectors';
import { useQuery } from '../../../store/utils/useQuery';
import { DATE_FORMAT_EXCHANGE } from '../../../utils/DateTimeUtil';

/**
 * 使用这个组件就意味着必会调1次接口。这个可不优化，本身影响微乎其微。
 * 为什么不用staffsForCalendarBox 先判断是否存在，首先是useQuery没有手动的开关，这个可以优化
 * 但是重点还是，staffsForCalendarBox只有在calendar 页面才回更新，其他页面不会更新，所以这个不适用。
 */
export function useShowOnCalendarStaff() {
  const [bizStaffs] = useSelector(selectBusinessStaffs);

  const currentDate = dayjs().format(DATE_FORMAT_EXCHANGE);
  const { value } = useQuery(
    getShowOnCalendarStaff({
      startDate: currentDate,
      endDate: currentDate,
    }),
  );

  return useMemo(() => (value?.length ? value.map(({ id }) => id) : bizStaffs.toArray()), [bizStaffs, value]);
}
