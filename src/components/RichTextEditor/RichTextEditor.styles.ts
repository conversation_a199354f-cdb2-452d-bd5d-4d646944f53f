import styled from 'styled-components';

export const StyledRichTextEditor = styled.div<{ hasError?: boolean }>`
  .moego-rich-editor {
    a[data-mce-selected='inline-boundary'] {
      outline: 3px solid #b4d7ff;
    }
  }
  .tox-tinymce {
    border: ${(props) => (props.hasError ? '1px solid #D0021B' : '1px solid #CDCDCD')};
    border-radius: 4px;
  }
  .tox .tox-tbtn {
    color: #666666;
  }
  .tox-editor-header {
    position: sticky;
  }
`;
