// 这个编辑器主要提供给 Marketing Campaign 使用，做了一些二次封装
import { Editor } from '@tinymce/tinymce-react';
import { useSelector } from 'amos';
import React, { forwardRef, useImperativeHandle, useMemo, useRef } from 'react';
import { useUnmount } from 'react-use';
import { type Editor as TinyMCE } from 'tinymce';
import { RICHTEXT_API_KEY, RICHTEXT_INIT_CONFIG } from '../../config/tinymaceEditor';
import { selectBDFeatureEnable } from '../../store/company/company.selectors';
import { useBool } from '../../utils/hooks/useBool';
import { useControllableValue } from '../../utils/hooks/useControlledValue';
import { useLatestCallback } from '../../utils/hooks/useLatestCallback';
import { alertApi } from '../Alert/AlertApi';
import { Condition } from '../Condition';
import { Loading } from '../Loading/Loading';
import { PluginEntry } from './RichEditorPlugins/PluginEntry';
import {
  useGetAgreementList,
  useGetIntakeFormList,
  useGetMembershipList,
  useGetOnlineBookingURL,
} from './RichEditorPlugins/URLOrPlaceholderPlugin';
import { type EditorComponentApi, type EditorInitOptions } from './RichEditorPlugins/types';
import { type UploadFileInfo, handleImageUpload } from './RichEditorPlugins/upload.utils';
import { StyledRichTextEditor } from './RichTextEditor.styles';
import { EDITOR_COMMAND_INVALID_BUTTON_DIALOG } from './const';

const plugins: string[] = [];

interface RichTextEditorProps {
  options?: EditorInitOptions;
  defaultValue?: string;
  value?: string;
  onChange?: (content: string) => void;
  handleUploadAttachment?: (file?: File) => Promise<void>;

  onBlur?: (content: string) => void;
  onFocus?: () => void;
  onDirty?: () => void;
  hasError?: boolean;
  onInit?: (editor: TinyMCE) => void;
  setContentAfterInit?: boolean;
}

export interface RichTextEditorRef {
  openInvalidButtonDialog: () => void;
}

export const RichTextEditor = forwardRef<RichTextEditorRef, RichTextEditorProps>((props: RichTextEditorProps, ref) => {
  const editorRef = useRef<TinyMCE | null>(null);
  const { options } = props;
  const loading = useBool(true);
  const setContentAfterInit = useBool(!!props.setContentAfterInit && props.value === '');
  const [isBDEnabled] = useSelector(selectBDFeatureEnable);
  const init: EditorInitOptions = useMemo(
    () => ({
      ...RICHTEXT_INIT_CONFIG,
      ...(options || {}),
      remove_trailing_brs: false,
    }),
    [],
  );

  const openInvalidButtonDialog = () => {
    editorRef.current?.execCommand(EDITOR_COMMAND_INVALID_BUTTON_DIALOG);
  };

  useImperativeHandle(ref, () => ({
    openInvalidButtonDialog,
  }));

  const pluginEntry = useMemo(() => {
    return new PluginEntry();
  }, []);

  const handleGetIntakeFormList = useGetIntakeFormList();
  const handleGetAgreementList = useGetAgreementList();
  const handleGetOnlineBookingURL = useGetOnlineBookingURL();
  const handleGetMembershipList = useGetMembershipList();
  const handleGetBoardingDaycareEnabled = useLatestCallback(() => {
    return isBDEnabled;
  });
  // heavily modified
  const initOptions = useLatestCallback(() => {
    const api: EditorComponentApi = {
      getIntakeFormList: handleGetIntakeFormList,
      getAgreementList: handleGetAgreementList,
      getOnlineBookingURL: handleGetOnlineBookingURL,
      getMembershipList: handleGetMembershipList,
      getBDEnabled: handleGetBoardingDaycareEnabled,
      handleLoading: (showLoading: boolean) => {
        if (showLoading) {
          loading.open();
        } else {
          loading.close();
        }
      },
      handleUploadAttachment: props.handleUploadAttachment ?? (() => Promise.resolve()),
      // extend design, not use now
      getDefaultData: () => {
        return null;
      },
    };
    return pluginEntry.resolveInitOptions(init, api);
  });

  const [content, setContent] = useControllableValue(props);

  const handleContentChange = useLatestCallback((content: string) => {
    /**
     * 当tinyMCE编辑器内容以</div>结尾时，会出现无法在div后面插入内容的问题，这里在内容结尾加一个空行，解决这个问题
     * 涉及到 src/components/RichTextEditor/RichEditorPlugins/HighlightSectionPlugin.ts 这个插件
     * @see https://moegoworkspace.slack.com/archives/C01TT9K995M/p1741228440944859
     */
    if (content?.endsWith('</div>')) {
      content = content.concat('<p>&nbsp;</p>');
    }
    setContent(content);
  });

  const handleInit = useLatestCallback((e, editor: TinyMCE) => {
    editorRef.current = editor;
    loading.close();
    props.onInit?.(editor);
  });

  const handleDirty = useLatestCallback((e, editor: TinyMCE) => {
    // if the editor is not focused, we don't want to trigger onDirty
    if (setContentAfterInit.value && !editor.hasFocus()) {
      editor.undoManager.clear();
      editor.setDirty(false);
    } else {
      props.onDirty?.();
    }
    setContentAfterInit.close();
  });

  useUnmount(() => {
    pluginEntry.destory();
  });

  const handleDrop = useLatestCallback(async (e: DragEvent) => {
    e.stopPropagation();
    e.preventDefault();
    e.stopImmediatePropagation();

    const files = [...(e.dataTransfer?.files || [])];
    if (files.length === 0) {
      return;
    }

    const imageList = files.filter((file) => file.type.includes('image/'));
    const fileList = files.filter((file) => !file.type.includes('image/'));

    if (imageList.length > 0) {
      loading.open();
      const fileResList = await Promise.all(
        imageList.map((file) =>
          handleImageUpload({
            file,
          }),
        ),
      );

      (fileResList.filter((res) => !!res) as UploadFileInfo[]).forEach((res) => {
        editorRef.current?.insertContent(`<img src="${res.url}" style="max-width: 100%; height: auto;" />`);
      });
      loading.close();
    }

    if (fileList.length > 1) {
      alertApi.warn('You can only upload one file at a time.');
    } else if (fileList.length === 1) {
      props.handleUploadAttachment?.(fileList[0]);
    }
  });

  return (
    <Loading loading={loading.value}>
      <StyledRichTextEditor className="moego-rich-editor" hasError={props.hasError}>
        <Editor
          init={initOptions()}
          value={content}
          onEditorChange={handleContentChange}
          apiKey={RICHTEXT_API_KEY}
          plugins={plugins}
          onInit={handleInit}
          onBlur={() => props.onBlur?.(content)}
          onDirty={handleDirty}
          onFocus={props.onFocus}
          onDrop={handleDrop}
        />
      </StyledRichTextEditor>
      <Condition if={!!props.hasError}>
        <span className="!moe-text-[#D0021B] !moe-text-[12px] !moe-mt-[4px]">The email content can not be blank.</span>
      </Condition>
    </Loading>
  );
});
