import SvgIconEditorAttachmentSvg from '../../../assets/svg/icon-editor-attachment.svg';
import { type RichEditorInitHandler, type RichEditorPlugin, RichEditorPluginInfo } from './types';
import { createButtonTooltip, isDefinedInToolbar, svgTransformer } from './utils';

const PluginInfo = RichEditorPluginInfo.mapLabels[RichEditorPluginInfo.Attachment];

export const AttachmentInitPlugin: RichEditorInitHandler = (editor, options, getApi) => {
  isDefinedInToolbar(options.toolbar, PluginInfo.name, () => {
    editor.ui.registry.addIcon(PluginInfo.name, svgTransformer(SvgIconEditorAttachmentSvg));
    editor.ui.registry.addButton(PluginInfo.name, {
      tooltip: PluginInfo.tooltip,
      icon: PluginInfo.name,
      onAction: () => {
        getApi().handleUploadAttachment();
      },
      onSetup() {
        createButtonTooltip(editor, PluginInfo.tooltip);
        return () => {};
      },
    });
  });
};

export const AttachmentPlugin: RichEditorPlugin = {
  init: AttachmentInitPlugin,
};
