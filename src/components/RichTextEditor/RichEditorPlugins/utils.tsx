import React from 'react';
import ReactDOM from 'react-dom';
import { type Editor } from 'tinymce';
import { Tooltip } from '../../Popup/Tooltip';
import { EDITOR_CLASS_WRAPPER, EDITOR_CLASS_WRAPPER_INNER, HtmlPlaceholder, IS_DEV } from '../const';
import { type EditorComponentApi, type EditorInitOptions, type EditorMenuButton } from './types';

export const isDefinedInToolbar = (
  toolbar: EditorInitOptions['toolbar'],
  pluginName: string,
  callback?: () => void,
): boolean => {
  let result = false;
  if (typeof toolbar === 'string') {
    result = toolbar.includes(pluginName);
  } else if (!Array.isArray(toolbar)) {
    return result;
  } else {
    result = toolbar.some((item) => {
      if (typeof item === 'string') {
        return item.includes(pluginName);
      } else if (item.items) {
        return item.items?.includes(pluginName);
      }
      return false;
    });
  }
  if (result && callback) {
    callback();
  }
  return result;
};

export const isDefinedInPlugins = (
  plugins: EditorInitOptions['plugins'],
  pluginName: string,
  callback?: () => void,
): boolean => {
  let result = false;
  if (typeof plugins === 'string') {
    result = plugins.includes(pluginName);
  } else if (Array.isArray(plugins)) {
    result = plugins.some((plugin) => plugin.includes(pluginName));
  }
  if (result && callback) {
    callback();
  }
  return result;
};

export const svgTransformer = (svg: string) => {
  svg = svg.replace('<svg ', '<svg height="24" width="24" style="fill: none;margin-top: 2px;"');
  return svg;
};

export const createTooltips = (el: Element, tips: JSX.Element | JSX.Element[] | string) => {
  if (el) {
    const svgElement = el.querySelector('svg');
    if (svgElement) {
      ReactDOM.render(
        <Tooltip
          overlay={tips}
          trigger="hover"
          placement="top"
          align={{
            offset: [-12, -12],
          }}
        >
          {ReactDOM.createPortal(<span dangerouslySetInnerHTML={{ __html: svgElement.outerHTML }}></span>, el)}
        </Tooltip>,
        el,
      );
    }
  }
  return () => {
    if (el) {
      ReactDOM.unmountComponentAtNode(el);
    }
  };
};

const getParentNode = (editor: Editor, node: HTMLElement | null, parentName: string): HTMLElement | null => {
  const parent = editor.dom.getParent<HTMLElement>(node, parentName);
  if (parent?.nodeName === parentName.toUpperCase()) {
    return parent;
  }
  return null;
};

export const getCurrentNode = <T extends HTMLElement>(
  editor: Editor,
  nodeName: string,
  parentNodeName: string | string[] = [],
) => {
  const selection = editor.selection;
  const node = selection.getNode();
  const parentNodeList = Array.isArray(parentNodeName) ? parentNodeName : [parentNodeName];
  for (const name of parentNodeList) {
    const res = getParentNode(editor, node, name);
    if (res) {
      return res as T;
    }
  }
  if (node?.nodeName === nodeName.toUpperCase()) {
    return node as T;
  }
  return null;
};

export const getNodes = <T extends HTMLElement>(editor: Editor, nodeSelector: string) => {
  const body = editor.getBody();
  return [...body.querySelectorAll<T>(nodeSelector)];
};

export const emailDefinedStyle = `
<link href="
https://cdn.jsdelivr.net/npm/@fontsource/nunito@4.5.12/latin.min.css
" rel="stylesheet">
<style type="text/css">
  body {
    font-family: Helvetica, Arial, 'PingFang SC', 'Microsoft YaHei';
  }
  p, h1, h2, h3, h4, h5, h6 {
    margin: 0;
    padding: 0;
  }
</style>

`;

// Actually the content should wrap by backend. Export plugin will use this function to wrap content
export const wrapBody = (content: string) => {
  if (content.includes(EDITOR_CLASS_WRAPPER)) {
    return content;
  }
  return `
${emailDefinedStyle}
<div class="${EDITOR_CLASS_WRAPPER}" style="width: 100%; padding: 40px 49px 40px;background: #F7F8FA; box-sizing: border-box;min-height: 100vh;">
  <div style="width: 700px;background: #fff; margin: 0 auto; padding: 32px;box-sizing: border-box;color: #333;">
    <div class="${EDITOR_CLASS_WRAPPER_INNER}">
      ${content}
    </div>
  </div>
</div>
`;
};

interface ParseOptions {
  /**
   * 删除固定700px的宽度
   * B端浏览用户的reply email，防止横向滚动条
   * */
  removeFixedWidth?: boolean;
  /** 删除第三方的样式 */
  removeStyleTag?: boolean;
  adjustImgMaxWidth?: boolean;
}

/**
 * some email client do not support custom css, so we need to add inline style for content
 */
export const addInlineStyleForContent = (content: string, options: ParseOptions = {}) => {
  const { removeFixedWidth, removeStyleTag, adjustImgMaxWidth } = options;
  try {
    const parser = new DOMParser();
    const dom = parser.parseFromString(content, 'text/html');
    const marginTags = ['p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6'];
    const fontTags = ['span', 'div', 'a'];
    [...marginTags, ...fontTags].forEach((tag) => {
      const elements = dom.querySelectorAll<HTMLElement>(tag);
      for (let i = 0; i < elements.length; i++) {
        if (marginTags.includes(tag)) {
          elements[i].style.margin = '0';
        }
        elements[i].style.fontFamily = `Helvetica, Arial, 'PingFang SC', 'Microsoft YaHei'`;
      }
    });
    if (removeFixedWidth) {
      dom.querySelectorAll('div').forEach((div) => {
        if (div.style.width === '700px') {
          div.style.width = 'auto';
        }
      });
    }
    dom.querySelectorAll('img').forEach((img) => {
      if (adjustImgMaxWidth) {
        img.style.maxWidth = '100%';
      }
      if (img.width) {
        img.style.width = `${img.width}px`;
      }
      if (img.height) {
        img.style.height = `${img.height}px`;
      }
    });
    if (removeStyleTag) {
      dom.querySelectorAll('style').forEach((style) => style.remove());
    }
    const body = dom.querySelector('body');
    return body?.innerHTML || content;
  } catch (e) {
    console.error(e);
    return content;
  }
};

export const changeTemplateLogoWithBusinessSetting = (content: string, logoSrc: string): string => {
  if (!logoSrc) {
    return content;
  }
  try {
    const parser = new DOMParser();
    const dom = parser.parseFromString(content, 'text/html');
    // this logo url is specific from template
    const image = dom.querySelector<HTMLImageElement>(
      `img[src='https://moegonew.s3-us-west-2.amazonaws.com/Public/Uploads/1681148067a171c72a33db4b60bf5f7b509e158a60.png?name=Logo.png']`,
    );
    if (image) {
      const height = image.getAttribute('height');
      // by yisheng: logo should be circle
      image.setAttribute('src', logoSrc);
      image.setAttribute('width', height || '80');
      image.style.objectFit = 'cover';
      image.style.borderRadius = '50%';
      // in QQ Mail client, must be set display to inline-block & text-align center to force image center
      if (image.parentElement) {
        image.style.display = 'inline-block';
        image.parentElement.style.textAlign = 'center';
      }
      const body = dom.querySelector('body');
      return body?.innerHTML || content;
    }
    return content;
  } catch (e) {
    console.error(e);
    return content;
  }
};

export const variablePlaceholderList = [
  {
    label: 'Store name',
    value: '{storeName}',
  },
  {
    label: 'Customer name',
    value: '{customerName}',
  },
];

export const variableLinkList = (api: EditorComponentApi) => {
  const isBDEnabled = api.getBDEnabled();
  const linkList: EditorMenuButton[] = [
    {
      label: 'Upcoming appointment link',
      value: '{upcomingLink}',
    },
    {
      label: 'Parent portal invite link',
      value: '{petParentPortalLink}',
    },
    {
      label: 'Online booking link',
      value: '{onlineBookingLink}',
    },
  ].filter((item) => {
    if (item.value === '{petParentPortalLink}') {
      return !isBDEnabled;
    }
    return true;
  });
  const agreementList = api.getAgreementList();
  const membershipList = api.getMembershipList();

  if (agreementList.length) {
    linkList.push(
      {
        type: 'separator',
        label: 'Digital Agreement',
        value: '',
      },
      ...agreementList,
    );
  }
  if (membershipList.length) {
    linkList.push(
      {
        type: 'separator',
        label: 'Membership',
        value: '',
      },
      ...membershipList,
    );
  }
  return linkList;
};

export const getDynamicLinkList = (api: EditorComponentApi) => {
  const linkList: EditorMenuButton[] = [
    {
      label: 'Empty',
      value: '',
    },
    ...(IS_DEV()
      ? [
          {
            label: 'App Download Link',
            value: HtmlPlaceholder.AppDownloadLink,
          },
        ]
      : []),
  ];

  const onlineBookingURL = api.getOnlineBookingURL();

  if (onlineBookingURL) {
    linkList.push(...onlineBookingURL);
  }

  const intakeFormList = api.getIntakeFormList();

  if (intakeFormList) {
    linkList.push(...intakeFormList);
  }

  return linkList;
};

export const createButtonTooltip = (editor: Editor, tooltip: string, callback?: (elm: HTMLElement) => void) => {
  editor.once('init', () => {
    let dispose = () => {};
    const elm = document.querySelector<HTMLElement>(`button[title="${tooltip}"]`);
    if (elm) {
      editor.dom.setAttrib(elm, 'title', '');
      dispose = createTooltips(elm, tooltip);
      callback?.(elm);
    }
    editor.once('remove', () => {
      dispose();
    });
  });
};

export const checkButtonInContentIsValid = (content: string | undefined) => {
  if (!content) {
    return true;
  }
  const parser = new DOMParser();
  const dom = parser.parseFromString(content, 'text/html');
  const button = dom.querySelectorAll<HTMLAnchorElement>('.moego-editor-button');
  if (button.length === 0) {
    // no button in content, therefore it is valid
    return true;
  }
  return [...button].every((item) => !!item.href);
};
