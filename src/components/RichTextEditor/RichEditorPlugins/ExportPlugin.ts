// this plugin just used in private page for designer to export template html file
import dayjs from 'dayjs';
import { type Ui } from 'tinymce';
import SvgIconGreenExportSvg from '../../../assets/svg/icon-green-export.svg';
import { DATE_FORMAT_EXCHANGE } from '../../../utils/DateTimeUtil';
import { EDITOR_CLASS_WRAPPER_INNER } from '../const';
import { type RichEditorInitHandler, type RichEditorPlugin, RichEditorPluginInfo } from './types';
import { createButtonTooltip, isDefinedInPlugins, isDefinedInToolbar, svgTransformer, wrapBody } from './utils';

const PluginInfo = RichEditorPluginInfo.mapLabels[RichEditorPluginInfo.ExportHTML];

const ExportInitPlugin: RichEditorInitHandler = (editor, options) => {
  isDefinedInToolbar(options.toolbar, PluginInfo.name, () => {
    editor.ui.registry.addIcon(PluginInfo.name, svgTransformer(SvgIconGreenExportSvg));
    editor.ui.registry.addMenuButton(PluginInfo.name, {
      tooltip: PluginInfo.tooltip,
      icon: PluginInfo.name,
      fetch: (callback) => {
        const items: Ui.Menu.NestedMenuItemContents[] = [
          {
            type: 'menuitem',
            text: 'Export HTML with preivew style',
            onAction() {
              editor.windowManager.confirm('Do you want to export the content as HTML file?', (state) => {
                if (state) {
                  const data = editor.getContent();
                  let html = data;
                  if (isDefinedInPlugins(options.plugins, 'bodywrapper')) {
                    html = wrapBody(html);
                  }
                  const fileName = `${dayjs().format(DATE_FORMAT_EXCHANGE)} preview-style export.html`;
                  const blob = new Blob([html], { type: 'text/html' });
                  const url = URL.createObjectURL(blob);
                  const link = document.createElement('a');
                  link.href = url;
                  link.download = fileName;
                  link.click();
                  URL.revokeObjectURL(url);
                }
              });
            },
          },
          {
            type: 'menuitem',
            text: 'Export HTML with origin content',
            onAction() {
              editor.windowManager.confirm('Do you want to export the content as HTML file?', (state) => {
                if (state) {
                  const content = editor.getContent();
                  const data = content.includes(EDITOR_CLASS_WRAPPER_INNER)
                    ? content
                    : `<div class="${EDITOR_CLASS_WRAPPER_INNER}">${editor.getContent()}</div>`;
                  const fileName = `${dayjs().format(DATE_FORMAT_EXCHANGE)} origin-content export.html`;
                  const blob = new Blob([data], { type: 'text/html' });
                  const url = URL.createObjectURL(blob);
                  const link = document.createElement('a');
                  link.href = url;
                  link.download = fileName;
                  link.click();
                  URL.revokeObjectURL(url);
                }
              });
            },
          },
        ];
        callback(items);
      },
      onSetup() {
        createButtonTooltip(editor, PluginInfo.tooltip);
        return () => {};
      },
    });
  });
};

export const ExportPlugin: RichEditorPlugin = {
  init: ExportInitPlugin,
};
