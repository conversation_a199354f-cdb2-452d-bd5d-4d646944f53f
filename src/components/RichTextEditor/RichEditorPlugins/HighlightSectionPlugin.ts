// implement a custom add-highlight-section plugin for tinymce

import { default as chroma } from 'chroma-js';
import { type Editor, type Ui } from 'tinymce';
import SvgIconDeleteNewSvg from '../../../assets/svg/icon-delete-new.svg';
import SvgIconEditorHighlightSectionSvg from '../../../assets/svg/icon-editor-highlight-section.svg';
import { EDITOR_COMMAND_OPEN_HIGHLIGHT_SECTION_DIALOG, EDITOR_COMMAND_REMOVE_HIGHLIGHT_SECTION } from '../const';
import { type RichEditorInitHandler, type RichEditorPlugin, RichEditorPluginInfo } from './types';
import { createButtonTooltip, getCurrentNode, isDefinedInToolbar, svgTransformer } from './utils';

interface HighlightSectionOptions {
  backgroundColor: string;
  color: string;
}

const PluginInfo = RichEditorPluginInfo.mapLabels[RichEditorPluginInfo.AddHighlightSection];

const highlightSectionTemplate = (options: HighlightSectionOptions) =>
  `
<div
  class="moego-editor-paragraph"
  style="
    padding: 32px;
    background-color: ${options.backgroundColor};
    color: ${options.color};
    display: block;
    border-radius: 32px;
    text-decoration: none;
    font-size: 16px;
    line-height: 20px;
    margin: 0;
  "
>
  <p> </p>
</div>
<br />
`;

const showDialog = (editor: Editor) => {
  const res = buildDialogOptions(editor);
  const dialogApi = editor.windowManager.open(res.dialogOptions);
  dialogApi.setData(res.defaultData);
};

const removeHighlightSection = (editor: Editor) => {
  const currentBlock = getCurrentNode(editor, 'p', ['div', 'section']);
  if (currentBlock && currentBlock.className === 'moego-editor-paragraph') {
    editor.dom.remove(currentBlock);
  }
};

export const HighlightSectionInitPlugin: RichEditorInitHandler = (editor, options) => {
  isDefinedInToolbar(options.toolbar, PluginInfo.name, () => {
    editor.ui.registry.addIcon(PluginInfo.name, svgTransformer(SvgIconEditorHighlightSectionSvg));
    editor.ui.registry.addIcon(`${PluginInfo.name}remove`, svgTransformer(SvgIconDeleteNewSvg));

    editor.ui.registry.addButton(PluginInfo.name, {
      tooltip: PluginInfo.tooltip,
      icon: PluginInfo.name,
      onAction: () => {
        showDialog(editor);
      },
      onSetup() {
        createButtonTooltip(editor, PluginInfo.tooltip);
        return () => {};
      },
    });

    editor.ui.registry.addButton(`${PluginInfo.name}remove`, {
      icon: `${PluginInfo.name}remove`,
      onAction: () => {
        editor.execCommand(EDITOR_COMMAND_REMOVE_HIGHLIGHT_SECTION);
      },
    });

    editor.ui.registry.addButton(`${PluginInfo.name}edit`, {
      icon: 'moegoedit',
      onAction: () => {
        editor.execCommand(EDITOR_COMMAND_OPEN_HIGHLIGHT_SECTION_DIALOG);
      },
    });

    editor.ui.registry.addContextToolbar(PluginInfo.name, {
      predicate: (node) => {
        return (
          (node.nodeName.toLowerCase() === 'div' || node.nodeName.toLowerCase() === 'section') &&
          node.classList.contains('moego-editor-paragraph')
        );
      },
      items: `${PluginInfo.name}edit ${PluginInfo.name}remove`,
      position: 'node',
      scope: 'node',
    });

    editor.addCommand(EDITOR_COMMAND_OPEN_HIGHLIGHT_SECTION_DIALOG, () => {
      showDialog(editor);
    });
    editor.addCommand(EDITOR_COMMAND_REMOVE_HIGHLIGHT_SECTION, () => {
      removeHighlightSection(editor);
    });
  });
};

const buildDialogOptions = (editor: Editor) => {
  let hasHighlightSectionBlock = false;
  const options: Partial<HighlightSectionOptions> = {};
  const currentHighlightSection = getCurrentNode(editor, 'p', ['div', 'section']);
  // check if the current node is a paragraph block
  if (currentHighlightSection?.className === 'moego-editor-paragraph') {
    const backgroundColor = currentHighlightSection.style?.backgroundColor || '#FEF0E8';
    const color = currentHighlightSection.style?.color || '#333333';
    hasHighlightSectionBlock = true;
    try {
      options.backgroundColor = chroma(backgroundColor).hex().toUpperCase();
      options.color = chroma(color).hex().toUpperCase();
    } catch (e) {
      // the color maybe invalid
      // do nothing
    }
  }

  const dialogOptions: Ui.Dialog.DialogSpec<Ui.Dialog.DialogData> = {
    title: `${hasHighlightSectionBlock ? 'Update' : 'Add'} a highlight section`,
    body: {
      type: 'panel',
      items: [
        {
          type: 'colorinput',
          name: 'backgroundColor',
          label: 'Background color',
        },
        {
          type: 'colorinput',
          name: 'color',
          label: 'Text color',
        },
      ],
    },
    onSubmit: function (dialog) {
      const options = dialog.getData() as HighlightSectionOptions;
      if (hasHighlightSectionBlock && currentHighlightSection) {
        // update
        editor.dom.setStyle(currentHighlightSection, 'background-color', options.backgroundColor);
        editor.dom.setStyle(currentHighlightSection, 'color', options.color);
      } else {
        const html = highlightSectionTemplate(options);
        editor.insertContent(html);
      }
      dialog.close();
    },
    buttons: [
      {
        text: 'Close',
        type: 'cancel',
      },
      {
        text: hasHighlightSectionBlock ? 'Update' : 'Insert',
        type: 'submit',
        primary: true,
        enabled: true,
      },
    ],
  };
  const defaultData: HighlightSectionOptions = {
    backgroundColor: options.backgroundColor || '#FEF0E8',
    color: options.color || '#333333',
  };
  return {
    dialogOptions,
    defaultData,
  };
};

export const HighlightSectionPlugin: RichEditorPlugin = {
  init: HighlightSectionInitPlugin,
};
