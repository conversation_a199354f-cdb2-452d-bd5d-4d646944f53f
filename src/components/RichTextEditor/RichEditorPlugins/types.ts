import { type Editor, type RawEditorOptions } from 'tinymce';
import { createEnum } from '../../../store/utils/createEnum';

export interface EditorMenuButton {
  label: string;
  value: string;
  type?: 'separator';
}

export type EditorInitOptions = RawEditorOptions & {
  selector?: undefined;
  target?: undefined;
};

export type EditorComponentApi = {
  getIntakeFormList: () => EditorMenuButton[];
  getAgreementList: () => EditorMenuButton[];
  getOnlineBookingURL: () => EditorMenuButton[];
  getDefaultData: (id: string) => any | null;
  getMembershipList: () => EditorMenuButton[];
  getBDEnabled: () => boolean;
  handleLoading: (loading: boolean) => void;
  handleUploadAttachment: (file?: File) => Promise<void>;
};

/**
 * The RichEditorInitHandler is called when the editor is initialized.
 * This handler can get the editor instance.
 * But it can't change the init options of the editor.
 */
export type RichEditorInitHandler = (
  editor: Editor,
  options: EditorInitOptions,
  getApi: () => EditorComponentApi,
) => void;

/**
 * The RichEditorOptionsHandler is called before the editor is initialized.
 * This handler can get the editor options.
 * But it can't get the editor instance
 */
export type RichEditorOptionsHandler = (
  options: EditorInitOptions,
  getApi: () => EditorComponentApi,
) => EditorInitOptions;

export interface RichEditorPlugin {
  init?: RichEditorInitHandler;
  options?: RichEditorOptionsHandler;
}
export type RichEditorPluginEntryName =
  | 'button'
  | 'image'
  | 'toolbar'
  | 'exporthtml'
  | 'highlightsection'
  | 'variable'
  | 'importhtml'
  | 'attachment'
  | 'link';
export type RichEditorRegistryMap = {
  [key in RichEditorPluginEntryName]: RichEditorPlugin;
};

export const RichEditorPluginInfo = createEnum({
  AddButton: [
    1,
    {
      name: 'addbutton',
      tooltip: 'Add a button',
    },
  ],
  AddHighlightSection: [
    2,
    {
      name: 'addhighlightsection',
      tooltip: 'Add a highlight section',
    },
  ],
  AddVariable: [
    3,
    {
      name: 'addvariable',
      tooltip: 'Add a variable',
    },
  ],
  ExportHTML: [
    4,
    {
      name: 'exporthtml',
      tooltip: 'Export HTML',
    },
  ],
  ImportHTML: [
    5,
    {
      name: 'importhtml',
      tooltip: 'Import HTML',
    },
  ],
  Attachment: [
    6,
    {
      name: 'attachment',
      tooltip: 'Add an attachment',
    },
  ],
  TextFormat: [
    7,
    {
      name: 'textformat',
      tooltip: 'Text format',
    },
  ],
  Alignment: [
    8,
    {
      name: 'alignment',
      tooltip: 'Text align',
    },
  ],
});
