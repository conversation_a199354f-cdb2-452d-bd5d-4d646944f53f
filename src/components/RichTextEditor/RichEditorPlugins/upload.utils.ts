import { toastApi } from '../../Toast/Toast';
import { Upload } from '../../Upload/Upload';
import { type EditorComponentApi } from './types';

let uid = 0;

export const calculateAttachmentSize = (size: number) => {
  if (size < 1024) {
    return `${size}B`;
  }
  if (size < 1024 * 1024) {
    return `${(size / 1024).toFixed(2)}KB`;
  }
  return `${(size / 1024 / 1024).toFixed(2)}MB`;
};

export const setTinyMCENativeButtonDisabledStatus = () => {
  const uploadButton = document.querySelector('button[title="Source"].tox-browse-url');
  const saveButton = document.querySelector('button[title="Save"].tox-button');

  return {
    toggle: (disabled: boolean) => {
      if (disabled) {
        uploadButton?.setAttribute('disabled', 'true');
        saveButton?.setAttribute('disabled', 'true');
      } else {
        uploadButton?.removeAttribute('disabled');
        saveButton?.removeAttribute('disabled');
      }
    },
  };
};

export const handleFileUpload = async ({
  getApi,
  file,
  acceptFileType,
  maxSize,
}: {
  getApi?: () => EditorComponentApi;
  file?: File;
  acceptFileType?: string;
  maxSize?: number;
}) => {
  return new Promise<UploadFileInfo | false>(async (resolve) => {
    if (file) {
      const res = await uploadFile(file, getApi, maxSize);
      resolve(res);
      return;
    } else {
      const input = document.createElement('input');
      input.setAttribute('type', 'file');
      if (acceptFileType) {
        input.setAttribute('accept', acceptFileType);
      }

      const { toggle } = setTinyMCENativeButtonDisabledStatus();

      input.addEventListener('change', async (e) => {
        const file = (e.target as HTMLInputElement).files?.[0];
        if (file) {
          toggle(true);
          const res = await uploadFile(file, getApi, maxSize);
          resolve(res);
          toggle(false);
        } else {
          resolve(false);
        }
      });

      input.addEventListener('cancel', () => {
        resolve(false);
      });

      input.click();
    }
  });
};

export const handleImageUpload = async ({ getApi, file }: { getApi?: () => EditorComponentApi; file?: File }) => {
  return await handleFileUpload({
    getApi,
    file,
    acceptFileType: 'image/*',
  });
};

export interface UploadFileInfo {
  url: string;
  name: string;
  type: string;
  size: number;
}

const uploadFile = (file: File, getApi?: () => EditorComponentApi, maxFileSize?: number) => {
  return new Promise<UploadFileInfo | false>((resolve) => {
    if (maxFileSize && file.size > maxFileSize) {
      return toastApi.error(
        `File size is too large, please upload a file smaller than ${calculateAttachmentSize(maxFileSize)}`,
      );
    }
    const api = getApi?.();
    api?.handleLoading(true);
    Upload.defaultUploadHandler(
      uid++,
      file,
      () => {},
      (url) => {
        resolve({
          url,
          name: file.name,
          type: file.type,
          size: file.size,
        });
        api?.handleLoading(false);
      },
      (reason) => {
        console.error(reason);
        toastApi.error('Upload failed please try again later');
        api?.handleLoading(false);
        resolve(false);
      },
    );
  });
};
