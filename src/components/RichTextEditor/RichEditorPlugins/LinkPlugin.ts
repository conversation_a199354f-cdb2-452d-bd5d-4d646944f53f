import { EDITOR_COMMAND_OPEN_NATIVE_LINK_DIALOG } from '../const';
import { type RichEditorInitHandler, type RichEditorPlugin } from './types';
import { getDynamicLinkList } from './utils';

interface CommandOptions {
  readonly dialog?: boolean;
}

type originLinkHandler = (...args: any[]) => void | undefined;

export const LinkInitOptionsPlugin: RichEditorInitHandler = (editor, options, getApi) => {
  editor.once('init', () => {
    // @ts-expect-error -> private API
    const originMCELinkHandler = editor.editorCommands.commands.exec.mcelink as originLinkHandler;
    editor.addCommand(EDITOR_COMMAND_OPEN_NATIVE_LINK_DIALOG, (ui, value?: CommandOptions) => {
      const originLinkList = editor.options.get('link_list');
      if (!originLinkList) {
        const api = getApi();
        const linkList = getDynamicLinkList(api).filter((item) => item.label !== 'Empty');
        editor.options.set(
          'link_list',
          linkList.map((item) => {
            return {
              title: item.label,
              value: item.value,
            };
          }),
        );
      }
      originMCELinkHandler?.(ui, value);
    });
  });
};

export const LinkPlugin: RichEditorPlugin = {
  init: LinkInitOptionsPlugin,
};
