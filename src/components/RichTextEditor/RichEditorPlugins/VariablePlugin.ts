/* eslint-disable sonarjs/no-nested-functions */
import { type Ui } from 'tinymce';
import SvgIconEditorVariableSvg from '../../../assets/svg/icon-editor-variable.svg';
import { type RichEditorInitHandler, type RichEditorPlugin, RichEditorPluginInfo } from './types';
import {
  createButtonTooltip,
  isDefinedInToolbar,
  svgTransformer,
  variableLinkList,
  variablePlaceholderList,
} from './utils';

const PluginInfo = RichEditorPluginInfo.mapLabels[RichEditorPluginInfo.AddVariable];

export const VariableInitPlugin: RichEditorInitHandler = (editor, options, getApi) => {
  isDefinedInToolbar(options.toolbar, PluginInfo.name, () => {
    editor.ui.registry.addIcon(PluginInfo.name, svgTransformer(SvgIconEditorVariableSvg));
    editor.ui.registry.addMenuButton(PluginInfo.name, {
      tooltip: PluginInfo.tooltip,
      icon: PluginInfo.name,
      onSetup() {
        createButtonTooltip(editor, PluginInfo.tooltip, (elm) => {
          elm.style.width = '34px';
        });
        return () => {};
      },
      fetch: async (callback) => {
        const api = getApi();
        const items: Ui.Menu.NestedMenuItemContents[] = [
          {
            type: 'separator',
            text: 'Placeholder',
          },
          ...variablePlaceholderList.map((item) => {
            return {
              type: 'menuitem',
              text: item.label,
              onAction: () => {
                editor.insertContent(`<span class="moego-buildin-variable">${item.value}</span>`);
              },
            } as const;
          }),
          {
            type: 'separator',
            text: 'Link',
          },
          ...variableLinkList(api).map((item) => {
            return {
              type: item.type || 'menuitem',
              text: item.label,
              onAction: () => {
                editor.insertContent(`<a href="${item.value}" target="_blank">${item.value}</span>`);
              },
            } as const;
          }),
        ];
        callback(items);
      },
    });
  });
};

export const VariablePlugin: RichEditorPlugin = {
  init: VariableInitPlugin,
};
