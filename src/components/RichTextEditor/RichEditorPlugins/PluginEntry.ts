import { type Editor } from 'tinymce';
import { AttachmentPlugin } from './AttachmentPlugin';
import { ButtonPlugin } from './ButtonPlugin';
import { ExportPlugin } from './ExportPlugin';
import { HighlightSectionPlugin } from './HighlightSectionPlugin';
import { ImagePlugin } from './ImagePlugin';
import { ImportPlugin } from './ImportPlugin';
import { LinkPlugin } from './LinkPlugin';
import { ToolbarPlugin } from './ToolbarPlugin';
import { VariablePlugin } from './VariablePlugin';
import { type EditorComponentApi, type EditorInitOptions, type RichEditorRegistryMap } from './types';

const PluginRegistry: RichEditorRegistryMap = {
  button: ButtonPlugin,
  image: ImagePlugin,
  toolbar: ToolbarPlugin,
  exporthtml: ExportPlugin,
  highlightsection: HighlightSectionPlugin,
  variable: VariablePlugin,
  importhtml: ImportPlugin,
  attachment: AttachmentPlugin,
  link: LinkPlugin,
};

const handleCommonOptions = (options: EditorInitOptions) => {
  const contentStyle = `
    body {
      margin: 32px;
      font-family: Helvetica, Arial, 'PingFang SC', 'Microsoft YaHei';
    }
    p {
      margin: 0;
    }
    a.moego-editor-button[data-mce-selected] {
      outline: 3px solid #b4d7ff;
    }
    .moego-editor-button {
      user-select: auto !important;
    }
    .moego-editor-paragraph {
      user-select: auto !important;
    }
  `;
  options.content_style = `${options.content_style || ''} ${contentStyle}`;
  options.color_map = [
    '#EAFAEE',
    'Green-light',
    '#FFF7E8',
    'Yellow-light',
    '#FEF0E8',
    'Orange-light',
    '#FAE6E8',
    'Red-light',
    '#E9E8FF',
    'Purple-light',
    '#EBF3FE',
    'Blue-light',

    '#29CD57',
    'Green-regular',
    '#FAAD14',
    'Yellow-regular',
    '#F96B18',
    'Orange-regular',
    '#D0021B',
    'Red-regular',
    '#867CFF',
    'Purple-regular',
    '#3985F5',
    'Blue-regular',

    '#1F9941',
    'Green-dark',
    '#C78A10',
    'Yellow-dark',
    '#DE5A21',
    'Orange-dark',
    '#9E0215',
    'Red-dark',
    '#7269E0',
    'Purple-dark',
    '#2D69C2',
    'Blue-dark',

    '#FFFFFF',
    'White',
    '#E5E6EC',
    'Grey-light',
    '#CCCCCC',
    'Grey-medium',
    '#999999',
    'Gray-regular',
    '#666666',
    'Gray-dark',
    '#333333',
    'Black',

    '#000000',
    'Black-dark',
    '#FFFFFF',
    'White',
  ];
};

export class PluginEntry {
  private hasInited = false;
  private api: EditorComponentApi | null = null;

  getApi = (): EditorComponentApi => {
    return this.api!;
  };

  resolveInitOptions = (options: EditorInitOptions, api: EditorComponentApi): EditorInitOptions => {
    this.api = api;
    if (this.hasInited) {
      return options;
    }
    this.hasInited = true;
    // init options without editorApi
    Object.values(PluginRegistry).forEach((plugin) => {
      plugin.options?.(options, this.getApi);
    });
    handleCommonOptions(options);
    options.setup = (editor: Editor) => {
      // init plugins with editorApi
      // notice: the options can't be change in this time
      Object.values(PluginRegistry).forEach((plugin) => {
        plugin.init?.(editor, options, this.getApi);
      });
    };
    return options;
  };

  destory() {
    this.api = null;
    this.hasInited = false;
  }
}
