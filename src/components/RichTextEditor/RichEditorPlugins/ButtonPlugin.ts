// implement a custom add-button plugin for tinymce

import { default as chroma } from 'chroma-js';
import { type Editor, type Ui } from 'tinymce';
import SvgIconDeleteNewSvg from '../../../assets/svg/icon-delete-new.svg';
import SvgIconEditorAddButtonSvg from '../../../assets/svg/icon-editor-add-button.svg';
import { alertApi } from '../../Alert/AlertApi';
import {
  EDITOR_COMMAND_INVALID_BUTTON_DIALOG,
  EDITOR_COMMAND_OPEN_BUTTON_DIALOG,
  EDITOR_COMMAND_REMOVE_BUTTON,
} from '../const';
import {
  type EditorComponentApi,
  type RichEditorInitHandler,
  type RichEditorPlugin,
  RichEditorPluginInfo,
} from './types';
import {
  createButtonTooltip,
  getCurrentNode,
  getDynamicLinkList,
  getNodes,
  isDefinedInToolbar,
  svgTransformer,
} from './utils';

interface ButtonOptions {
  text: string;
  href: string;
  backgroundColor: string;
  color: string;
  id: string;
}

const PluginInfo = RichEditorPluginInfo.mapLabels[RichEditorPluginInfo.AddButton];
const templateButton = (options: ButtonOptions) => `
<a
  class="moego-editor-button"
  style="
    padding: 10px 25px;
    background-color: ${options.backgroundColor};
    color: ${options.color};
    cursor: pointer;
    display: inline-block;
    vertical-align: middle;
    border-radius: 8px;
    text-decoration: none;
    font-size: 16px;
  "
  href="${options.href}"
  target="_blank"
  id="${options.id}"
>
${options.text}
</a>
`;

const showDialog = (editor: Editor, getApi: () => EditorComponentApi, openInvalidButton = false) => {
  const res = buildDialogOptions(editor, getApi, openInvalidButton);
  const dialogApi = editor.windowManager.open(res.dialogOptions);
  if (openInvalidButton) {
    dialogApi.focus('href');
  }
  dialogApi.setData(res.defaultData);
};

const removeButton = (editor: Editor) => {
  const currentButton = getCurrentNode(editor, 'a');
  if (currentButton && currentButton.className === 'moego-editor-button') {
    editor.dom.remove(currentButton);
  }
};

// extend design, not use now
let uid = 0;
export const ButtonInitPlugin: RichEditorInitHandler = (editor, options, getApi) => {
  isDefinedInToolbar(options.toolbar, PluginInfo.name, () => {
    editor.ui.registry.addIcon(PluginInfo.name, svgTransformer(SvgIconEditorAddButtonSvg));
    editor.ui.registry.addIcon(`${PluginInfo.name}remove`, svgTransformer(SvgIconDeleteNewSvg));

    editor.ui.registry.addButton(`${PluginInfo.name}edit`, {
      icon: 'moegoedit',
      onAction: () => editor.execCommand(EDITOR_COMMAND_OPEN_BUTTON_DIALOG),
    });
    editor.ui.registry.addButton(`${PluginInfo.name}remove`, {
      icon: `${PluginInfo.name}remove`,
      onAction: () => {
        editor.execCommand(EDITOR_COMMAND_REMOVE_BUTTON);
      },
    });

    editor.addCommand(EDITOR_COMMAND_OPEN_BUTTON_DIALOG, () => {
      showDialog(editor, getApi);
    });
    editor.addCommand(EDITOR_COMMAND_INVALID_BUTTON_DIALOG, () => {
      showDialog(editor, getApi, true);
    });
    editor.addCommand(EDITOR_COMMAND_REMOVE_BUTTON, () => {
      removeButton(editor);
    });

    editor.ui.registry.addContextToolbar(`${PluginInfo.name}align`, {
      predicate: (node) => node.nodeName.toLowerCase() === 'a' && node.classList.contains('moego-editor-button'),
      items: `alignleft aligncenter alignright | ${PluginInfo.name}edit ${PluginInfo.name}remove`,
      position: 'node',
      scope: 'node',
    });
    editor.ui.registry.addButton(PluginInfo.name, {
      tooltip: PluginInfo.tooltip,
      icon: PluginInfo.name,
      onAction: () => {
        showDialog(editor, getApi);
      },
      onSetup() {
        createButtonTooltip(editor, PluginInfo.tooltip);
        return () => {};
      },
    });
  });
};

const getInvalidButton = (editor: Editor) => {
  const buttons = getNodes<HTMLAnchorElement>(editor, '.moego-editor-button');
  const invalidButton = buttons.find((button) => !button.href);
  return invalidButton;
};

const buildDialogOptions = (editor: Editor, getApi: () => EditorComponentApi, openInvalidButton = false) => {
  const api = getApi();
  let hasButtonBlock = false;
  const options: Partial<ButtonOptions> = {};
  const currentButton = openInvalidButton ? getInvalidButton(editor) : getCurrentNode(editor, 'a');
  // check if the current node is a button block
  if (currentButton && currentButton.className === 'moego-editor-button') {
    const backgroundColor = currentButton.style?.backgroundColor || '#F96B18';
    const color = currentButton.style?.color || '#FFFFFF';
    const href = currentButton.getAttribute('href') || '';
    const text = currentButton.innerText || '';
    const id = currentButton.id || '';
    hasButtonBlock = true;
    try {
      options.backgroundColor = chroma(backgroundColor).hex().toUpperCase();
      options.color = chroma(color).hex().toUpperCase();
      options.href = href;
      options.text = text;
      options.id = id;
    } catch (e) {
      // the color maybe invalid
      // do nothing
    }
  }

  const linkList = getDynamicLinkList(api);

  const id = (options.id = options.id || `button-${uid++}`);

  const dialogOptions: Ui.Dialog.DialogSpec<Ui.Dialog.DialogData> = {
    title: `${hasButtonBlock ? 'Update' : 'Add'} custom button`,
    body: {
      type: 'panel',
      items: [
        {
          type: 'input',
          name: 'text',
          label: 'Button text',
        },
        {
          type: 'input',
          name: 'href',
          placeholder: 'https://example.com',
          label: 'Button URL',
        },
        {
          type: 'listbox',
          name: 'link',
          label: 'Link list',
          items: linkList.map((item) => {
            return {
              text: item.label,
              value: item.value,
            };
          }),
        },
        {
          type: 'colorinput',
          name: 'backgroundColor',
          label: 'Background color',
        },
        {
          type: 'colorinput',
          name: 'color',
          label: 'Text color',
        },
      ],
    },
    onSubmit: function (dialog) {
      const _options = dialog.getData() as ButtonOptions;
      const options = {
        ..._options,
        id,
      };
      if (!options.href) {
        alertApi.warn('Button destination URL cannot be blank.');
        return;
      }
      if (!/^(http|https):\/\//.test(options.href)) {
        if (!(options.href.startsWith('{') && options.href.endsWith('}'))) {
          alertApi.warn('Button destination URL should start with http:// or https://');
          return;
        }
      }
      if (hasButtonBlock && currentButton) {
        editor.dom.setStyle(currentButton, 'background-color', options.backgroundColor);
        editor.dom.setStyle(currentButton, 'color', options.color);
        editor.dom.setAttrib(currentButton, 'href', options.href);
        editor.dom.setAttrib(currentButton, 'id', id);
        editor.dom.setHTML(currentButton, options.text);
      } else {
        const html = templateButton(options);
        editor.insertContent(html);
      }
      dialog.close();
    },
    onChange(api, details) {
      if (details.name === 'link') {
        const { link } = api.getData() || {};
        if (link) {
          api.setData({
            href: link,
          });
        }
      }
    },
    buttons: [
      {
        text: 'Close',
        type: 'cancel',
      },
      {
        text: hasButtonBlock ? 'Update' : 'Insert',
        type: 'submit',
        primary: true,
        enabled: true,
      },
    ],
  };

  const defaultData: Partial<ButtonOptions> = {
    color: options.color || '#FFFFFF',
    backgroundColor: options.backgroundColor || '#F96B18',
    text: options.text || '',
    href: options.href || '',
    id,
  };

  return {
    dialogOptions,
    defaultData,
  };
};

export const ButtonPlugin: RichEditorPlugin = {
  init: ButtonInitPlugin,
};
