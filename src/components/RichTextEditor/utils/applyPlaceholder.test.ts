import { applyPlaceholder } from './applyPlaceholder';

describe('applyPlaceholder', () => {
  it('returns original content when no placeholders are applied', () => {
    const content = 'Hello, world!';
    const result = applyPlaceholder(content).done();
    expect(result).toBe(content);
  });

  it('replaces a single placeholder correctly', () => {
    const content = 'Hello, {name}!';
    const name = '<PERSON>';
    const expected = 'Hello, <PERSON>!';
    const result = applyPlaceholder(content).apply('name', name).done();
    expect(result).toBe(expected);
  });

  it('replaces multiple placeholders correctly', () => {
    const content = '{greeting}, {name}!';
    const replacements = { greeting: 'Hello', name: '<PERSON>' };
    const expected = 'Hello, <PERSON>!';
    let result = applyPlaceholder(content);
    Object.entries(replacements).forEach(([key, value]) => {
      result = result.apply(key, value);
    });
    expect(result.done()).toBe(expected);
  });

  it('does not alter content for non-existent placeholders', () => {
    const content = 'Hello, world!';
    const result = applyPlaceholder(content).apply('name', 'John').done();
    expect(result).toBe(content);
  });

  it('replaces repeated placeholders correctly', () => {
    const content = '{word} {word} {word}!';
    const word = 'echo';
    const expected = 'echo echo echo!';
    const result = applyPlaceholder(content).apply('word', word).done();
    expect(result).toBe(expected);
  });

  it('handles edge cases for placeholders', () => {
    const content = '{start}{middle}{end}';
    const replacements = { start: 'A', middle: '-middle-', end: 'Z' };
    const expected = 'A-middle-Z';
    let result = applyPlaceholder(content);
    Object.entries(replacements).forEach(([key, value]) => {
      result = result.apply(key, value);
    });
    expect(result.done()).toBe(expected);
  });
});
