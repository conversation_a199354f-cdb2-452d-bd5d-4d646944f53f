export const EDITOR_COMMAND_INVALID_BUTTON_DIALOG = 'mceInvalidButtonDialog';
export const EDITOR_COMMAND_OPEN_BUTTON_DIALOG = 'mceOpenButtonDialog';
export const EDITOR_COMMAND_OPEN_HIGHLIGHT_SECTION_DIALOG = 'mceOpenHighlightSectionDialog';
// it is the tinymce native command
export const EDITOR_COMMAND_OPEN_NATIVE_LINK_DIALOG = 'mceLink';
export const EDITOR_COMMAND_REMOVE_BUTTON = 'mceRemoveButton';
export const EDITOR_COMMAND_REMOVE_HIGHLIGHT_SECTION = 'mceRemoveHighlightSection';

// css class
export const EDITOR_CLASS_WRAPPER = 'moe-editor-wrapper';
export const EDITOR_CLASS_WRAPPER_INNER = 'moe-editor-wrapper__inner';
export const EDITOR_CLASS_CONTAINER = 'moe-editor-container';

export const IS_DEV = () => window.location.href.includes('debugging');

export const HtmlPlaceholder = {
  AppDownloadLink: '{AppDownloadLink}',
};
