import React, { useState, useCallback, useRef, useEffect } from 'react';
import { createContext, useContextSelector } from 'use-context-selector';
import { unstable_LowPriority, unstable_runWithPriority } from 'scheduler';

interface CardVisibleContextType {
  visibleCards: Record<string, boolean> | undefined;
  setCardVisible: (cardId: string, visible: boolean) => void;
  toggleCardVisible: (cardId: string) => void;
  setVisibleCards: React.Dispatch<React.SetStateAction<Record<string, boolean> | undefined>>;
  hiddenResource: boolean;
  setHiddenResource: React.Dispatch<React.SetStateAction<boolean>>;
  observerRef: React.MutableRefObject<IntersectionObserver | null>;
}

const CardVisibleContext = createContext<CardVisibleContextType | undefined>(undefined);

export const CardVisibleProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [visibleCards, setVisibleCards] = useState<Record<string, boolean>>();

  const [hiddenResource, setHiddenResource] = useState<boolean>(false);
  const observerRef = useRef<IntersectionObserver | null>(null);

  useEffect(() => {
    let stop = false;
    observerRef.current = new IntersectionObserver((entries) => {
      if (stop) return;
      const visibleStore: Record<string, boolean> = {};
      entries.forEach((entry) => {
        const container = entry.target as HTMLElement;
        const cardId = container.dataset.cardId;
        if (!container || !cardId) return;
        visibleStore[cardId] = entry.isIntersecting;
      });
      unstable_runWithPriority(unstable_LowPriority, () => {
        setVisibleCards((d) => {
          if (!d) return visibleStore;
          return {
            ...d,
            ...visibleStore,
          };
        });
      });
    });
    return () => {
      stop = true;
      observerRef.current?.disconnect();
    };
  }, []);

  const setCardVisible = useCallback((cardId: string, visible: boolean) => {
    setVisibleCards((prev) => ({
      ...prev,
      [cardId]: visible,
    }));
  }, []);

  const toggleCardVisible = useCallback((cardId: string) => {
    setVisibleCards((prev) => ({
      ...prev,
      [cardId]: !prev?.[cardId],
    }));
  }, []);

  const contextValue = {
    visibleCards,
    setCardVisible,
    toggleCardVisible,
    setVisibleCards,
    hiddenResource,
    setHiddenResource,
    observerRef,
  };

  return <CardVisibleContext.Provider value={contextValue}>{children}</CardVisibleContext.Provider>;
};

export const useCardVisible = (cardId: string) => {
  const visible = useContextSelector(CardVisibleContext, (context) => {
    const { visibleCards } = context || {};
    if (!visibleCards) return false; // 默认需要为空

    const visible = visibleCards[cardId];
    return Boolean(visible); // 没有值默认为 ture，有值则返回 ture 或者 false
  });
  return visible;
};

export const useCardVisibleObserver = () => {
  const context = useContextSelector(CardVisibleContext, (context) => context?.observerRef);
  if (!context) {
    throw new Error('useCardVisibleObserver must be used within a CardVisibleProvider');
  }
  return context;
};

export const useCardVisibleController = () => {
  const context = useContextSelector(CardVisibleContext, (context) => context?.setVisibleCards);
  if (!context) {
    throw new Error('useCardVisibleController must be used within a CardVisibleProvider');
  }
  return context;
};

export const useHiddenResource = () => {
  const context = useContextSelector(CardVisibleContext, (context) => {
    if (!context) return undefined;
    return { hiddenResource: context?.hiddenResource, setHiddenResource: context?.setHiddenResource };
  });

  if (!context) {
    throw new Error('useHiddenResource must be used within a CardVisibleProvider');
  }

  return context;
};

export const useHiddenResourceController = () => {
  const context = useContextSelector(CardVisibleContext, (context) => {
    if (!context) return undefined;
    return context?.setHiddenResource;
  });

  if (!context) {
    throw new Error('useHiddenResource must be used within a CardVisibleProvider');
  }

  return context;
};
