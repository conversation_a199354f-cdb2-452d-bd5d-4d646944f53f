import styled, { css } from 'styled-components';
import { c_danger } from '../style/_variables';

export const DropdownList = styled.div`
  background-color: white;
  padding: 8px 0;
  border-radius: 4px;
  box-shadow: 0px 4px 16px rgba(0, 0, 0, 0.12);
  max-height: calc(100vh - 100px);
  overflow: auto;
`;

export const DropdownItem = styled.div<{
  active?: boolean;
  disabled?: boolean;
  danger?: boolean;
  small?: boolean;
  hoverColor?: string;
  hoverBackgroundColor?: string;
}>`
  display: flex;
  align-items: center;
  line-height: 18px;
  font-size: 14px;
  font-weight: 600;
  padding: 7px 20px;
  margin: 4px 0;
  cursor: pointer;
  color: #333;
  &:hover {
    background-color: #f2f3f7;
    color: #333;
  }
  ${(props) =>
    props.hoverColor &&
    css`
      &:hover {
        color: ${props.hoverColor};
      }
    `}
  ${(props) =>
    props.hoverBackgroundColor &&
    css`
      &:hover {
        background-color: ${props.hoverBackgroundColor};
      }
    `}
  ${(props) =>
    props.active &&
    css`
      color: var(--moe-color-text-brand);
      background-color: var(--moe-color-bg-brand-subtle);
      &:hover {
        color: var(--moe-color-text-brand);
        background-color: var(--moe-color-bg-brand-mild);
      }
    `}
    ${(props) =>
      props.danger &&
      css`
      color: ${c_danger};
    `}
  ${(props) =>
    props.disabled &&
    css`
      cursor: not-allowed;
      opacity: 0.3;
      &:hover {
        background-color: 'unset';
      }
    `}
`;

export const DropdownLabel = styled.div`
  font-size: 12px;
  font-weight: 500;
  line-height: 16px;
  padding: 12px 20px;
  color: rgb(153, 153, 153);
`;
