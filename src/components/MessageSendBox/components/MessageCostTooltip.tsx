import { Tooltip } from '@moego/ui';
import React, { memo, useRef } from 'react';

export const MessageCostTooltip = memo<{ smsRemainLength: number; smsSegmentLength: number }>(
  ({ smsRemainLength, smsSegmentLength }) => {
    const ref = useRef<HTMLDivElement>(null);

    return (
      <Tooltip
        backgroundTheme="light"
        content={
          <div className="moe-overflow-auto moe-whitespace-normal">
            {'Approx. '}
            <span className="count-highlight">{smsRemainLength}</span>
            {' characters remaining with '}
            <span className="count-highlight">{smsSegmentLength}</span>
            {` SMS used`}
          </div>
        }
        side="top"
        container={ref.current}
      >
        <div className="word-counter" ref={ref}>
          <span className="count-highlight">{smsSegmentLength}</span>
          {' SMS'}
        </div>
      </Tooltip>
    );
  },
);
