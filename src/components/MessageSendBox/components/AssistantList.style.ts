import styled from 'styled-components';

export const AssistantListStyle = styled.div`
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 8px;
  margin: 12px 16px 3px 16px;
  .preset-button {
    overflow: hidden;
    display: inline-block;
    text-wrap: nowrap;
    &.enter {
      max-width: 0;
    }
    &.enter-active {
      max-width: 400px;
      transition: max-width 300ms ease-in;
    }
    &.exit {
      max-width: 400px;
    }
    &.exit-active {
      max-width: 0;
      transition: max-width 300ms ease-in;
    }
  }
`;
