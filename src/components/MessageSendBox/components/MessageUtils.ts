import { curry, pipe } from 'lodash/fp';
import {
  AgreementLinkPrefix,
  CardOnFileLinkExp,
  IntakeFormLinkPrefix,
  MembershipLinkPrefix,
  OnlineBookingLinkExp,
  PetParentAppDownloadLinkExp,
  PetParentPortalLinkExp,
  UpcomingApptLinkExp,
} from '../../../utils/messageVariable';
import { or } from '../../../utils/utils';

/**
 * real length of string,by counting astral symbols and ignoring ansi escape codes.
 */
export const ALLOW_TEXT_LENGTH_TWOWAY = 160;
export const ALLOW_TEXT_LENGTH_REMINDER = 480;
export const TEXT_MAX_LENGTH = ALLOW_TEXT_LENGTH_TWOWAY * 9;
/** Auto Message, Auto Reminder, Auto Reply */
export const AUTO_MESSAGE_SMS_MAX_LENGTH = ALLOW_TEXT_LENGTH_REMINDER * 9;
export const AUTO_MESSAGE_EMAIL_MAX_LENGTH = 3800;
export const AUTO_MESSAGE_PPA_MAX_LENGTH = 1024;

export const CusterAndPetVariableList: { value: string; label: string }[] = [
  { value: '{customerName}', label: 'customerName' },
  { value: '{petName}', label: 'petName' },
];

/**
 * to match surrogate pairs
 */
export const regexSurrogatePairs = /[\uD800-\uDBFF][\uDC00-\uDFFF]/g;

/** real length of online booking variable */
const REAL_ONLINE_BOOKING_VARIABLE_WORDS = 57;

/** real length of agreement variable */
const REAL_AGREEMENT_VARIABLE_WORDS = 72;

/** real length of upcoming appt link variable */
const REAL_UPCOMING_APPOINTMENT_VARIABLE_WORDS = 71;

/** real length of intake form variable */
const REAL_INTAKE_FORM_VARIABLE_WORDS = 70;

/** real length of pet parent portal link variable */
const REAL_PET_PARENT_PORTAL_VARIABLE_WORDS = 41;

/** real length of pet parent app download link variable */
const REAL_PET_PARENT_APP_DOWNLOAD_VARIABLE_WORDS = 51;

/** real length of membership variable */
const REAL_MEMBERSHIP_VARIABLE_WORDS = 89;

/** real length of card on file variable */
const REAL_CARD_ON_FILE_VARIABLE_WORDS = 54;

/** agreement variable substitution prefix */
const agreementVariablePrefixes = [AgreementLinkPrefix];
const agreementMatchChainText = `\\{(${agreementVariablePrefixes.join('|')}):(\\d+):([^{}]+?)\\}`; // ~= /\{(agreementLink):(\d+):([^{}]+?)\}/g;

/** intake form variable substitution prefix */
const intakeFormVariablePrefixes = [IntakeFormLinkPrefix];
const intakeFormMatchChainText = `\\{(${intakeFormVariablePrefixes.join('|')}):(\\d+):([^{}]+?)\\}`; // ~= /\{(intakeFormLink):(\d+):([^{}]+?)\}/g;

/** membership variable substitution prefix */
const membershipVariablePrefixes = [MembershipLinkPrefix];
const membershipMatchChainText = `\\{(${membershipVariablePrefixes.join('|')}):(\\d+):([^{}]+?)\\}`; // ~= /\{(membershipLink):(\d+):([^{}]+?)\}/g;

const checkIfMatchRegExp = curry((reg: RegExp, content: string) => reg.test(content));

/** Replace every surrogate pair with a BMP symbol. */
const make2bmp = (input: string) => input.replace(regexSurrogatePairs, '_');

/** get string length */
const getLength = (input: string) => input.length;

/** convert the matched characters to {repeatNum} specified {bmpChar} character */
const makeMatchedExp2BmpWord = curry((matchExp: RegExp, repeatNum: number, bmpChar: string, input: string) =>
  input.replace(matchExp, bmpChar.repeat(repeatNum)),
);

/** convert agreement variable characters to 50 placeholder hyphens */
const makeOnlineBookingVariable2Hyphen = makeMatchedExp2BmpWord(
  new RegExp(`\\{${OnlineBookingLinkExp}\\}`, 'g'),
  REAL_ONLINE_BOOKING_VARIABLE_WORDS,
  '-',
);

/** convert agreement variable characters to 72 placeholder hyphens */
const makeAgreementVariable2Hyphen = makeMatchedExp2BmpWord(
  new RegExp(agreementMatchChainText, 'g'),
  REAL_AGREEMENT_VARIABLE_WORDS,
  '-',
);

/** convert upcoming appt to 71 placeholder hyphens */
const makeUpcomingAppointment2Hyphens = makeMatchedExp2BmpWord(
  new RegExp(`\\{${UpcomingApptLinkExp}\\}`, 'g'),
  REAL_UPCOMING_APPOINTMENT_VARIABLE_WORDS,
  '-',
);

/** convert agreement variable characters to 70 placeholder hyphens */
const makeIntakeFormVariable2Hyphen = makeMatchedExp2BmpWord(
  new RegExp(intakeFormMatchChainText, 'g'),
  REAL_INTAKE_FORM_VARIABLE_WORDS,
  '-',
);

/** convert pet parent portal to 41 placeholder hyphens */
const makePetParentPortal2Hyphens = makeMatchedExp2BmpWord(
  new RegExp(`\\{${PetParentPortalLinkExp}\\}`, 'g'),
  REAL_PET_PARENT_PORTAL_VARIABLE_WORDS,
  '-',
);

/** convert pet parent portal app download link to 51 placeholder hyphens */
const makePetParentAppDownloadLink2Hyphens = makeMatchedExp2BmpWord(
  new RegExp(`\\{${PetParentAppDownloadLinkExp}\\}`, 'g'),
  REAL_PET_PARENT_APP_DOWNLOAD_VARIABLE_WORDS,
  '-',
);

/** convert membership variable characters to 89 placeholder hyphens */
const makeMembershipVariable2Hyphen = makeMatchedExp2BmpWord(
  new RegExp(membershipMatchChainText, 'g'),
  REAL_MEMBERSHIP_VARIABLE_WORDS,
  '-',
);

/** convert agreement variable characters to 54 placeholder hyphens */
const makeCardOnFileVariable2Hyphen = makeMatchedExp2BmpWord(
  new RegExp(`\\{${CardOnFileLinkExp}\\}`, 'g'),
  REAL_CARD_ON_FILE_VARIABLE_WORDS,
  '-',
);

/**
 * Replace every surrogate pair and agreement variable to BMP symbols.
 * @param input
 * @returns number of symbols
 */
const countSymbols = pipe(
  make2bmp,
  makeOnlineBookingVariable2Hyphen,
  makeAgreementVariable2Hyphen,
  makeUpcomingAppointment2Hyphens,
  makeIntakeFormVariable2Hyphen,
  makePetParentPortal2Hyphens,
  makePetParentAppDownloadLink2Hyphens,
  makeMembershipVariable2Hyphen,
  makeCardOnFileVariable2Hyphen,
  getLength,
);

/** if match online booking variable */
export const checkIfMatchOnlineBookingVariable = checkIfMatchRegExp(new RegExp(`\\{${OnlineBookingLinkExp}\\}`));

/** if match agreement variable */
export const checkIfMatchAgreementVariable = checkIfMatchRegExp(new RegExp(agreementMatchChainText));

/** if match upcoming appointment variable */
export const checkIfMatchUpcomingAppointmentVariable = checkIfMatchRegExp(new RegExp(`\\{${UpcomingApptLinkExp}\\}`));

/** if match intake form variable */
export const checkIfMatchIntakeFormVariable = checkIfMatchRegExp(new RegExp(intakeFormMatchChainText));

/** if match pet parent portal variable */
export const checkIfMatchPetParentPortalVariable = checkIfMatchRegExp(new RegExp(`\\{${PetParentPortalLinkExp}\\}`));

/** if match pet parent app download link variable */
export const checkIfMatchPetParentAppDownloadLinkVariable = checkIfMatchRegExp(
  new RegExp(`\\{${PetParentAppDownloadLinkExp}\\}`),
);

/** if match membership variable */
export const checkIfMatchMembershipVariable = checkIfMatchRegExp(new RegExp(membershipMatchChainText));

/** if match card on file variable */
export const checkIfMatchCardOnFileVariable = checkIfMatchRegExp(new RegExp(`\\{${CardOnFileLinkExp}\\}`));

export const checkIfMatchCustomerAndPetVariable = checkIfMatchRegExp(
  new RegExp(CusterAndPetVariableList.map((i) => i.value).join('|')),
);

/** if any expression matched */
export const checkIfMatchAnyVariable = or(
  checkIfMatchOnlineBookingVariable,
  checkIfMatchAgreementVariable,
  checkIfMatchUpcomingAppointmentVariable,
  checkIfMatchIntakeFormVariable,
  checkIfMatchPetParentPortalVariable,
  checkIfMatchPetParentAppDownloadLinkVariable,
  checkIfMatchMembershipVariable,
  checkIfMatchCardOnFileVariable,
  checkIfMatchCustomerAndPetVariable,
);

export interface SmsSegments {
  smsLength: number;
  smsSegmentLength: number;
  remainLength: number;
}

export const getSmsSegments = (smsBody: string, SingleAllowLength: number): SmsSegments => {
  // 当前字数长度
  let smsLength = 0;
  // 当前短信总条数
  let smsSegmentLength = 0;
  // 当前短信剩余字数（超出后会再增加一条短信）
  let remainLength = 0;
  // 内容为空，计算
  if (smsBody.length == 0) {
    // 当前字数长度
    smsLength = 0;
    smsSegmentLength = 1;
    remainLength = SingleAllowLength;
  }
  smsLength = countSymbols(smsBody);
  // 判断是否单条消息
  if (smsLength <= SingleAllowLength) {
    smsSegmentLength = 1;
    remainLength = SingleAllowLength - smsLength;
  } else {
    // 多条消息处理
    const remainWords = smsLength % SingleAllowLength;
    smsSegmentLength = Math.ceil(smsLength / SingleAllowLength);
    remainLength = SingleAllowLength - remainWords;
  }
  return { smsLength, smsSegmentLength, remainLength };
};
