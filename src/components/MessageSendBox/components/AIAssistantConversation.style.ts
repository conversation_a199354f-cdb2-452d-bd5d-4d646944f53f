import styled from 'styled-components';

export const StyledAIConversation = styled.div`
  display: flex;
  width: 360px;
  height: 600px;
  background-color: #fff;
  flex-direction: column;
  box-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.08);

  .title {
    border-bottom: 1px solid rgba(233, 236, 239, 1);
    padding: 16px;
    font-size: 14px;
    font-weight: 700;
    line-height: 18px;
  }
  .qa-list-container {
    overflow-y: auto;
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 10px;
    background: rgba(247, 248, 250, 1);
    padding: 16px;
  }
  .input-container {
    border-top: 1px solid rgba(222, 225, 229, 1);
    padding: 16px;
    .input-wrapper {
      padding: 7px 8px;
    }
    .ant-input-affix-wrapper {
      border-radius: 8px;
      border-color: rgba(222, 225, 229, 1);
    }
    .ant-input {
      font-size: 12px;
      font-weight: 500;
      line-height: 16px;
    }
    .ant-input-disabled {
    }
  }
`;

export const QAContent = styled.div`
  border: 1px solid rgba(233, 236, 239, 1);
  padding: 16px;
  border-radius: 6px;
  background: white;

  .command-prompt {
    color: rgba(153, 153, 153, 1);
  }
  .answer {
    margin-top: 16px;
    margin-bottom: 16px;
    white-space: pre-line;
  }
  .actions {
    display: flex;
    gap: 12px;
  }
`;
