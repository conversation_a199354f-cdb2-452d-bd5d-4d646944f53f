import styled, { css } from 'styled-components';

const buttonCommonStyle = css`
  display: inline-flex;
  flex-direction: row;
  align-items: center;
  font-size: 14px;
  font-weight: 600;
  line-height: 18px;
  cursor: pointer;
  border: none;
`;

/**
 * 需要支持 gradient 和 size 定制
 */
export const StyledBorderGradientButton = styled.button<{ disabled?: boolean }>`
  ${buttonCommonStyle}
  padding: 5px 10px;
  border: 2px solid #0000;
  border-radius: 50vh;
  background: linear-gradient(white, white) padding-box, linear-gradient(150deg, #a5e6ff, #ddcdff) border-box;
  ${({ disabled }) =>
    disabled &&
    css`
      opacity: 0.7;
      cursor: not-allowed;
    `}
`;

export const StyledAIButton = styled.button<{ disabled?: boolean }>`
  ${buttonCommonStyle}
  padding: 5px 20px 5px 16px;
  border-radius: 50vh;
  font-weight: 700;
  background: linear-gradient(92deg, #96ecff 0%, #febdff 100%);
  ${({ disabled }) =>
    disabled &&
    css`
      opacity: 0.7;
      cursor: not-allowed;
    `}
`;
