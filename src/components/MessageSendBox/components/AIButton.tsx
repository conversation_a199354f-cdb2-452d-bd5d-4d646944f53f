import { Dropdown } from 'antd';
import React from 'react';
import SvgIconStarShiningSvg from '../../../assets/svg/icon-star-shining.svg';
import { useBool } from '../../../utils/hooks/useBool';
import { useLatestCallback } from '../../../utils/hooks/useLatestCallback';
import { SvgIcon } from '../../Icon/Icon';
import { AIAssistantConversation } from './AIAssistantConversation';
import { StyledAIButton } from './GradientButton.style';

export interface AIButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  disabled?: boolean;
  className?: string;
}

export const AIButton: React.FC<AIButtonProps> = ({ className, disabled, ...props }) => {
  return (
    <StyledAIButton {...props} className={className} disabled={disabled}>
      <SvgIcon src={SvgIconStarShiningSvg} style={{ marginRight: '4px' }} />
      <span>AI assist</span>
    </StyledAIButton>
  );
};

export interface AIAssistantDropdownProps {
  question?: string;
  prompt: string;
  customerId: string;
  onAdoptAnswer: (answer: string) => void;
  sendType?: 'massText' | 'twoWay';
  disabled?: boolean;
}

export const AIAssistantDropdown: React.FC<AIAssistantDropdownProps> = ({
  children,
  question,
  prompt,
  onAdoptAnswer,
  customerId,
  sendType,
  disabled,
}) => {
  const visible = useBool(false);
  const handleAdoptAnswer = useLatestCallback((answer: string) => {
    visible.close();
    onAdoptAnswer(answer);
  });
  return (
    <Dropdown
      overlay={
        visible.value ? (
          <AIAssistantConversation
            question={question}
            prompt={prompt}
            customerId={customerId + ''}
            onAdoptAnswer={handleAdoptAnswer}
            sendType={sendType}
          />
        ) : (
          <></>
        )
      }
      visible={visible.value}
      onVisibleChange={(value) => visible.as(value)}
      placement={sendType == 'twoWay' ? 'topCenter' : 'bottomCenter'}
      trigger={['click']}
      disabled={disabled}
      arrow
    >
      {children}
    </Dropdown>
  );
};
