import { createEnum } from '../../../../store/utils/createEnum';

export const SpecialLinkType = createEnum({
  OnlineBooking: ['OnlineBooking', { label: 'Online booking' }],
  DigitalAgreement: ['DigitalAgreement', { label: 'Digital agreement' }],
  UpcomingAppointment: ['UpcomingAppointment', { label: 'Send upcoming appointments' }],
  IntakeForm: ['IntakeForm', { label: 'Submit Intake forms' }],
  PetParentPortalWeb: ['PetParentPortalWeb', { label: 'Pet parent portal invite link' }],
  PetParentPortalApp: ['PetParentPortalApp', { label: 'Pet parent app download link' }],
  Membership: ['Membership', { label: 'Subscribe memberships' }],
  CardOnFile: ['CardOnFile', { label: 'Save a card on file' }],
});

export type OnSelect = (link: string) => void;
