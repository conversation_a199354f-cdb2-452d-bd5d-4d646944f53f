import React, { type CSSProperties, useCallback } from 'react';
import { useWindowSize } from 'react-use';
import { type PricingPermissionKey } from '../../store/company/company.boxes';
import { useSerialCallback } from '../../utils/hooks/useSerialCallback';
import { Button } from '../Button/Button';
import { ImageCarouse, type ImageCarouseProps } from '../ImageCarouse/ImageCarouse';
import { Modal } from '../Modal/MobileModal';
import { Tips } from '../Pricing/PricingUpgradeModal';
import { Link } from '../Pricing/PricingUpgradeModal.style';
import { getConfig } from '../Pricing/pricing.config';
import { useGetParams } from '../Pricing/pricing.hooks';
import { ImageCarouselContainer, UpgradeBtnContainer } from './UpgradeGuideModal.style';

export interface UpgradeGuideModalProps extends Pick<ImageCarouseProps, 'imgConfigList'> {
  carouselContainerStyle?: React.CSSProperties;
  permissionKey: PricingPermissionKey;
  visible?: boolean;
  onVisibleChange?: (v: boolean) => void;
  style?: CSSProperties;
}

export function UpgradeGuideModal(props: UpgradeGuideModalProps) {
  const { permissionKey, visible, onVisibleChange, imgConfigList, carouselContainerStyle, style } = props;
  const { width } = useWindowSize();
  const params = useGetParams(permissionKey);
  const { btn, link, tips } = getConfig(permissionKey)(params);

  const onClose = useCallback(() => onVisibleChange?.(false), []);

  const handleUpgradeBtnClick = useSerialCallback(async () => {
    await btn?.onClick(() => {
      onClose?.();
    });
  });

  return (
    <Modal
      style={style}
      styleClose={{ zIndex: 1 }}
      bodyStyle={{ padding: 0, margin: 0, width: width <= 480 ? 'min(375px, 100%)' : 480 }}
      visible={!!visible}
      onClose={onClose}
    >
      <ImageCarouselContainer style={carouselContainerStyle}>
        <ImageCarouse className="image-carouse-upgrade" imgConfigList={imgConfigList} />
      </ImageCarouselContainer>
      <div className="!moe-bg-[#fff] !moe-pb-[20px] !moe-pt-[20px] !moe-rounded-[0_0_8px_8px]">
        <UpgradeBtnContainer>
          <Button
            className="!moe-shadow-none"
            btnType="primary"
            buttonRadius="circle"
            loading={handleUpgradeBtnClick.isBusy()}
            onClick={handleUpgradeBtnClick}
          >
            {btn?.text}
          </Button>
        </UpgradeBtnContainer>
        <div className="moe-flex moe-flex-col moe-items-center">
          {tips ? <Tips tips={tips} className="!moe-text-[12px] !moe-leading-[16px]" /> : null}
          {link ? <Link onClick={link.onClick}>{link.text}</Link> : null}
        </div>
      </div>
    </Modal>
  );
}
