import styled from 'styled-components';

export const ImageCarouselContainer = styled.div`
  --img-carouse-height: 390px;

  .image-carouse-upgrade {
    height: var(--img-carouse-height);
    border-radius: 8px 8px 0 0;
    background-color: #fff7e8;

    .carousel-item {
      height: var(--img-carouse-height);
      border-radius: 0;
      background-color: transparent;
      background-size: contain;
      background-position: bottom center;
    }

    .ant-carousel {
      .slick-dots-bottom {
        bottom: 12px;
        margin-bottom: 0;
      }
    }
  }
`;

export const UpgradeBtnContainer = styled.div`
  text-align: center;

  .btn-primary,
  .btn-primary:disabled {
    padding: 8px 30px;
    background-color: #f96b18;
    border-color: #f96b18;
    border-radius: 56px;
    font-weight: 700;
    font-size: 14px;
    line-height: 18px;
    color: #fff;

    &:active,
    &:focus {
      background-color: #f96b18;
      border-color: #f96b18;
    }
  }
`;
