/*
 * @since 2021-07-13 18:20:50
 * <AUTHOR> <<EMAIL>>
 */

import classNames from 'classnames';
import isNil from 'lodash/isNil';
import React, { forwardRef, memo, type ReactNode, useEffect, useImperativeHandle, useState } from 'react';
import NumberFormat, { type NumberFormatPropsBase } from 'react-number-format';
import SvgIconEditNewSvg from '../assets/svg/icon-edit-new.svg';
import { type BusinessRecord } from '../store/business/business.boxes';
import { useRefObject } from '../utils/hooks/hooks';
import { AmountInputView, BtnAmountInputView } from './AmountInput.style';
import { Button } from './Button/Button';
import { SvgIcon } from './Icon/Icon';

export interface AmountInputProps
  extends Omit<NumberFormatPropsBase, 'onValueChange' | 'onChange' | 'prefix' | 'suffix'> {
  business: BusinessRecord;
  onChange?: (value: number | undefined) => void;
  prefix?: ReactNode;
  suffix?: ReactNode;
  onFocus?: () => void;
  onBlur?: () => void;
  inputPrefix?: string;
  inputClassName?: string;
}

export const AmountInput = memo(
  forwardRef<HTMLInputElement, AmountInputProps>(
    (
      {
        className,
        inputClassName,
        business,
        value,
        defaultValue,
        onChange,
        prefix,
        suffix,
        onBlur,
        onFocus,
        inputPrefix,
        ...others
      },
      outerRef,
    ) => {
      const inputRef = useRefObject<HTMLInputElement>();
      const [focused, setFocused] = useState(others.autoFocus);

      useImperativeHandle(outerRef, () => inputRef.current as HTMLInputElement, [inputRef.current]);

      return (
        <AmountInputView className={classNames(className, { focused })} onClick={() => inputRef.current?.focus()}>
          {prefix}
          <NumberFormat
            className={inputClassName}
            onFocus={() => {
              setFocused(true);
              onFocus?.();
            }}
            onBlur={() => {
              setFocused(false);
              onBlur?.();
            }}
            getInputRef={inputRef}
            thousandSeparator={business.thousandSeparator()}
            decimalSeparator={business.decimalSeparator()}
            thousandsGroupStyle="thousand"
            decimalScale={business.decimalScale()}
            fixedDecimalScale
            displayType="input"
            prefix={inputPrefix ?? business.currencySymbol}
            value={value}
            defaultValue={defaultValue}
            allowNegative={false}
            allowLeadingZeros={false}
            onValueChange={(v) => onChange?.(v.floatValue)}
            type="text"
            {...others}
          />
          {suffix}
        </AmountInputView>
      );
    },
  ),
);

/**
 * 针对 Smart Tip Threshold 输入定制的带按钮金额输入
 */
export const AmountInputBtn = memo<AmountInputProps & { onUpdate?: () => void; loading: boolean; hasError: boolean }>(
  ({ className, business, value, defaultValue, onChange, onUpdate, onFocus, onBlur, hasError, loading, ...others }) => {
    const ref = useRefObject<HTMLInputElement>();
    const [focused, setFocused] = useState(others.autoFocus);

    const [currentAmount, setCurrentAmount] = useState(+(defaultValue || 0));
    useEffect(() => {
      if (!isNil(value)) {
        setCurrentAmount(+value);
      }
    }, [value]);

    // sam: 失焦之后调用 NumberInput 的 onBlur？这种一层一层的 reactive 比较难读，可以将 onBlur 方法封装一下，这里直接调用。
    useEffect(() => {
      const handler = ({ key }: KeyboardEvent) => {
        if (focused && key === 'Enter' && !hasError) {
          ref.current?.blur();
        }
      };
      window.addEventListener('keypress', handler);
      return () => {
        window.removeEventListener('keypress', handler);
      };
    }, [focused, hasError]);

    return (
      <BtnAmountInputView className={className} onClick={() => ref.current?.focus()}>
        <Button loading={loading} className={classNames('amount-btn', { focused })} btnType="primary" size="sm">
          {business.formatAmount(currentAmount)}
          <SvgIcon className="btn-icon" src={SvgIconEditNewSvg} size={12} color="#fff" />
        </Button>
        <AmountInput
          ref={ref}
          business={business}
          className={classNames('amount-input', { focused })}
          onFocus={() => {
            setFocused(true);
            onFocus?.();
          }}
          onBlur={() => {
            if (!hasError) {
              setFocused(false);
              onUpdate?.();
            }
            onBlur?.();
          }}
          onChange={(v) => {
            setCurrentAmount(v || 0);
            onChange?.(v);
          }}
          value={value}
          defaultValue={defaultValue}
          {...others}
        />
      </BtnAmountInputView>
    );
  },
);
