import { Modal } from '@moego/ui';
import { useDispatch } from 'amos';
import React, { memo } from 'react';

import { declinePetVaccineRequest } from '../../store/customer/vaccineReview.action';
import { dismissPendingReviewNotification } from '../../store/notification/notification.actions';
import { useAsyncCallback } from '../../utils/hooks/useAsyncCallback';
// Define data structures based on usage and linter errors

export const VaccineExpiredModal = memo<{ onClose: () => void; notificationId: number; petVaccineRequestId: string }>(
  ({ onClose, notificationId, petVaccineRequestId }) => {
    const dispatch = useDispatch();
    const handleDismiss = useAsyncCallback(async () => {
      await declinePetVaccineRequest({
        id: petVaccineRequestId,
        notificationId: notificationId?.toString(),
        force: true,
      });
      await dispatch(dismissPendingReviewNotification(notificationId));
      onClose?.();
    });
    return (
      <Modal
        isOpen
        className="moe-w-[492px]"
        title="Unable to load vaccine information"
        onClose={onClose}
        confirmText="Dismiss notification"
        showCancelButton={false}
        autoCloseOnConfirm={false}
        confirmButtonProps={{
          isLoading: handleDismiss.loading,
        }}
        onConfirm={handleDismiss}
      >
        <span className="moe-font-regular">
          {/* text-lint ignore */}
          We couldn&apos;t find the vaccine record related to this notification. The associated client, pet, or vaccine
          may have been deleted.
        </span>
      </Modal>
    );
  },
);
