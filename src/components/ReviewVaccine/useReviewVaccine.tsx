import React from 'react';
import { useFloatableHost } from '../../utils/hooks/useFloatableHost';

import { useSerialCallback } from '../../utils/hooks/useSerialCallback';
import { type ReviewPetVaccineRequestResult } from '@moego/api-web/moego/api/business_customer/v1/business_pet_vaccine_request_api';
import { ReviewVaccineModal } from './ReviewVaccineModal';
import { VaccineExpiredModal } from './VaccineExpiredModal';
// Define data structures based on usage and linter errors

export function useReviewVaccine() {
  const { mountModal } = useFloatableHost();

  const showVaccineExpiredModal = useSerialCallback(
    async (props: { onClose?: () => void; notificationId: number; petVaccineRequestId: string }) => {
      const { closeFloatable, promise } = mountModal(() => (
        <VaccineExpiredModal
          onClose={() => {
            closeFloatable?.();
            props.onClose?.();
          }}
          notificationId={props.notificationId}
          petVaccineRequestId={props.petVaccineRequestId}
        />
      ));
      return promise;
    },
  );

  const showReviewVaccineModal = useSerialCallback(
    async (props: {
      petVaccineRequestId: string;
      notificationId: number;
      data: ReviewPetVaccineRequestResult;
      onClose?: () => void;
    }) => {
      const { data, onClose: onCloseProp } = props;

      const { closeFloatable, promise } = mountModal(() => (
        <ReviewVaccineModal
          petVaccineRequestId={props.petVaccineRequestId}
          notificationId={props.notificationId}
          onClose={() => {
            closeFloatable();
            onCloseProp?.();
          }}
          data={data}
        />
      ));
      return promise;
    },
  );

  return {
    showVaccineExpiredModal,
    showReviewVaccineModal,
  };
}
