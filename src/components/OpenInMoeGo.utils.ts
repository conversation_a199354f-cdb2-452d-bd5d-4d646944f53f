/*
 * @since 2021-05-20 21:31:37
 * <AUTHOR> <<EMAIL>>
 */
import {
  PATH_ANNUAL_CONTRACT,
  PATH_BASIC_PLAN_SETUP,
  PATH_BUSINESS_ADD,
  PATH_BUSINESS_LANDING,
  PATH_CARE_AGREEMENT,
  PATH_CARE_CONTRACT,
  PATH_CARE_SETUP,
  PATH_FINANCE_SUBSCRIPTION,
  PATH_GOOGLE_CALENDAR_SYNC_AUTH,
  PATH_GOOGLE_CALENDAR_SYNC_CALLBACK,
  PATH_GOOGLE_CALENDAR_SYNC_SETTING,
  PATH_INVITE,
  PATH_LOG_SHEET,
  PATH_MOBILE_EDITOR,
  PATH_MOEGO_PAY_CONTRACT,
  PATH_PURCHASE,
  PATH_QUESTIONNAIRE,
  PATH_REFEREE,
  PATH_SALES_SETUP,
  PATH_SELECT_PLAN,
  PATH_SIGN_IN,
  PATH_SIGN_UP,
  PATH_SQUARE_POS,
  PATH_SQUARE_POS_CALLBACK,
} from '../router/paths';

export const SCHEME_BUSINESS_V2 = 'commoementmoegobusiness';
export const ANDROID_APP_ID_BUSINESS_V2 = 'com.moement.moego.business';
export const ANDROID_STORE_URL_BUSINESS_V2 = 'https://play.google.com/store/apps/details?id=com.moement.moego.business';
export const IOS_STORE_URL_BUSINESS_V2 = 'https://itunes.apple.com/us/app/moego-2-0/id1561621817?mt=8';
/* NOTE:
 * Universal link now only supports domain `www.moego.pet`, which is configured in the mobile project (Capabilities -> Associated Domains)
 * And the only supported path is `/download`, which is configured in the file `~/.well-known/apple-app-site-association` of domain above
 * So make sure not to change the domain and path unceremoniously
 */
export const UNIVERSAL_LINK_V2 = 'https://www.moego.pet/download';

export const OPEN_IN_MOEGO_PAGE_BLACK_LIST = [
  PATH_BUSINESS_ADD,
  PATH_BUSINESS_LANDING,
  PATH_GOOGLE_CALENDAR_SYNC_AUTH,
  PATH_GOOGLE_CALENDAR_SYNC_CALLBACK,
  PATH_GOOGLE_CALENDAR_SYNC_SETTING,
  PATH_SQUARE_POS,
  PATH_SQUARE_POS_CALLBACK,
  PATH_MOBILE_EDITOR,
  PATH_SIGN_IN,
  PATH_SIGN_UP,
  PATH_PURCHASE,
  PATH_INVITE,
  PATH_REFEREE,

  // MoeGo Care Program pages are not supported on mobile app
  PATH_CARE_SETUP,
  PATH_CARE_AGREEMENT,
  PATH_CARE_CONTRACT,

  // contract links can be opened in mobile browser, no need to open in app
  PATH_MOEGO_PAY_CONTRACT,
  PATH_ANNUAL_CONTRACT,

  PATH_SALES_SETUP,
  PATH_BASIC_PLAN_SETUP,
  PATH_QUESTIONNAIRE,
  PATH_SELECT_PLAN,
  PATH_FINANCE_SUBSCRIPTION,

  // intercom
  PATH_LOG_SHEET,
];

export function openInMoeGo(uri?: string) {
  // TODO: Support custom path (which needs changes in both mobile & desktop project)
  window.open(UNIVERSAL_LINK_V2, '_self');
}
