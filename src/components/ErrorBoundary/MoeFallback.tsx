import React, { memo } from 'react';
import Nothing from '../../assets/image/nothing.png';
import { Condition } from '../Condition';

export interface MoeFallbackProps {
  className?: string;
  description?: string;
}

export const MoeFallback = memo<MoeFallbackProps>(function MoeFallback(props) {
  const { className, description = 'Please contact us' } = props;

  return (
    <div
      className={
        'moe-h-full moe-flex moe-flex-col moe-items-center moe-justify-center moe-gap-y-[8px]' + (className ?? '')
      }
    >
      <div className="moe-flex moe-flex-col moe-items-center moe-gap-y-[20px]">
        <img src={Nothing} width={140} />
        <div className="moe-text-base moe-text-[#333] moe-font-semibold">Oops! Something went wrong...</div>
      </div>
      <Condition if={description}>
        <div className="moe-text-sm moe-font-medium">{description}</div>
      </Condition>
    </div>
  );
});
