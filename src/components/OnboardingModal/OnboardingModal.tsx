import React, { memo } from 'react';
import { useWindowSize } from 'react-use';
import { Button } from '../../components/Button/Button';
import { ImageCarouse, type ImageCarouseImgConfig } from '../../components/ImageCarouse/ImageCarouse';
import { type MobileModalProps, Modal } from '../../components/Modal/MobileModal';
import { useSerialCallback } from '../../utils/hooks/useSerialCallback';
import { AdditionalDesc, Start2New } from './OnboardingModal.styles';

export interface OnBoardingModalProps extends MobileModalProps {
  imgConfigList: ImageCarouseImgConfig[];
  btnText: string;
  desc: string;
  onClick: () => Promise<void> | void;
}

export const OnBoardingModal = memo<OnBoardingModalProps>(
  ({ imgConfigList, btnText, desc, onClick, ...modalProps }) => {
    const { width } = useWindowSize();

    const handleClick = useSerialCallback(async () => {
      await onClick();
    });

    return (
      <Modal
        {...modalProps}
        styleClose={{ zIndex: 1 }}
        bodyStyle={{ padding: 0, margin: 0, width: width <= 480 ? 'min(375px, 100%)' : 480 }}
      >
        <ImageCarouse imgConfigList={imgConfigList} />
        <Start2New>
          <Button btnType="primary" buttonRadius="circle" loading={handleClick.isBusy()} onClick={handleClick}>
            {btnText}
          </Button>
        </Start2New>
        <AdditionalDesc>{desc}</AdditionalDesc>
      </Modal>
    );
  },
);
