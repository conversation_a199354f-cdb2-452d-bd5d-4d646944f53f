import { Heading, Spin, Text } from '@moego/ui';
import { useSelector } from 'amos';
import { type Dayjs } from 'dayjs';
import React, { memo } from 'react';
import { StaffScheduleApptWarningType } from '../../store/calendarLatest/calendar.types';
import { staffMapBox } from '../../store/staff/staff.boxes';
import { getStaffColorCode } from '../../utils/utils';
import { CompressedAvatar } from '../CompressedAvatar/CompressedAvatar';
import { Switch } from '../SwitchCase';
import { useStaffScheduleWarning } from './hooks/useStaffScheduleWarning';

export interface AvailableStaffOptionProps {
  staffId: number;
  start?: Dayjs;
  duration?: number;
  showConflict?: boolean;
}
export const AvailableStaffOption = memo((props: AvailableStaffOptionProps) => {
  const { staffId, start, duration, showConflict = true } = props;
  const [staff] = useSelector(staffMapBox.mustGetItem(staffId));

  const { warningType, loading } = useStaffScheduleWarning(staffId, start, duration);

  return (
    <div className="moe-flex moe-items-center moe-gap-s">
      <CompressedAvatar.Staff
        size="s"
        src={staff.avatarPath}
        color={getStaffColorCode(staff.colorCode, staff.firstName, staff.lastName)}
        name={staff.fullName()}
        isBordered
      />
      <div className="moe-flex moe-flex-col moe-gap-xxs">
        <Heading size="6">{staff.fullName()}</Heading>
        <Switch>
          <Switch.Case if={loading}>
            <Spin isLoading size="s" />
          </Switch.Case>
          <Switch.Case if={showConflict && warningType === StaffScheduleApptWarningType.ConflictWithExistingAppt}>
            <Text variant="small" className="moe-text-warning">
              Conflict with existing booking
            </Text>
          </Switch.Case>
          <Switch.Case if={warningType === StaffScheduleApptWarningType.NotWorking}>
            <Text variant="small" className="moe-text-warning">
              Not working at this time
            </Text>
          </Switch.Case>
        </Switch>
      </div>
    </div>
  );
});
