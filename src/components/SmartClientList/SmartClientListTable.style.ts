import { Input } from 'antd';
import { opacify } from 'polished';
import styled from 'styled-components';

export const SearchInput = styled(Input)`
  max-width: 300px;
  min-width: 200px;
  height: 32px;
  border-radius: 20px;
  border: solid 1px #cdcdcd;
  background-color: #ffffff;
  font-size: 14px;
  font-weight: 600;
  padding-left: 16px;
  margin-right: 16px;
  &:hover,
  &:active {
    border-color: #c9d5e1;
    box-shadow: 0 0 0 2px ${opacify(0.2, '#c9d5e100')};
  }
  > .ant-input-prefix {
    margin-right: 8px;
  }
  input {
    font-size: 14px;
    font-weight: 600;
    line-height: normal;
    color: #333333;

    &::placeholder {
      color: rgba(0, 0, 0, 0.2);
      font-weight: 400;
    }
  }
  .anticon {
    color: #2c2c2c;
  }
`;

export const CustomerNameView = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  white-space: normal;
  margin-right: -12px;
  > span {
    display: inline-block;
    vertical-align: middle;
    margin-right: 8px;
  }
  .name {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }
  .tag {
    display: inline-block;
    border-radius: 12px;
    font-size: 12px;
    line-height: 16px;
    padding: 4px 8px;
    white-space: nowrap;
    &.tag-frequency {
      color: #f45e00;
      background-color: rgba(244, 94, 0, 0.1);
    }
    &.tag-new {
      color: #f96b18;
      background-color: #fff7f0;
    }
    &.tag-inactive {
      color: #d1000d;
      background-color: rgba(209, 0, 13, 0.1);
    }
    &.tag-prospect {
      color: #faad14;
      background-color: rgba(255, 247, 232, 1);
    }
  }
  > .avatar-wrapper {
    flex-shrink: 0;
    margin-right: 12px;
  }
  > .info {
    flex: 1;
  }
`;
