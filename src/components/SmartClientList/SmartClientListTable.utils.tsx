import { CallCenter } from '@moego/call-center';
import { MinorCallOutlined, MinorMessageOutlined } from '@moego/icons-react';
import { IconButton } from '@moego/ui';
import { useMemoizedFn } from 'ahooks';
import { useSelector } from 'amos';
import { Badge, Dropdown, Popover } from 'antd';
import React, { type MouseEventHandler, memo, useCallback } from 'react';
import SvgIconMessageSvg from '../../assets/svg/icon-message.svg';
import SvgIconSortableSvg from '../../assets/svg/icon-sortable.svg';
import { type MessageCenterState } from '../../router/paths';
import { selectCurrentBusiness } from '../../store/business/business.selectors';
import { selectCurrentPermissions } from '../../store/business/role.selectors';
import { currentCompanyIdBox } from '../../store/company/company.boxes';
import { CustomerListSortFields, type CustomerRecord } from '../../store/customer/customer.boxes';
import { META_DATA_KEY_LIST } from '../../store/metadata/metadata.config';
import { PetLifeStatus, type PetRecord } from '../../store/pet/pet.boxes';
import { currentStaffIdBox } from '../../store/staff/staff.boxes';
import { type PartialRequired, type PickProps } from '../../store/utils/RecordMap';
import { useRedirectMessage } from '../../utils/BusinessUtil';
import { ReportActionName } from '../../utils/reportType';
import { reportData } from '../../utils/tracker';
import { transformFrequencyToString } from '../../utils/transformFrequencyToString';
import { Avatar } from '../Avatar/Avatar';
import { DropdownItem, DropdownList } from '../DropdownList.style';
import { WithPermission } from '../GuardRoute/WithPermission';
import { SvgIcon } from '../Icon/Icon';
import { Line } from '../Style/Style';
import { WithBooleanMetadata } from '../WithBooleanMetadata/WithBooleanMetadata';
import { CustomerNameView } from './SmartClientListTable.style';

export interface RenderClientTableColumnClientNameProps {
  currentValue?: string;
  onClick: (value: string) => void;
}

export const ClientTableColumnClientName = memo<RenderClientTableColumnClientNameProps>(({ currentValue, onClick }) => (
  <Dropdown
    placement="bottomLeft"
    overlay={
      <DropdownList>
        {CustomerListSortFields.values.map((value) => (
          <DropdownItem active={value === currentValue} key={value} onClick={() => onClick(value)}>
            {CustomerListSortFields.mapLabels[value]}
          </DropdownItem>
        ))}
      </DropdownList>
    }
  >
    <div className="moe-inline-flex moe-items-center moe-cursor-pointer">
      Client name &nbsp;
      <SvgIcon src={SvgIconSortableSvg} />
    </div>
  </Dropdown>
));
export function renderCustomerName({
  client,
  onClick,
  className,
  dot,
}: {
  client: PartialRequired<CustomerRecord, 'firstName' | 'lastName' | 'avatarPath'>;
  onClick?: MouseEventHandler;
  className?: string;
  dot?: boolean;
}) {
  const color = client?.clientColor ? client.clientColor : void 0;

  return (
    <CustomerNameView onClick={onClick} className={className}>
      {dot ? (
        <Badge dot offset={[-4, 4]}>
          <Avatar type="user" src={client.avatarPath} size="32px" info={client} />
        </Badge>
      ) : (
        <Avatar type="user" src={client.avatarPath} size="32px" info={client} />
      )}

      <div className="name" style={{ color }}>
        {client.firstName} {client.lastName}
      </div>
    </CustomerNameView>
  );
}

export function renderCustomerNormalTags(client: Partial<CustomerRecord>) {
  return (
    <>
      {client?.inactive! > 0 && <span className="tag tag-inactive">Inactive</span>}
      {client?.isProspectCustomer ? (
        <span className="tag tag-prospect">Prospect</span>
      ) : (
        client?.isNewCustomer && <span className="tag tag-new">New</span>
      )}
    </>
  );
}

export function renderCustomerTags(client: Partial<CustomerRecord>, className?: string) {
  return (
    <CustomerNameView className={className}>
      {renderCustomerNormalTags(client)}
      <Popover
        placement="bottomLeft"
        content={`Grooming frequency: ${transformFrequencyToString(
          client?.preferredFrequencyDay,
          client?.preferredFrequencyType,
        )}`}
      >
        <span className="tag tag-frequency">
          {transformFrequencyToString(client?.preferredFrequencyDay, client?.preferredFrequencyType)}
        </span>
      </Popover>
    </CustomerNameView>
  );
}

export function renderCustomerNameWithoutFrequency(client: Partial<CustomerRecord>) {
  const color = client.clientColor ? client.clientColor : void 0;
  return (
    <CustomerNameView>
      <Avatar type="user" src={client.avatarPath} size="36px" info={client} />
      <div className="info">
        <div className="name" style={{ color }}>
          {client.firstName} {client.lastName}
        </div>
        <div className="tags">{renderCustomerNormalTags(client)}</div>
      </div>
    </CustomerNameView>
  );
}

export function renderCustomerPetList(petList: PickProps<PetRecord, 'petName' | 'breed' | 'lifeStatus'>[]) {
  const alivePets = petList.filter((pet) => pet.lifeStatus === PetLifeStatus.Alive);
  const passAwayPets = petList.filter((pet) => pet.lifeStatus === PetLifeStatus.PassAway);
  const showedPets = alivePets.slice(0, 2);

  const extra = passAwayPets.length + alivePets.length - showedPets.length;
  const extraText = extra ? ` +${extra}` : '';
  return (
    <div>
      {showedPets.length
        ? showedPets.map((pet, i) => (
            <div key={i}>
              {pet.petName}
              {pet.breed ? ` (${pet.breed})` : ''}
              {i === showedPets.length - 1 ? extraText : null}
            </div>
          ))
        : extraText}
    </div>
  );
}

interface RenderOptions {
  customer: PickProps<CustomerRecord, 'phoneNumber' | 'customerId'>;
  className?: string;
  iconClassName?: string;
  onLackCustomerId?: () => void;
  useNewIcon?: boolean;
  acceptCall?: boolean;
}
export function useRenderCustomerPhoneNumber(messageCenterParams?: MessageCenterState) {
  const [business, permissions, curStaffId, curCompanyId] = useSelector(
    selectCurrentBusiness(),
    selectCurrentPermissions(),
    currentStaffIdBox,
    currentCompanyIdBox,
  );

  const handleToMessage = useRedirectMessage();

  return useCallback(
    (options: RenderOptions) => {
      const { customer, className, iconClassName, onLackCustomerId, useNewIcon, acceptCall = false } = options;
      if (!customer?.phoneNumber) {
        return null;
      }
      if (!permissions.has('viewMessageCenter')) {
        return business.formatPhoneNumber(customer.phoneNumber);
      }

      const handleClick = () => {
        if (!customer?.customerId && onLackCustomerId) {
          onLackCustomerId();
          return;
        }
        reportData(ReportActionName.ObAbandonListContact);
        handleToMessage(customer.customerId, messageCenterParams);
      };

      const onPhoneCalling = useMemoizedFn(() => {
        if (!customer?.customerId && onLackCustomerId) {
          onLackCustomerId();
          return;
        }
        CallCenter?.call?.({
          businessId: business.id,
          companyId: curCompanyId,
          staffId: curStaffId,
          customerId: customer.customerId,
        });
      });

      return (
        <Line
          onClick={(e) => {
            e.stopPropagation();
          }}
          className={className}
        >
          <span data-action="contactInfo-click" className="rr-mask">
            {business.formatPhoneNumber(customer.phoneNumber)}
          </span>
          {useNewIcon ? (
            <IconButton
              variant="primary"
              color="transparent"
              icon={<MinorMessageOutlined />}
              className={iconClassName}
              onPress={() => {
                // by default, IconButton will stopPropagation, and press event will not Propagation to Line's onClick
                handleClick();
              }}
            />
          ) : (
            <SvgIcon size={18} src={SvgIconMessageSvg} className={iconClassName} onClick={() => handleClick()} />
          )}
          <WithBooleanMetadata metaDataKey={META_DATA_KEY_LIST.EngagementCenter}>
            {acceptCall && (
              <WithPermission permissions={['callFromWeb']}>
                <IconButton
                  variant="primary"
                  color="transparent"
                  icon={<MinorCallOutlined />}
                  className={iconClassName}
                  onPress={() => {
                    // by default, IconButton will stopPropagation, and press event will not Propagation to Line's onClick
                    onPhoneCalling();
                  }}
                />
              </WithPermission>
            )}
          </WithBooleanMetadata>
        </Line>
      );
    },
    [business, permissions, handleToMessage],
  );
}
