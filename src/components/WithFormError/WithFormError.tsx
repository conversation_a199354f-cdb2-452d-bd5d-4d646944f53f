import { MinorErrorFilled } from '@moego/icons-react';
import { Text, cn } from '@moego/ui';
import { helpTextVariants } from '@moego/ui/dist/esm/components/Form/FormItemHelpText.style';
import React, { forwardRef, memo, type PropsWithChildren, useImperativeHandle, useMemo, useRef } from 'react';
import { Condition } from '../Condition';

export interface WithFormErrorProps {
  errorMessage?: string;
  className?: string;
}

export interface WithFormErrorRef {
  focus: () => void;
  blur: () => void;
}

export const WithFormError = memo(
  forwardRef<WithFormErrorRef, PropsWithChildren<WithFormErrorProps>>((props, ref) => {
    const { children, errorMessage, className } = props;

    const domRef = useRef<HTMLDivElement>(null);

    const slots = useMemo(() => {
      return helpTextVariants({});
    }, []);

    const handleFocus = () => {
      domRef.current?.scrollIntoView();
    };

    useImperativeHandle(ref, () => ({
      focus: handleFocus,
      blur: handleFocus,
    }));

    return (
      <div ref={domRef}>
        {children}
        <Condition if={errorMessage}>
          <div className={cn('moe-mt-xxs', className)}>
            <Text
              variant="small"
              as="div"
              className={slots.error({
                className: cn(slots.base()),
              })}
            >
              <MinorErrorFilled className="moe-mr-xxs" />
              {errorMessage}
            </Text>
          </div>
        </Condition>
      </div>
    );
  }),
);
