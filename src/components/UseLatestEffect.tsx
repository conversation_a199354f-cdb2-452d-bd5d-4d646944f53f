import type React from 'react';
import { useEffect } from 'react';
import { useLatest } from 'react-use';

export interface UseEffectLatestCbProps {
  cb: React.EffectCallback;
  deps: React.DependencyList;
}

/**
 * 在组件挂载、deps 变化时执行 callback
 * 主要用于 <Form.Item shouldUpdate> 的 render 函数，不封装额外组件情况下使用
 */
export const UseLatestEffect: React.FC<UseEffectLatestCbProps> = (props) => {
  const { cb, deps } = props;
  const latestCbRef = useLatest(cb);
  useEffect(() => {
    const destructor = latestCbRef.current?.();
    return destructor;
  }, deps);
  return null;
};
