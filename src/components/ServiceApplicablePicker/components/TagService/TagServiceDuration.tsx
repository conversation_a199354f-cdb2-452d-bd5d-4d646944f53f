import { ServiceOverrideType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { Text, Tooltip } from '@moego/ui';
import classNames from 'classnames';
import React from 'react';
import IconTimerSvg from '../../../../assets/svg/icon-clock-in.svg';
import { renderCountableNounPlurals } from '../../../../utils/utils';
import { Condition } from '../../../Condition';
import { SvgIcon } from '../../../Icon/Icon';
import { type TagServiceDurationProps, getTagServiceTooltips } from './TagService.utils';

export function TagServiceDuration(props: TagServiceDurationProps) {
  const {
    hiddenIcon = false,
    className,
    duration,
    iconSize = 17,
    overrideType = ServiceOverrideType.UNSPECIFIED,
    iconClassName,
    defaultColor = 'moe-text-secondary',
    hour = false,
    durationFmt: durationFmtText,
    petIds,
  } = props;
  const durationFmt =
    typeof duration === 'number'
      ? hour
        ? renderCountableNounPlurals(duration, 'hour')
        : renderCountableNounPlurals(duration, 'min')
      : duration;

  const { isOverridden, overriddenClassName, tooltipContent } = getTagServiceTooltips({
    target: 'duration',
    overrideType,
    defaultColor,
  });

  const isMultiplePetsOverridden = (petIds?.length ?? 0) > 1 && isOverridden;

  return (
    <Condition if={!isMultiplePetsOverridden}>
      <div
        className={classNames(
          className,
          defaultColor,
          'moe-flex moe-items-center moe-gap-x-[4px] moe-text-sm moe-font-medium moe-cursor-default',
        )}
      >
        <Condition if={!hiddenIcon}>
          <SvgIcon src={IconTimerSvg} size={iconSize} className={classNames(overriddenClassName, iconClassName)} />
        </Condition>
        <Tooltip isDisabled={!isOverridden} content={tooltipContent}>
          <Text variant="small" className={overriddenClassName}>
            {durationFmtText || durationFmt}
          </Text>
        </Tooltip>
      </div>
    </Condition>
  );
}
