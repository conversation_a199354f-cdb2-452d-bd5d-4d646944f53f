import { Text, cn } from '@moego/ui';
import React, { type PropsWithChildren, memo } from 'react';
import { LABEL_KEY, getPriceUnitText } from '../../utils/priceUnit';

export interface TagServiceUnitProps {
  priceUnit: number;
  className?: string;
}

export const TagServiceUnit = memo((props: PropsWithChildren<TagServiceUnitProps>) => {
  const { priceUnit, className, children } = props;
  const priceUnitText = getPriceUnitText(priceUnit, LABEL_KEY.label);
  if (priceUnitText) {
    return (
      <Text variant="small" className={cn('moe-text-secondary', className)}>
        {priceUnitText}
      </Text>
    );
  }
  return <>{children}</>;
});
