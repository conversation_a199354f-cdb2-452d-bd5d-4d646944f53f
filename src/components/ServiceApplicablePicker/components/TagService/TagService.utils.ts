import { ServiceOverrideType } from '@moego/api-web/moego/models/offering/v1/service_enum';

export interface TagServiceProps {
  className?: string;
  iconSize?: number;
  iconClassName?: string;
  txtClassName?: string;
  defaultColor?: string;
  hiddenIcon?: boolean;
  overrideType?: ServiceOverrideType;
  petIds?: string[];
}

export interface TagServicePriceProps extends TagServiceProps {
  price?: React.ReactNode;
}

export interface TagServiceDurationProps extends TagServiceProps {
  duration?: React.ReactNode;
  hour?: boolean;
  durationFmt?: string;
}

export interface TagServiceDurationParams extends Pick<TagServiceProps, 'overrideType' | 'defaultColor'> {
  target: 'price' | 'duration';
}

export const getTagServiceTooltips = (params: TagServiceDurationParams) => {
  const { target, overrideType = ServiceOverrideType.UNSPECIFIED, defaultColor } = params;

  const isOverridden = [ServiceOverrideType.CLIENT, ServiceOverrideType.STAFF].includes(overrideType);
  const overriddenClassName = isOverridden ? '!moe-text-[#3985F5]' : defaultColor;
  const tooltipContent =
    overrideType === ServiceOverrideType.STAFF ? `customized ${target} for this staff` : `saved ${target} for this pet`;

  return {
    isOverridden,
    overriddenClassName,
    tooltipContent,
  };
};
