import { type ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { Text } from '@moego/ui';
import React, { memo } from 'react';
import { type RenderService } from '../../../PetAndServicePicker/types/types';
import { useFormatServiceTime } from '../../hooks/useFormatServiceTime';
import { shouldHideServiceDate } from '../../utils/shouldHideServiceDate';
import { getDateTypeDisplayInfo, getFormattedDateTypeDateLabel } from '../../../DateType/DateType.utils';
import { useSelector } from 'amos';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import { isRequireDedicatedStaffServiceAddOn } from '../../../../container/Appt/store/appt.utils';

export interface ServiceAddOnDateTimeProps {
  service: RenderService;
  mainServiceItemType?: ServiceItemType;
}

export const ServiceAddOnDateTime = memo<ServiceAddOnDateTimeProps>(function AddOnSpecific(props) {
  const { service, mainServiceItemType } = props;
  const { specificDates, dateType, serviceItemType, startDate, startTime, serviceType, requireDedicatedStaff } =
    service;
  const formatDateTime = useFormatServiceTime();
  const [business] = useSelector(selectCurrentBusiness);
  const shouldHideDate = shouldHideServiceDate({
    serviceItemType,
    mainServiceItemType,
    serviceType,
  });

  const { showDateTypeLabel, showTimeLabel, isSpecificDate, isDatePoint } = getDateTypeDisplayInfo({
    dateType,
    mainServiceItemType,
    isRequireDedicatedStaff: isRequireDedicatedStaffServiceAddOn(serviceType, serviceItemType, requireDedicatedStaff),
  });

  // 如果有特殊的 dateType 参数，按照特殊情况处理
  if (showDateTypeLabel) {
    const finalDateLabel = getFormattedDateTypeDateLabel(business, dateType, isSpecificDate, specificDates);
    const finalTimeLabel = formatDateTime({ startDate, startTime, onlyTime: true });
    return <Text variant="small">{showTimeLabel ? `${finalDateLabel}, ${finalTimeLabel}` : finalDateLabel}</Text>;
  }

  // 如果有 date point 或者有 startDate，展示默认格式
  if (isDatePoint || startDate) {
    return <Text variant="small">{formatDateTime({ startDate, startTime, onlyTime: shouldHideDate })}</Text>;
  }

  // 什么都没有
  return null;
});
