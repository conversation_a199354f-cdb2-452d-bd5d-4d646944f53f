import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { Heading } from '@moego/ui';
import { useSelector } from 'amos';
import dayjs from 'dayjs';
import React, { memo, useMemo } from 'react';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import { type PetServicePriceContentProps, type RenderService } from '../../../PetAndServicePicker/types/types';
import { TagServiceDuration } from '../TagService/TagServiceDuration';
import { TagServicePrice } from '../TagService/TagServicePrice';
import { GroomingServiceStaff } from './GroomingServiceStaff';
import { ServiceAddOnDateTime } from './ServiceAddOnDateTime';
import { SlotFreeServiceTag } from './SlotFreeServiceTag';

export interface PetServiceItemProps extends PetServicePriceContentProps {
  service: RenderService;
  mainServiceItemType?: ServiceItemType;
}

export const PetServiceItem = memo<PetServiceItemProps>(function PetServiceItem(props) {
  const { service, mainServiceItemType, priceContent } = props;
  const {
    serviceTime,
    operationList,
    serviceName,
    servicePrice,
    startDate,
    startTime = 0,
    staffId,
    priceOverrideType,
    durationOverrideType,
  } = service;

  const [business] = useSelector(selectCurrentBusiness());

  const formattedPrice = business.formatAmount(servicePrice);
  const isMultiStaff = operationList?.length > 1;
  const staffIds = isMultiStaff ? operationList.map((op) => op.staffId) : [staffId];

  const order = useMemo(() => {
    return mainServiceItemType === ServiceItemType.GROOMING && startDate
      ? dayjs(startDate).setMinutes(startTime).unix()
      : 'initial';
  }, [mainServiceItemType, startDate, startTime]);

  const tagServicePrice = (
    <TagServicePrice hiddenIcon className="moe-flex-shrink-0" price={formattedPrice} overrideType={priceOverrideType} />
  );

  return (
    <div className="moe-flex" style={{ order }}>
      <div className="moe-flex-1 moe-min-w-0 moe-flex moe-flex-col moe-items-stretch moe-gap-y-[4px]">
        <Heading size="5" className="moe-text-primary moe-flex moe-items-center moe-gap-x-xxs">
          {serviceName}
          <SlotFreeServiceTag service={service} />
        </Heading>
        <ServiceAddOnDateTime service={service} mainServiceItemType={mainServiceItemType} />
        <div className="moe-flex moe-items-center moe-gap-x-[8px] moe-text-secondary">
          {priceContent?.(service.serviceId + '', tagServicePrice) || tagServicePrice}
          <TagServiceDuration
            hiddenIcon
            className="moe-flex-shrink-0"
            duration={serviceTime}
            overrideType={durationOverrideType}
          />
        </div>
      </div>
      <GroomingServiceStaff staffIds={staffIds} />
    </div>
  );
});
