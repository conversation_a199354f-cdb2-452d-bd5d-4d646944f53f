import React, { memo, useMemo } from 'react';
import { type RenderService } from '../../../PetAndServicePicker/types/types';
import { Tag } from '@moego/ui';
import { useSelector } from 'amos';
import { apptServiceMapBox, ApptServiceRecord } from '../../../../container/Appt/store/appt.boxes';
import { useTicketDrawerDetail } from '../../../../container/Appt/modules/ApptDetailDrawer/hooks/useTicketDrawerDetail';

export interface SlotFreeServiceTagProps {
  service: RenderService;
}

/**
 * 在 service name 旁边展示 slot free service 的 tag
 */
export const SlotFreeServiceTag = memo<SlotFreeServiceTagProps>((props) => {
  const { service } = props;
  const [apptService] = useSelector(apptServiceMapBox);
  const { appointmentId } = useTicketDrawerDetail();

  const isSlotFreeService = useMemo(() => {
    return apptService.mustGetItem(ApptServiceRecord.createOwnId(appointmentId, service.id)).isSlotFreeService;
  }, [service, apptService, appointmentId]);

  if (!isSlotFreeService) return null;

  return (
    <Tag
      variant="filled"
      className="moe-text-primary"
      classNames={{ label: 'moe-w-full', base: 'moe-max-w-[120px]' }}
      label="Slot free service"
    />
  );
});

SlotFreeServiceTag.displayName = 'SlotFreeServiceTag';
