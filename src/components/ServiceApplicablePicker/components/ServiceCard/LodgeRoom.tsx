import { type BoardingSplitLodgingScheduleDef } from '@moego/api-web/moego/models/appointment/v1/boarding_split_lodging_defs';
import { Markup, Tooltip, cn } from '@moego/ui';
import { useSelector } from 'amos';
import React, { Fragment, memo } from 'react';
import SvgIconLodgeRoomSvg from '../../../../assets/svg/icon-lodge-room.svg';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import { lodgingUnitMapBox } from '../../../../store/lodging/lodgingUnit.boxes';
import { isNormal } from '../../../../store/utils/identifier';
import { Condition } from '../../../Condition';
import { SvgIcon } from '../../../Icon/Icon';

export interface LodgeRoomProps {
  className?: string;
  lodgingId?: string;
  splitLodgings?: BoardingSplitLodgingScheduleDef[];
}

export const LodgeRoom = memo<LodgeRoomProps>(function LodgeRoom(props) {
  const { className, lodgingId, splitLodgings } = props;
  const [lodgingUnitMap, business] = useSelector(lodgingUnitMapBox, selectCurrentBusiness);

  return (
    <Fragment>
      <Condition if={isNormal(lodgingId)}>
        <div className={cn('moe-flex moe-gap-x-[4px] moe-items-center', className)}>
          <SvgIcon src={SvgIconLodgeRoomSvg} size={24} color="#202020" />
          <div className="moe-text-[12px] moe-leading-[16px] moe-text-[#333333] moe-truncate moe-max-w-[150px]">
            {lodgingUnitMap.mustGetItem(lodgingId || '').name}
          </div>
        </div>
      </Condition>
      <Condition if={splitLodgings?.length}>
        <Tooltip
          side="top"
          content={
            <div>
              <Markup variant="small">Lodging assignment</Markup>
              {splitLodgings &&
                splitLodgings.map((item, index) => (
                  <p key={[item.lodgingId, index].join('-')}>
                    {`${business.formatDate(item.startDate)} - ${business.formatDate(item.endDate)}: ${lodgingUnitMap.mustGetItem(item.lodgingId).name}`}
                  </p>
                ))}
            </div>
          }
        >
          <div className={cn('moe-flex moe-gap-x-[4px] moe-items-center', className)}>
            <SvgIcon src={SvgIconLodgeRoomSvg} size={24} color="#202020" />
            <div className="moe-text-[12px] moe-leading-[16px] moe-text-[#333333] moe-truncate moe-max-w-[150px]">
              {splitLodgings && splitLodgings.map((item) => lodgingUnitMap.mustGetItem(item.lodgingId).name).join(', ')}
            </div>
          </div>
        </Tooltip>
      </Condition>
    </Fragment>
  );
});
