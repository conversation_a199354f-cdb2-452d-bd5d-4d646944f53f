import { ServicePriceUnit } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { money2Number } from '@moego/reporting';
import { Heading, Text } from '@moego/ui';
import { useSelector } from 'amos';
import React, { memo, useMemo } from 'react';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import { type PetServicePriceContentProps, type RenderService } from '../../../PetAndServicePicker/types/types';
import { useFeedingInstructions } from '../../hooks/useFeedingInstructions';
import { useMedicationInstructions } from '../../hooks/useMedicationInstructions';
import { LABEL_KEY, getPriceUnitText } from '../../utils/priceUnit';
import { TagServicePrice } from '../TagService/TagServicePrice';
import { BlockInfo } from './BlockInfo';
import { LodgeRoom } from './LodgeRoom';

export interface BoardingServiceProps extends PetServicePriceContentProps {
  service: RenderService;
  petId: string;
}

export const BoardingService = memo<BoardingServiceProps>((props) => {
  const { service, priceContent } = props;
  const [business] = useSelector(selectCurrentBusiness());
  const fmtFeeding = useFeedingInstructions();
  const fmtMedication = useMedicationInstructions();

  const splitPriceRangeText = useMemo(() => {
    const priceList = (service.splitLodgings || []).map((lodgingInfo) => money2Number(lodgingInfo.price));
    const minPrice = priceList.reduce((a, b) => Math.min(a, b), Infinity);
    const maxPrice = priceList.reduce((a, b) => Math.max(a, b), -Infinity);
    if (minPrice === maxPrice) {
      return business.formatAmount(minPrice);
    }
    return `${business.formatAmount(minPrice)} - ${business.formatAmount(maxPrice)}`;
  }, [service.splitLodgings, business]);

  const tagServicePrice = (
    <TagServicePrice
      hiddenIcon
      price={service.splitLodgings?.length ? splitPriceRangeText : business.formatAmount(service.servicePrice || 0)}
      overrideType={service.priceOverrideType}
    />
  );

  return (
    <div className="moe-flex moe-flex-col moe-gap-y-[12px]">
      <div className="moe-flex moe-items-center">
        <div className="moe-flex-1 moe-min-w-0 moe-flex moe-flex-col moe-items-stretch moe-gap-y-[4px]">
          <div className="moe-flex moe-items-center">
            <Heading size="5" className="moe-text-primary">
              {service.serviceName}
            </Heading>
          </div>
          <Text as="div" variant="small" className="moe-flex moe-items-center moe-gap-x-[8px] moe-text-secondary">
            {priceContent ? priceContent(service.serviceId + '', tagServicePrice) : tagServicePrice}
            <div>{getPriceUnitText(service.priceUnit || ServicePriceUnit.PER_NIGHT, LABEL_KEY.label)}</div>
          </Text>
        </div>
        <LodgeRoom lodgingId={service.lodgingId} splitLodgings={service.splitLodgings} />
      </div>
      <BlockInfo label="Feeding instructions" instructions={fmtFeeding(service.feedings)} />
      <BlockInfo
        label="Medication instructions"
        instructions={fmtMedication(service.medications, { showDate: true })}
      />
    </div>
  );
});
