import { cn } from '@moego/ui';
import { CheckboxIcon } from '@moego/ui/dist/esm/components/Checkbox/CheckboxIcon';
import React, { memo } from 'react';
import { matchApptServiceScene } from '../../../../container/Appt/store/appt.options';
import { ApptServiceScene } from '../../../../container/Appt/store/appt.types';
import { Condition } from '../../../Condition';
import { useServiceInfo } from '../../hooks/useServiceInfo';
import { type ServiceEntry } from '../../types/serviceEntry';
import { TagServiceDuration } from '../TagService/TagServiceDuration';
import { TagServicePrice } from '../TagService/TagServicePrice';
import { TagServiceUnit } from '../TagService/TagServiceUnit';

interface PickServiceItemProps {
  petId?: number;
  value: number;
  info?: ServiceEntry;
  isDisabled?: boolean;
  isSelected?: boolean;
  isMultiple?: boolean;
}

export const PickServiceItem = memo((props: PickServiceItemProps) => {
  const { petId, value, info, isDisabled, isSelected, isMultiple } = props;
  const {
    serviceName,
    formattedPrice,
    formattedDuration,
    priceOverrideType,
    durationOverrideType,
    service: { serviceItemType, priceUnit },
  } = useServiceInfo({ serviceId: value, staffId: info?.staffId, petIds: [petId] });

  const showDuration = matchApptServiceScene(ApptServiceScene.ServicePickerDuration, { serviceItemType });

  return (
    <div
      className={cn('moe-flex moe-w-full', {
        'moe-cursor-not-allowed moe-opacity-50': isDisabled,
      })}
    >
      <Condition if={isMultiple}>
        <CheckboxIcon
          isSelected={isSelected}
          className="moe-inline-flex moe-items-center moe-mb-none moe-shrink-0 moe-mr-[8px]"
          isDisabled={isDisabled}
        />
      </Condition>
      <div className="moe-flex-1 moe-truncate moe-font-bold moe-text-base">{serviceName || info?.serviceName}</div>
      <div className="moe-flex moe-items-center moe-gap-x-[8px] moe-text-[14px] moe-text-[#666] moe-leading-[18px] moe-font-medium">
        <TagServicePrice price={formattedPrice} overrideType={priceOverrideType} />
        <TagServiceUnit priceUnit={priceUnit}>
          <Condition if={showDuration}>
            <TagServiceDuration duration={formattedDuration} overrideType={durationOverrideType} />
          </Condition>
        </TagServiceUnit>
      </div>
    </div>
  );
});
