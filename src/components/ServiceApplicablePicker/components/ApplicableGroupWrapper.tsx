import { RadioGroup } from '@moego/ui';
import React, { type PropsWithChildren } from 'react';
import { type ApplicableCategoryProps } from './ApplicableCategory';

interface ApplicableGroupWrapperProps {
  type?: ApplicableCategoryProps['itemType'];
  value?: string;
  onChange?: (e: string) => void;
}
export const ApplicableGroupWrapper = (props: PropsWithChildren<ApplicableGroupWrapperProps>) => {
  const { value, onChange, children, type = 'Checkbox' } = props;
  return type === 'Checkbox' ? (
    <>{children}</>
  ) : (
    <RadioGroup value={value} onChange={(v) => onChange?.(v)}>
      {children}
    </RadioGroup>
  );
};
