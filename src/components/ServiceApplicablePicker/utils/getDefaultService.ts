import { ServiceOverrideType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import dayjs from 'dayjs';
import { EServiceType } from '../../../config/interface';
import { MultiStaffWorkMode } from '../../../store/createTicket/createTicket.types';
import { ScopeType } from '../../../store/grooming/grooming.boxes';
import { ID_ANONYMOUS } from '../../../store/utils/identifier';
import { type ServiceEntry } from '../types/serviceEntry';
import { isOperationListHasValue } from './isMultipleStaffService';

// Date.now() 在 js 循环中可能根本不需要时间，所以会拿到一致的结果
// performance.now() 在某些浏览器会降低精度。所以我们直接叠加 1 就好
let startId = Date.now();
export const generateId = () => String(++startId);

export const DefaultQuantityPerDay = 1;

export const getDefaultService = (srv?: Partial<ServiceEntry>): ServiceEntry => {
  const baseService = {
    id: srv?.id || generateId(),
    staffId: ID_ANONYMOUS,
    serviceId: ID_ANONYMOUS,
    serviceTime: 0,
    servicePrice: 0,
    workMode: MultiStaffWorkMode.SERIAL,
    scopeTypePrice: ScopeType.NotSaved,
    scopeTypeTime: ScopeType.NotSaved,
    operationList: [],
    serviceType: EServiceType.Service,
    quantityPerDay: DefaultQuantityPerDay,
    ...srv,
  };

  // 确保 enableOperation 和 operationList 的一致性
  const operationList = baseService.operationList || [];
  const enableOperation = isOperationListHasValue(operationList);

  return {
    ...baseService,
    operationList,
    enableOperation,
  };
};

export const isOverriddenByPet = (...overrideTypeList: (ServiceOverrideType | undefined)[]) => {
  return overrideTypeList.map((type) => type === ServiceOverrideType.CLIENT);
};

export const getFirstStartInServiceList = (serviceList: ServiceEntry[]) => {
  if (!serviceList.length) {
    return undefined;
  }
  return serviceList.reduce((pre, curr) => {
    const { startDate = '', startTime = 0 } = curr;
    const { startDate: preStartDate = '', startTime: preStartTime = 0 } = pre;
    return dayjs(startDate).setMinutes(startTime).isBefore(dayjs(preStartDate).setMinutes(preStartTime)) ? curr : pre;
  });
};
