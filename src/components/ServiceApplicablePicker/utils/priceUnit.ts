import { ServicePriceUnit } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { type EnumValues, createEnum } from '../../../store/utils/createEnum';

export enum LABEL_KEY {
  label = 'label',
  unit = 'unit',
}

export const PriceUnitEnum = createEnum<string, ServicePriceUnit, Record<LABEL_KEY, string>>({
  PER_SESSION: [ServicePriceUnit.PER_SESSION, { label: 'per session', unit: 'session' }],
  PER_NIGHT: [ServicePriceUnit.PER_NIGHT, { label: 'per night', unit: 'night' }],
  PER_HOUR: [ServicePriceUnit.PER_HOUR, { label: 'per hour', unit: 'hour' }],
  PER_DAY: [ServicePriceUnit.PER_DAY, { label: 'per day', unit: 'day' }],
});

export const getPriceUnitText = (priceUnit: EnumValues<typeof PriceUnitEnum>, labelKey: LABEL_KEY = LABEL_KEY.unit) => {
  if ([ServicePriceUnit.PER_NIGHT, ServicePriceUnit.PER_DAY].includes(priceUnit)) {
    const { label, unit } = PriceUnitEnum.mapLabels[priceUnit];
    return labelKey === LABEL_KEY.unit ? unit : label;
  }
  return '';
};
