import { type CommonServiceCategoryView } from '@moego/api-web/moego/api/offering/v1/service_api';
import { type CustomizedServiceCategoryView } from '@moego/api-web/moego/models/offering/v1/service_models';

export const transformCategoryList = (categoryList: CustomizedServiceCategoryView[]) => {
  return categoryList.flatMap((item) => {
    const { categoryId, name, services } = item;
    return (services ?? []).map((service) => ({
      ...service,
      categoryId,
      categoryName: name,
    }));
  });
};

export const transformCommonCategories = (commonCategories: CommonServiceCategoryView[]) => {
  return commonCategories.flatMap((item) => {
    const { categoryId, name, commonServices } = item;
    return (commonServices ?? []).map((service) => ({
      ...service,
      categoryId,
      categoryName: name,
    }));
  });
};
