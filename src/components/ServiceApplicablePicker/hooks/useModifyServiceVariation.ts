import { ServiceOverrideType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { useCallback } from 'react';
import { type ServiceEntry } from '../types/serviceEntry';
import { isOverriddenByPet } from '../utils/getDefaultService';
import { useGetServiceVariationByStaff } from './useServiceVariationByStaff';

type ModifyServiceVariationReturn = Pick<
  ServiceEntry,
  'servicePrice' | 'serviceTime' | 'priceOverrideType' | 'durationOverrideType'
>;

export const useModifyServiceVariation = () => {
  const getServiceVariationByStaff = useGetServiceVariationByStaff();

  return useCallback(
    (service: ServiceEntry, staffId: number): ModifyServiceVariationReturn => {
      const { serviceId, priceOverrideType, durationOverrideType, servicePrice, serviceTime } = service;

      const { price, duration, hasStaffPrice, hasStaffDuration } =
        getServiceVariationByStaff({ serviceId: serviceId, staffId }) || {};

      const [isPriceOverriddenByPet, isDurationOverriddenByPet] = isOverriddenByPet(
        priceOverrideType,
        durationOverrideType,
      );

      const isModifiedByStaffPrice = hasStaffPrice && !isPriceOverriddenByPet;
      const isModifiedByStaffDuration = hasStaffDuration && !isDurationOverriddenByPet;

      const nextPrice = isModifiedByStaffPrice ? price : servicePrice;
      const nextDuration = isModifiedByStaffDuration ? duration : serviceTime;
      const nextPriceOverrideType = isModifiedByStaffPrice ? ServiceOverrideType.STAFF : service.priceOverrideType;
      const nextDurationOverrideType = isModifiedByStaffDuration
        ? ServiceOverrideType.STAFF
        : service.durationOverrideType;

      return {
        servicePrice: nextPrice!,
        serviceTime: nextDuration!,
        priceOverrideType: nextPriceOverrideType,
        durationOverrideType: nextDurationOverrideType,
      };
    },
    [getServiceVariationByStaff],
  );
};
