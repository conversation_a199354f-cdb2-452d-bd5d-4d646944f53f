import { useSelector } from 'amos';
import { useCallback } from 'react';
import { type ServiceFeedingMedication } from '../../../container/Appt/store/appt.types';
import { selectCurrentBusiness } from '../../../store/business/business.selectors';
import { beautifyInstructions } from '../utils/beautifyInstructions';

export function useFeedingInstructions() {
  const [business] = useSelector(selectCurrentBusiness());

  const fmtFeeding = useCallback(
    (feedings?: ServiceFeedingMedication['feedings']) => {
      if (!Array.isArray(feedings) || feedings.length === 0) {
        return [];
      }

      const feedingDescriptionsMap = feedings.reduce(
        (acc, curr) => {
          const {
            feedingTimes = [],
            feedingAmount,
            feedingUnit,
            feedingType,
            feedingSource,
            feedingInstruction,
            feedingNote,
          } = curr;
          const intro = beautifyInstructions(
            {
              amount: feedingAmount,
              unit: feedingUnit,
              type: feedingType,
              source: feedingSource,
              instruction: feedingInstruction,
              note: feedingNote,
              times: '',
            },
            { appendPeriod: false },
          );

          feedingTimes.forEach(({ extraJson }) => {
            const { label } = extraJson;

            acc[label] = acc[label] || [];
            acc[label].push(intro);
          });

          return acc;
        },
        {} as Record<string, string[]>,
      );

      const feedingDescriptions = Object.entries(feedingDescriptionsMap).map(
        ([key, value]) => `${key}: ${value.join('; ')}`,
      );
      return feedingDescriptions;
    },
    [business],
  );

  return fmtFeeding;
}
