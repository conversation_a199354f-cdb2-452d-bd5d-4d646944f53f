import { useSelector } from 'amos';
import { isNil } from 'lodash';
import { useCallback } from 'react';
import { serviceMapBox } from '../../../store/service/service.boxes';
import { ID_ANONYMOUS, isNormal } from '../../../store/utils/identifier';
import { type ServiceStaffOptions } from '../types/options';

export interface ServiceVariationByStaff {
  price: number | undefined;
  duration: number | undefined;
  hasStaffPrice: boolean;
  hasStaffDuration: boolean;
}

export interface ServiceVariationRange {
  price: [number, number] | null;
  duration: [number, number] | null;
  hasStaffPrice: boolean;
  hasStaffDuration: boolean;
}

export type UseServiceVariationReturn<T> = T extends { staffId: number }
  ? ServiceVariationByStaff
  : ServiceVariationRange;

export const useServiceVariationByStaff = (options: Required<ServiceStaffOptions>) => {
  const { serviceId, staffId } = options;
  const [service] = useSelector(serviceMapBox.mustGetItem(serviceId));
  return service.getStaffOverrideVariation(staffId);
};

export const useServiceVariationRange = (serviceId: ServiceStaffOptions['serviceId']) => {
  const [service] = useSelector(serviceMapBox.mustGetItem(serviceId));
  return service.getServiceVariationRange();
};

/**
 * 通过 hooks 的方式获取 service variation by staff
 */
export const useServiceVariation = <T extends ServiceStaffOptions>(options: T): UseServiceVariationReturn<T> => {
  const { serviceId, staffId } = options;
  const { price, duration } = useServiceVariationByStaff({ serviceId, staffId: staffId ?? ID_ANONYMOUS }) ?? {};
  const { priceRange, durationRange } = useServiceVariationRange(serviceId);

  if (!isNormal(staffId)) {
    const { isRanged: isPriceRanged, min: minPrice, max: maxPrice } = priceRange;
    const { isRanged: isDurationRanged, min: minDuration, max: maxDuration } = durationRange;

    return {
      price: isPriceRanged ? [minPrice, maxPrice] : null,
      duration: isDurationRanged ? [minDuration, maxDuration] : null,
      hasStaffPrice: false,
      hasStaffDuration: false,
    } as UseServiceVariationReturn<T>;
  }

  return {
    price,
    duration,
    hasStaffPrice: !isNil(price),
    hasStaffDuration: !isNil(duration),
  } as UseServiceVariationReturn<T>;
};

/**
 * 通过函数调用的方式获取 service variation by staff
 */
export const useGetServiceVariationByStaff = () => {
  const [serviceMap] = useSelector(serviceMapBox);

  return useCallback(
    (options: Required<ServiceStaffOptions>) => {
      const { serviceId, staffId } = options;
      const service = serviceMap.mustGetItem(serviceId);

      const { price, duration } = service.getStaffOverrideVariation(staffId) || {};
      return {
        price,
        duration,
        hasStaffPrice: !isNil(price),
        hasStaffDuration: !isNil(duration),
      };
    },
    [serviceMap],
  );
};
