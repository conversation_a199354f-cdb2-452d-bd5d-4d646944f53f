import { useSelector } from 'amos';
import { useCallback } from 'react';
import { type ServiceFeedingMedication } from '../../../container/Appt/store/appt.types';
import { selectCurrentBusiness } from '../../../store/business/business.selectors';
import { beautifyInstructions } from '../utils/beautifyInstructions';

export function useMedicationInstructions() {
  const [business] = useSelector(selectCurrentBusiness());

  const fmtMedication = useCallback(
    (medications?: ServiceFeedingMedication['medications'], options?: { showDate?: boolean }) => {
      if (!Array.isArray(medications) || medications.length === 0) {
        return [];
      }
      const medicationInstruction = medications.map((item) => {
        const { medicationTimes, medicationAmount, medicationUnit, medicationName, medicationNote, selectedDate } =
          item;
        const { dateType, specificDates } = selectedDate || {};
        const timeStr = (medicationTimes || []).map((time) => time.extraJson.label).join(', ');
        const formattedSpecificDates = specificDates?.map((date) => business.formatDate(date));
        const formattedSelectedDate = options?.showDate
          ? {
              dateType,
              specificDates: formattedSpecificDates,
            }
          : undefined;

        return beautifyInstructions({
          times: timeStr,
          amount: medicationAmount,
          unit: medicationUnit,
          type: medicationName,
          instruction: medicationNote,
          ...formattedSelectedDate,
        });
      });
      return medicationInstruction;
    },
    [business],
  );

  return fmtMedication;
}
