import { cn } from '@moego/ui';
import React from 'react';
import { memo } from 'react';
import styled from 'styled-components';
import SmartTipPreviewMobilePng from '../../assets/image/smart-tip-preview-mobile.png';

export const PreviewMobileView = styled.div`
  .preview-mobile {
    height: 525px;
    .sub-title {
      font-size: 18px;
      line-height: 24px;
      margin-top: 20px;
      margin-bottom: 20px;
    }
  }
  .preview-phone-wrapper {
    background-image: url(${SmartTipPreviewMobilePng});
    background-size: contain;
    background-repeat: no-repeat;
    margin: auto;
    width: 415.5px;
    height: 840px;
    transform-origin: top;
    padding: 67px 27px 66px 20px;
    display: flex;
    flex-direction: column;

    .body {
      overflow: hidden;
      flex: 1 1 auto;
    }
  }
`;

interface PreviewMobileProps {
  children: React.ReactNode;
  className?: string;
  transformScale?: number;
}

export const PreviewMobile = memo(({ children, className, transformScale = 0.625 }: PreviewMobileProps) => {
  // 0.625 is 10 / 16
  return (
    <PreviewMobileView>
      <div className={cn('preview-mobile', className)}>
        <div
          className="preview-phone-wrapper"
          style={{
            transform: `scale(${transformScale})`,
          }}
        >
          <div className="body">{children}</div>
        </div>
      </div>
    </PreviewMobileView>
  );
});
