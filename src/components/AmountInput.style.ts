/*
 * @since 2021-07-13 20:03:07
 * <AUTHOR> <<EMAIL>>
 */

import styled from 'styled-components';

export const AmountInputView = styled.div`
  display: flex;
  flex: 1;
  align-items: center;
  border-radius: 4px;
  border: 1px solid #c9d5e1;
  height: 32px;
  padding: 4px 11px;
  font-size: 16px;
  font-weight: 600;

  &.focused {
    border-color: #de5a21;
    box-shadow: 0 0 0 2px rgb(41 205 87 / 20%);
  }

  > input {
    flex: 1;
    border: 0;
    outline: none;
    font-size: 16px;
    font-weight: 600;

    &:focus {
      outline: none;
    }
  }
`;

export const BtnAmountInputView = styled.div`
  position: relative;
  height: 28px;
  width: 100%;

  .amount-btn {
    /* for smart-tip */
    min-width: 100px;
    max-width: 180px;

    /* normal style */
    height: 28px;
    position: absolute;
    top: 0;
    left: 0;
    padding: 0 14px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    font-weight: 700;
    font-size: 12px;
    line-height: 16px;

    &.btn-radius-normal {
      min-width: 100px;
      height: 28px;
      border-radius: 56px;
    }

    &.focused {
      display: none;
    }

    .btn-icon {
      margin-left: 4px;
    }

    /* loading 状态展示 */
    .ant-spin-dot {
      font-size: 16px;
    }
  }

  .amount-input {
    width: 100%;
    height: 28px;
    position: absolute;
    top: 0;
    left: 0;
    font-weight: 500;
    font-size: 14px;
    line-height: 20px;
    border-radius: 100px;
    opacity: 0;
    width: 100px;
    cursor: pointer;
    box-shadow: none;

    &.focused {
      opacity: 1;
      width: auto;
      cursor: inherit;

      > input {
        cursor: initial;
      }
    }

    > input {
      width: 100%;
      cursor: pointer;
      font-weight: 500;
      font-size: 14px;
      line-height: 20px;
      caret-color: #f96b18;
      color: #333;
    }
  }
`;
