import { CallCenter, GlobalCallView } from '@moego/call-center';
import { useBoolean } from 'ahooks';
import { useSelector } from 'amos';
import React, { useEffect } from 'react';
import { useHistory } from 'react-router';
import { PATH_CUSTOMER_ADD, PATH_CUSTOMER_OVERVIEW, PATH_CUSTOMER_PETS, PATH_MESSAGE_CENTER } from '../../router/paths';
import { selectCurrentPermissions } from '../../store/business/role.selectors';
import { MessageThreadQueryType } from '../../store/message/message.boxes';
import { LoggerModule, logger } from '../../utils/logger';
import { CALL_CENTER_DISABLED } from '../../utils/pagesConstants';
import { KEY_BUSINESS_ID, KEY_COMPANY_ID } from '../../utils/querySessionContextConst';
import { ReportModule } from '../ErrorBoundary/types';
import { withErrorBoundary } from '../ErrorBoundary/withErrorBoundary';

const GlobalCallCenterComponent = React.memo(() => {
  const history = useHistory();
  const [permissions] = useSelector(selectCurrentPermissions());

  const canViewClientProfile = permissions.has('viewIndividualClientProfile');
  const [init, initAction] = useBoolean(false);
  const disabledCallCenter = CALL_CENTER_DISABLED.includes(location.pathname);

  const updateToken = async () => {
    await CallCenter.updateToken();
  };

  useEffect(() => {
    async function initCallCenter() {
      if (!disabledCallCenter) {
        // 1 hour block token refresh
        await CallCenter.register(false, 60 * 60 * 1000);
        CallCenter.registerLogSystem((log) => {
          if (log.info) {
            logger.get(LoggerModule.CALL_CENTER).info(log.info, { ...log.extra, core: window.callCenterCore });
          }
          if (log.error) {
            logger
              .get(LoggerModule.CALL_CENTER)
              .error(log.error?.message, { ...log.extra, core: window.callCenterCore });
          }
        });
        initAction.setTrue();
        window.addEventListener('focus', updateToken);
      }
    }
    initCallCenter();
    return () => {
      CallCenter.destroy();
      window.removeEventListener('focus', updateToken);
    };
  }, []);

  if (!init) {
    return null;
  }
  return (
    <GlobalCallView
      onCreateClient={() => {
        history.push(PATH_CUSTOMER_ADD.build());
      }}
      onClientClick={({
        companyUuid,
        businessUuid,
        customerId,
      }: {
        companyUuid?: number;
        businessUuid?: number;
        customerId?: number;
      }) => {
        if (customerId) {
          if (!canViewClientProfile) return;
          history.push(
            PATH_CUSTOMER_OVERVIEW.queried(
              {
                [KEY_BUSINESS_ID]: businessUuid,
                [KEY_COMPANY_ID]: companyUuid,
              },
              { customerId: customerId },
            ),
          );
        }
      }}
      onPetClick={({
        companyUuid,
        businessUuid,
        customerId,
        petId,
      }: {
        companyUuid?: number;
        businessUuid?: number;
        customerId?: number;
        petId?: number;
      }) => {
        if (customerId) {
          if (!canViewClientProfile) return;
          history.push(
            PATH_CUSTOMER_PETS.queried(
              {
                [KEY_BUSINESS_ID]: businessUuid,
                [KEY_COMPANY_ID]: companyUuid,
              },
              { customerId: customerId, petId: petId },
            ),
          );
        }
      }}
      onLinkedClick={({
        companyUuid,
        businessUuid,
        customerId,
      }: {
        companyUuid?: number;
        businessUuid?: number;
        customerId?: number;
      }) => {
        history.push(
          PATH_MESSAGE_CENTER.queried({
            clientId: customerId,
            [KEY_BUSINESS_ID]: businessUuid,
            [KEY_COMPANY_ID]: companyUuid,
            chatsType: MessageThreadQueryType.Open,
          }),
        );
      }}
    />
  );
});

export const GlobalCallCenter = withErrorBoundary(GlobalCallCenterComponent, { reportModule: ReportModule.CallCenter });
