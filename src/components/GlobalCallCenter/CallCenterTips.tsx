import { CallCenter } from '@moego/call-center';
import { CallCenterStatus, CallErrorIdEnum } from '@moego/call-center';
import { MajorInfoOutlined, MajorRefreshOutlined } from '@moego/icons-react';
import { useSerialCallback } from '@moego/tools';
import { Text, Tooltip, cn } from '@moego/ui';
import React, { memo, useEffect } from 'react';
import { useLocation } from 'react-router';
import { CALL_CENTER_DISABLED } from '../../utils/pagesConstants';

export interface CallCenterTipsProps {}

const CallStatusTextMap: Record<CallCenterStatus, string> = {
  [CallCenterStatus.unRegistered]: 'Offline',
  [CallCenterStatus.registered]: 'Loading',
  [CallCenterStatus.ready]: 'Loading',
  [CallCenterStatus.allReady]: 'Online',
  [CallCenterStatus.error]: 'Error',
  [CallCenterStatus.calling]: 'Online',
  [CallCenterStatus.connecting]: 'Online',
};

export const CallCenterTips = memo<CallCenterTipsProps>(({}) => {
  const location = useLocation();
  const disabledCallCenter = CALL_CENTER_DISABLED.includes(location.pathname);
  const [status, setStatus] = React.useState<CallCenterStatus>(CallCenterStatus.unRegistered);
  const [errorMsg, setErrorMsg] = React.useState<string>('');

  const activeGetCallCenterStatus = async () => {
    const newStatus = CallCenter?.getStatus?.();
    setStatus(newStatus);
  };
  const onCallCenterError = (connectUuid: number | string, error: Error) => {
    if (CallErrorIdEnum.TwilioError === connectUuid) {
      setErrorMsg(error.message);
    }
  };
  const initCallCenter = async () => {
    if (!disabledCallCenter) {
      // 1 hour block token refresh
      CallCenter?.addListener('onStatusChanged', setStatus);
      CallCenter?.addListener('onError', onCallCenterError);
      activeGetCallCenterStatus();
    }
  };
  const removeCallCenter = () => {
    CallCenter?.removeListener?.('onStatusChanged', setStatus);
    CallCenter?.removeListener?.('onError', onCallCenterError);
  };
  const register = useSerialCallback(async () => {
    if (!disabledCallCenter) {
      await CallCenter?.reloadDevice?.();
      activeGetCallCenterStatus();
      removeCallCenter();
      initCallCenter();
    }
  });
  useEffect(() => {
    initCallCenter();
    return () => {
      removeCallCenter();
    };
  }, []);

  return (
    <div className="moe-flex moe-items-center">
      <Text as="span" variant="small" className="moe-mr-[2px]">
        Calling:
      </Text>
      <Text
        as="span"
        variant="small"
        className={cn({
          'moe-text-success':
            status === CallCenterStatus.allReady ||
            status === CallCenterStatus.calling ||
            status === CallCenterStatus.connecting,
          'moe-text-danger': status === CallCenterStatus.error,
          'moe-text-warning': status === CallCenterStatus.ready || status === CallCenterStatus.registered,
          'moe-text-disabled': status === CallCenterStatus.unRegistered,
        })}
      >
        {CallStatusTextMap[status]}
      </Text>
      {status === CallCenterStatus.error && (
        <Tooltip content={errorMsg}>
          <MajorInfoOutlined className="moe-text-danger moe-ml-1 moe-cursor-pointer !moe-text-l" />
        </Tooltip>
      )}
      {[CallCenterStatus.error, CallCenterStatus.unRegistered].includes(status) && (
        <Tooltip content="Refresh. If that doesn't work, please refresh the page.">
          <MajorRefreshOutlined onClick={register} className="moe-cursor-pointer moe-ml-2 !moe-text-l" />
        </Tooltip>
      )}
    </div>
  );
});
