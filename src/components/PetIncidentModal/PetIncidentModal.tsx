import {
  DatePicker,
  Form,
  Input,
  Modal,
  Radio,
  RadioGroup,
  LegacySelect as Select,
  Spin,
  TimePicker,
  Upload,
  UploadStatus,
  transformer,
  useForm,
  useWatch,
  validateUtils,
} from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import dayjs from 'dayjs';
import React, { type FC, memo, useEffect, useMemo, useRef } from 'react';
import { stringToNumberV2 } from '../../container/settings/Settings/ServicesSetting/utils/inputTransformer';
import { uploadFiles } from '../../container/settings/Settings/ServicesSetting/utils/uploadFiles';
import { selectCurrentBusiness } from '../../store/business/business.selectors';
import {
  createPetIncidentReport,
  getPetIncidentReportList,
  updatePetIncidentReport,
} from '../../store/pet/petIncident.actions';
import { petIncidentReportMapBox } from '../../store/pet/petIncident.boxes';
import { selectPetIncidentTypeList } from '../../store/pet/petIncident.selectors';
import { isNormal } from '../../store/utils/identifier';
import { DATE_TIME_FORMAT_FULL_TZ } from '../../utils/DateTimeUtil';
import { useLatestCallback } from '../../utils/hooks/useLatestCallback';
import { useSerialCallback } from '../../utils/hooks/useSerialCallback';
import { SingleLocationSelector } from '../Business/SingleLocationSelector';
import { PetMultipleSelector } from '../PetMultipleSelector/PetMultipleSelector';
import { toastApi } from '../Toast/Toast';
import { generateUID } from '../Upload/MoeGoUIUpload';
import { type PetIncidentModalForm, type PetIncidentModalProps } from './PetIncidentModal.type';
import { MAX_DESCRIPTION_LENGTH } from './PetIncidentModal.utils';

/**
 * @description
 * 宠物事件记录弹窗组件
 * 用于创建或编辑宠物相关事件记录的模态框组件。支持选择日期时间、事件类型、添加描述和上传附件等功能。
 *
 * @example
 * ```ts
 * dispatch(
 *  openGlobalModal({
 *    petIncident: {
 *      visible: true,
 *      // 编辑模式传入事件 id 如果是新增则不传
 *      incidentId: 'xxx',
 *      // 默认选中的宠物ID列表
 *      defaultPetIdList: [String(petId)]
 *    }
 *  })
 * );
 * ```
 *
 * @props
 * - incidentId?: string - 事件 id 用于编辑模式
 * - defaultPetIdList?: string[] - 默认选中的宠物ID列表
 */
export const PetIncidentModal: FC<PetIncidentModalProps> = memo((props) => {
  const dispatch = useDispatch();
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const { visible, incidentId, defaultPetIdList = [], onClose } = props;
  const [business, petIncidentTypeList, petIncidentReportMap] = useSelector(
    selectCurrentBusiness,
    selectPetIncidentTypeList,
    petIncidentReportMapBox,
  );
  const isEditMode = isNormal(incidentId);
  const form = useForm<PetIncidentModalForm>();
  const [attachmentFiles = [], petIds = []] = useWatch({
    control: form.control,
    name: ['attachmentFiles', 'petIds'],
  });
  const isUploading = attachmentFiles?.some(({ status }) => status === UploadStatus.uploading);
  const petIncidentOptions = useMemo(
    () =>
      petIncidentTypeList
        .filter((item) => !item.isDeleted)
        .sort((a, b) => b.sort - a.sort)
        .map((item) => ({ label: item.name, value: item.id })),
    [petIncidentTypeList],
  );
  const petOptionIdList = useMemo(() => {
    const list = isEditMode ? petIncidentReportMap.mustGetItem(incidentId).petIds : defaultPetIdList;
    return list.map((item) => Number(item));
  }, [isEditMode, petIncidentReportMap, incidentId, defaultPetIdList]);
  const handleSave = useSerialCallback(async () => {
    await form.handleSubmit(async (values) => {
      const { incidentDate, incidentTime } = values;
      const time = incidentDate
        .clone()
        .hour(incidentTime.hour())
        .minute(incidentTime.minute())
        .utc()
        .format(DATE_TIME_FORMAT_FULL_TZ);
      const payload = {
        incidentTime: time,
        petIds: values.petIds,
        businessId: values.businessId,
        description: values.description,
        isVetVisited: values.isVetVisited,
        isPetInjured: values.isPetInjured,
        isStaffInjured: values.isStaffInjured,
        incidentTypeId: values.incidentTypeId,
        attachmentFiles: values.attachmentFiles.map((file, index) => ({
          name: file.name ?? `File-${index + 1}`,
          url: file.url ?? '',
        })),
      };
      if (isEditMode) {
        await dispatch(updatePetIncidentReport({ id: incidentId, incidentReport: payload }));
        toastApi.success('Incident updated successfully');
      } else {
        await dispatch(createPetIncidentReport(payload));
        toastApi.success('Incident added');
      }
      onClose();
    })();
  });
  const handleUploadFiles = useLatestCallback((options) => uploadFiles(dispatch, options));
  // 监听上传状态变化
  useEffect(() => {
    const hasNewUploadingFile = attachmentFiles?.some((file) => file.status === UploadStatus.uploading);
    if (hasNewUploadingFile) {
      setTimeout(() => {
        scrollContainerRef.current?.scrollTo({
          top: scrollContainerRef.current.scrollHeight,
          behavior: 'smooth',
        });
      }, 100);
    }
  }, [attachmentFiles]);
  // 初始化表单数据
  useEffect(() => {
    if (isEditMode) {
      const incident = petIncidentReportMap.mustGetItem(incidentId);
      form.reset({
        petIds: incident.petIds.map((id) => String(id)),
        businessId: String(incident.businessId),
        incidentTypeId: String(incident.incidentTypeId),
        incidentDate: dayjs(incident.incidentTime),
        incidentTime: dayjs(incident.incidentTime),
        description: incident.description,
        isVetVisited: incident.isVetVisited,
        isPetInjured: incident.isPetInjured,
        isStaffInjured: incident.isStaffInjured,
        attachmentFiles: incident.attachmentFiles.map((file, index) => ({
          url: file.url,
          name: file.name,
          uid: generateUID(index),
          status: UploadStatus.success,
        })),
      });
    } else {
      form.reset({
        petIds: defaultPetIdList,
        incidentTypeId: undefined,
        incidentDate: dayjs(),
        incidentTime: dayjs(),
        description: undefined,
        businessId: String(business.id),
        isStaffInjured: false,
        isPetInjured: false,
        isVetVisited: false,
        attachmentFiles: [],
      });
    }
  }, [isEditMode, incidentId, petIncidentReportMap, defaultPetIdList]);

  const defaultPetId = defaultPetIdList?.[0];
  const getPetIncident = useSerialCallback(
    async (petId: string) => await dispatch(getPetIncidentReportList({ petId })),
  );
  const isLoadingDetail = getPetIncident.isBusy();
  useEffect(() => {
    if (isNormal(defaultPetId)) {
      getPetIncident(String(defaultPetId));
    }
  }, [defaultPetId]);

  return (
    <Modal
      size="l"
      confirmText="Save"
      autoCloseOnConfirm={false}
      title={isEditMode ? 'Edit incident' : 'Create incident'}
      isOpen={visible}
      onClose={onClose}
      onCancel={onClose}
      onConfirm={handleSave}
      confirmButtonProps={{
        isDisabled: isUploading || isLoadingDetail,
        isLoading: handleSave.isBusy(),
      }}
    >
      <Spin isLoading={isLoadingDetail}>
        <div id="pet-incident-modal-container" className="moe-h-[560px] moe-overflow-y-scroll" ref={scrollContainerRef}>
          <Form form={form} footer={null}>
            <Form.Item
              name="petIds"
              rules={{ required: 'Please select incident pet' }}
              transformer={{
                input: () => petIds.map((value) => stringToNumberV2.input(value)),
              }}
            >
              <PetMultipleSelector isRequired label="Pet(s)" petIdList={petOptionIdList} />
            </Form.Item>
            <div className="moe-flex moe-w-full moe-gap-8px-300">
              <Form.Item name="businessId" label="Business" rules={{ required: 'Please select business' }}>
                <SingleLocationSelector scene="all" className="moe-flex-1" isRequired />
              </Form.Item>
              <Form.Item
                label="Incident type"
                name="incidentTypeId"
                rules={{ required: 'Please select incident type' }}
              >
                <Select
                  isRequired
                  className="moe-flex-1"
                  placeholder="Select incident type"
                  options={petIncidentOptions}
                />
              </Form.Item>
            </div>
            <div className="moe-flex moe-w-full moe-gap-8px-300">
              <Form.Item
                label="Date of incident"
                name="incidentDate"
                rules={{ required: 'Please select incident date' }}
              >
                <DatePicker isRequired className="moe-flex-1" format={business.dateFormat} />
              </Form.Item>
              <Form.Item
                label="Time of incident"
                name="incidentTime"
                rules={{ required: 'Please select incident time' }}
              >
                <TimePicker isRequired className="moe-flex-1" format={business.timeFormat()} />
              </Form.Item>
            </div>
            <Form.Item<boolean, string>
              name="isStaffInjured"
              label="Was a staff member injured?"
              rules={validateUtils.booleanRequiredRules}
              transformer={transformer.booleanToStringify}
            >
              <RadioGroup isRequired orientation="horizontal">
                <Radio value="false">No</Radio>
                <Radio value="true">Yes</Radio>
              </RadioGroup>
            </Form.Item>
            <Form.Item<boolean, string>
              name="isPetInjured"
              label="Was any pet injured?"
              rules={validateUtils.booleanRequiredRules}
              transformer={transformer.booleanToStringify}
            >
              <RadioGroup isRequired orientation="horizontal">
                <Radio value="false">No</Radio>
                <Radio value="true">Yes</Radio>
              </RadioGroup>
            </Form.Item>
            <Form.Item<boolean, string>
              name="isVetVisited"
              label="Did a vet visit occur?"
              rules={validateUtils.booleanRequiredRules}
              transformer={transformer.booleanToStringify}
            >
              <RadioGroup isRequired orientation="horizontal">
                <Radio value="false">No</Radio>
                <Radio value="true">Yes</Radio>
              </RadioGroup>
            </Form.Item>
            <Form.Item
              name="description"
              label="Incident description"
              rules={{ required: 'Please enter incident description' }}
            >
              <Input.TextArea
                isRequired
                showCount
                maxLength={MAX_DESCRIPTION_LENGTH}
                classNames={{
                  textarea: 'moe-w-[852px]',
                  placeholder: 'moe-w-[852px]',
                }}
                placeholder="Please provide a detailed description of the incident. Include key details such as what occurred, how it happened, any staff observations or feedback, and actions taken to address the situation."
              />
            </Form.Item>
            <Form.Item name="attachmentFiles" label="Attachment">
              <Upload
                isMultiple
                enableDrop
                maxCount={10}
                accept=".pdf,image/*"
                placeholderDescription="Supported format: .jpg, .png, and .pdf. Max: 10 files"
                customRequest={handleUploadFiles}
              />
            </Form.Item>
          </Form>
        </div>
      </Spin>
    </Modal>
  );
});
