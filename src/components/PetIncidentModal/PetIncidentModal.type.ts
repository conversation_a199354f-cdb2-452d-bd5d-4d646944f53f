import { type BusinessPetIncidentReportCreateDef } from '@moego/api-web/moego/models/business_customer/v1/business_pet_incident_report_defs';
import { type UploadItem } from '@moego/ui';
import type dayjs from 'dayjs';
export interface PetIncidentModalProps {
  visible: boolean;
  incidentId?: string;
  defaultPetIdList?: string[];
  onClose: () => void;
}

export type PetIncidentModalForm = Omit<BusinessPetIncidentReportCreateDef, 'incidentTime' | 'attachmentFiles'> & {
  incidentDate: dayjs.Dayjs;
  incidentTime: dayjs.Dayjs;
  attachmentFiles: UploadItem[];
};
