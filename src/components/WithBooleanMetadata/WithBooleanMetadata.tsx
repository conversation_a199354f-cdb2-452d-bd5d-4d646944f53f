import React, { memo, type ReactElement } from 'react';
import { type MetaData<PERSON><PERSON> } from '../../store/metadata/metadata.config';
import { useMetaData } from '../../store/metadata/metadata.hooks';

export interface WithMetadataProps {
  metaDataKey: MetaDataKey;
  children?: ReactElement | null | boolean;
}
export const WithBooleanMetadata = memo((props: WithMetadataProps) => {
  const [data, , loading] = useMetaData<boolean>(props.metaDataKey);
  if (loading || !data) {
    return null;
  }
  if (!props.children) {
    return null;
  }
  return <>{props.children}</>;
});
