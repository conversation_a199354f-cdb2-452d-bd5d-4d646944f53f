import React, { memo, type MemoExoticComponent, type ReactElement } from 'react';

type ComputedComponent = MemoExoticComponent<({ children }: SwitchProps) => ReactElement<any, any>> & {
  /**
   * @deprecated Do not use anymore!
   */
  Case: typeof Case;
};

/**
 * @deprecated Do not use anymore!
 */
interface SwitchProps {
  shortCircuit?: boolean;
  children: JSX.Element | JSX.Element[];
}

/**
 * @deprecated Do not use anymore!
 */
export interface CaseProps {
  if?: any;
  else?: any;
  children?: React.ReactNode;
}

const SwitchComponent = memo(({ children, shortCircuit = false }: SwitchProps) => {
  const cases = Array.isArray(children) ? children : [children];
  const assert = (child: JSX.Element) => child?.type.name === Case.name && !!child.props.if;

  const firstTruly: JSX.Element | boolean | null | undefined = shortCircuit && cases.find(assert);
  if (firstTruly) {
    return <>{firstTruly}</>;
  }

  const ifList = cases.filter(assert);

  if (ifList.length > 0) {
    return <>{ifList}</>;
  }

  const elseComponent = cases.filter((child) => child?.type.name === Case.name && !!child.props.else)[0] || null;

  return <>{elseComponent}</>;
});

/**
 * @deprecated Do not use anymore!
 */
const Case = memo(({ children }: CaseProps) => {
  return <>{children}</>;
});

/**
 * For multiple conditions
 *
 * @example
 * ```tsx
 * <Switch>
 *   <Switch.Case if={A}></Switch.Case>
 *   <Switch.Case if={B}></Switch.Case>
 *   <Switch.Case else></Switch.Case> // Notice just one else component
 * </Switch>
 * ```
 *
 * @deprecated Do not use anymore!
 */
const Switch = SwitchComponent as ComputedComponent;

Switch.Case = Case;

export { Case, Switch };
