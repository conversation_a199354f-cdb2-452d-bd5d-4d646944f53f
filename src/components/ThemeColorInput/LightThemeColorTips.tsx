import { default as chroma } from 'chroma-js';
import React, { memo, useMemo } from 'react';
import SvgObAlertnoteSvg from '../../assets/svg/ob-alertnote.svg';
import { SvgIcon } from '../Icon/Icon';

interface LightThemeColorTipsProps {
  themeColor: string;
  visible?: boolean;
}

export const LightThemeColorTips = memo<LightThemeColorTipsProps>(({ themeColor, visible = true }) => {
  const warn = useMemo(() => validateThemeColor(themeColor), [themeColor]);

  if (!warn || !visible) {
    return null;
  }
  return (
    <div className="!moe-flex !moe-mt-[16px] !moe-w-[281px]">
      <SvgIcon className="!moe-w-[20px] !moe-h-[20px] !moe-mr-[6px]" color="#D0021B" src={SvgObAlertnoteSvg} />
      <p className="!moe-text-[#D0021B]">Using a darker color is suggested for better legibility and accessibility.</p>
    </div>
  );
});

export function validateThemeColor(colorString: string) {
  if (!colorString) return null;
  const color = chroma(colorString);
  const contrastAPCA = chroma.contrastAPCA(colorString, '#fff');
  if (contrastAPCA < 49) {
    return 'Contrast is too weak';
  }
  const brightness = color.get('hsl.l');
  if (brightness > 0.9) {
    return 'Brightness is too strong';
  }
  return null;
}
