import styled from 'styled-components';

export const ThemeColorInputView = styled.div`
  .color-input-wrapper {
    label {
      display: none;
    }

    input {
      width: 230px;
      height: 32px;
      padding-left: 16px;
      padding-right: 16px;
      border-radius: 4px;
    }
  }

  .hue-picker {
    width: 281px !important;
  }

  .hue-horizontal {
    height: 12px !important;
    border-radius: 8px !important;

    & > div {
      top: -1px;

      & > div {
        height: 16px !important;
        width: 16px !important;
      }
    }
  }
`;
