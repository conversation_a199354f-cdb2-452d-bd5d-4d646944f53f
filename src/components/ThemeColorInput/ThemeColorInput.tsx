import chroma from 'chroma-js';
import React, { type ComponentType, useEffect, useMemo, useState } from 'react';
import { HuePicker } from 'react-color';
import { EditableInput, Saturation as SaturationSelector } from 'react-color/lib/components/common';
import IconColorCirclePng from '../../assets/image/color-circle.png';
import SvgPricingIconRightSvg from '../../assets/svg/pricing-icon-right.svg';
import { useLatestCallback } from '../../utils/hooks/useLatestCallback';
import { SvgIcon } from '../Icon/Icon';
import { LightThemeColorTips } from './LightThemeColorTips';
import { ThemeColorInputView } from './ThemeColorInput.style';

const PresetColors = [
  ['#F96B18', '#FF3B30', '#FF9500', '#FFCC00', '#34C7BE', '#30B0C7'],
  ['#32ADE6', '#007AFF', '#5856D6', '#AF52DE', '#FF2D55', '#A2845E'],
];

export interface HSV {
  h: number;
  s: number;
  v: number;
}

export interface HSL {
  h: number;
  s: number;
  l: number;
}

// Prevent the type bug of @types/react-color for now
const Saturation = SaturationSelector as any as ComponentType<{
  hsl: HSL;
  hsv: HSV;
  onChange: (v: HSV) => unknown;
}>;

function toHSV(c: string = '#000'): HSV {
  const [h, s, v] = chroma(c).hsv();
  return { h: isNaN(h) ? 0 : h, s, v };
}

export interface ThemeColorInputProps {
  value?: string;
  onChange?: (hex: string) => void;
}

/**
 * Input for OB Theme Color,
 * see: https://mengshikeji.feishu.cn/wiki/P96ywoisviEhfVknX35cyYi5nxg
 *
 * history:
 * 1. smart tip: /src/container/CardProcessing/stripe/CustomTipScreenModal.tsx
 * 2. OB theme color input: /src/container/OnlineBooking/modules/OnlineBookingSettings/BookingSite/components/ThemeColorModal/ThemeColorConfigModal.tsx
 */
export const ThemeColorInput: React.FC<ThemeColorInputProps> = ({ value, onChange }) => {
  // Calling chroma with empty string will cause error.
  const hex = value || '#000000';
  const [{ h, s, v }, setHSV] = useState<HSV>(() => toHSV(hex));
  const l = useMemo(() => {
    const color = chroma(h, s, v, 'hsv');
    return color.get('hsl.l');
  }, [h, s, v]);

  const [input, setInput] = useState<string>(hex);
  const valid = useMemo(() => chroma.valid(input), [input]);

  useEffect(() => {
    setHSV(toHSV(hex));
    setInput(hex);
  }, [hex]);

  const updateInput = useLatestCallback((newInput: string) => {
    setInput(newInput);
    if (chroma.valid(newInput) && hex !== newInput) {
      onChange?.(newInput);
    }
  });

  const setValue = useLatestCallback((newVal: Partial<HSV>) => {
    const hsv = { h, s, v, ...newVal };
    setHSV(hsv);
    updateInput(chroma(hsv.h, hsv.s, hsv.v, 'hsv').hex());
  });

  return (
    <ThemeColorInputView className="!moe-w-full">
      {PresetColors.map((row) => (
        <div key={row[0]} className="!moe-mb-[16px] !moe-space-x-[18px] !moe-flex !moe-justify-between">
          {row.map((color) => (
            <div
              key={color}
              className="!moe-w-[32px] !moe-h-[32px] !moe-rounded-full !moe-flex !moe-justify-center !moe-items-center !moe-cursor-pointer"
              style={{ backgroundColor: color }}
              onClick={() => updateInput(color)}
            >
              {hex?.toLowerCase() === color?.toLowerCase() ? (
                <SvgIcon src={SvgPricingIconRightSvg} color="#fff" className="!moe-w-[16px] !moe-h-[16px]" />
              ) : null}
            </div>
          ))}
        </div>
      ))}
      <hr className="!moe-border-[#E6E6E6] !moe-border-t-0" />
      <div className="!moe-flex !moe-justify-between !moe-mb-[20px] !moe-mt-[16px] color-input-wrapper">
        <img src={IconColorCirclePng} className="!moe-w-[32px] !moe-h-[32px]" />
        <EditableInput
          label="hex"
          value={valid ? hex : input}
          onChange={(color) => updateInput(color.hex)}
          style={{ input: { border: `1px solid ${hex}`, outlineColor: hex, width: '279px' } }}
        />
      </div>
      <HuePicker
        className="!moe-width-[368px]"
        color={{ h: Math.floor(h), s: 0, l: 0 }}
        onChange={(color) => setValue({ h: color.hsl.h })}
      />
      <div className="!moe-h-[167px] !moe-relative !moe-mt-[13px]">
        <Saturation hsl={{ h, s, l }} hsv={{ h, s, v }} onChange={(color) => setValue({ s: color.s, v: color.v })} />
      </div>
      <LightThemeColorTips themeColor={hex} />
    </ThemeColorInputView>
  );
};
