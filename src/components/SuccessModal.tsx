/*
 * @since 2021-07-21 19:52:44
 * <AUTHOR> <<EMAIL>>
 */

import React, { memo } from 'react';
import IconSignatureSuccessPng from '../assets/icon/signature-success.png';
import { createModalComponent } from './Modal/Modal';
import { SuccessCardView } from './SuccessModal.style';

export interface SuccessCardProps {
  desc: string;
  isCardOnFile?: boolean;
}

export const SuccessCard = memo<SuccessCardProps>(({ desc }) => (
  <SuccessCardView>
    <img className="handclap" src={IconSignatureSuccessPng} alt="" />
    <p className="desc !moe-mb-[70px]">{desc}</p>
  </SuccessCardView>
));

export const SuccessModal = createModalComponent(SuccessCard, { width: '1200px' });
