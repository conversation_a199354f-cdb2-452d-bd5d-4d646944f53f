/**
 * @since 2023-12-29
 * <AUTHOR>
 * @description display business name with businessId, if businessId is 'current', display current business name
 */
import { Tooltip, cn } from '@moego/ui';
import { useSelector } from 'amos';
import React, { type ReactNode } from 'react';
import { currentBusinessIdBox } from '../store/business/business.boxes';
import { locationMapBox } from '../store/business/location.boxes';

interface BusinessNameProps {
  businessId: number | string | undefined;
  className?: string;
  prefix?: ReactNode;
  suffix?: ReactNode;
  tooltip?: boolean;
}

export const BusinessName = (props: BusinessNameProps) => {
  const { businessId, className, prefix, suffix, tooltip } = props;
  const [locationMap, currentBizId] = useSelector(locationMapBox, currentBusinessIdBox);
  const bizId = businessId === 'current' ? currentBizId : businessId;
  const bizName = locationMap.mustGetItem(bizId ? bizId + '' : '')?.name;
  if (!bizName) {
    return null;
  }

  return (
    <Tooltip content={tooltip ? bizName : null}>
      <div className={cn('moe-text-s moe-font-medium moe-text-tertiary', className)}>
        {prefix}
        {bizName}
        {suffix}
      </div>
    </Tooltip>
  );
};
