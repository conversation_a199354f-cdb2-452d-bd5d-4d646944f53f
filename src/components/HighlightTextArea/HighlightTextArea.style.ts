import styled from 'styled-components';

export const TextAreaWrapper = styled.div`
  position: relative;
  z-index: 1;
  border-radius: 2px;
  font-size: 16px;
  line-height: 1.5;
  border: 1px solid rgb(201, 213, 225);
`;

export const TextArea = styled.textarea`
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 1;
  padding: 10px;
  border: 0;
  resize: none;
  font-size: 16px;
  line-height: 1.5;
  color: transparent;
  background: transparent;
  white-space: pre-wrap;
  caret-color: black;
  border: none;

  &:focus {
    outline: none;
  }
`;

export const Highlight = styled.pre`
  position: absolute;
  font-family: inherit;
  font-weight: normal;
  width: 100%;
  height: 100%;
  overflow: auto;
  z-index: 0;
  padding: 10px;
  border: 0;
  tab-size: 4;
  hyphens: none;
  white-space: pre-wrap;

  & > mark {
    color: rgb(241, 90, 43);
    padding: 0;
    margin: 0;
    background: transparent;
  }

  &::-webkit-scrollbar {
    height: 0;
    width: 0;
    color: transparent;
  }
`;
