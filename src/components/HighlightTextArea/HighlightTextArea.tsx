import React, { forwardRef, useCallback, useEffect, useMemo, useRef } from 'react';
import { useMergeRef } from '../../utils/react';
import { Highlight, TextArea, TextAreaWrapper } from './HighlightTextArea.style';

export interface IHighlightTextAreaProps {
  style?: React.CSSProperties;
  height?: number | string;
  placeholder?: string;
  /** 高亮规则 */
  markRules?: RegExp[];
  value?: string;
  onChange?: (value?: string) => void;
  autoFocus?: boolean;
}

export const HighlightTextArea = forwardRef(function TextAreaWithTag(
  props: IHighlightTextAreaProps,
  ref: React.Ref<HTMLTextAreaElement>,
) {
  const { style, height = 240, placeholder, markRules, value, onChange, autoFocus } = props;
  const refTextArea = useRef<HTMLTextAreaElement>(null);
  const refPre = useRef<HTMLPreElement>(null);
  const mergedTextRef = useMergeRef(ref, refTextArea);
  const scrollSync = useCallback((e) => {
    const { target: element } = e;
    refPre.current!.scrollTop = element.scrollTop;
    refPre.current!.scrollLeft = element.scrollLeft;
  }, []);
  const prettierValue = useMemo(() => {
    let html = markRules?.reduce((preStr, next) => preStr.replace(next, '<mark>$&</mark>'), value ?? '') ?? value ?? '';
    if (html[html.length - 1] == '\n') {
      // If the last character is a newline character
      // Add a placeholder space character to the final line
      html += '\u{20}';
    }
    return html;
  }, [value, markRules]);

  useEffect(() => {
    scrollSync({ target: refTextArea.current! });
  }, [value, scrollSync]);

  useEffect(() => {
    if (autoFocus && refTextArea.current) {
      const len = refTextArea.current.value.length;
      refTextArea.current.setSelectionRange(len, len);
      refTextArea.current.focus();
    }
  }, [autoFocus]);

  return (
    <TextAreaWrapper style={{ height, ...style }}>
      <TextArea
        ref={mergedTextRef}
        value={value}
        placeholder={placeholder}
        onChange={(e) => {
          if (onChange) {
            onChange(e.target.value);
          }
        }}
        onScroll={scrollSync}
      />
      <Highlight ref={refPre} dangerouslySetInnerHTML={{ __html: prettierValue }} aria-hidden="true" />
    </TextAreaWrapper>
  );
});
