import { ColorPicker, Form, Input, Modal, useForm } from '@moego/ui';
import React from 'react';
import { useModal } from '../../../../../Modal/useModal';

interface PetCodeFormFields {
  color: string;
  codeNumber: string;
  description: string;
}

interface PetCodeFormModalProps extends React.ComponentProps<typeof Modal> {
  onSubmitSuccess: (formValues: PetCodeFormFields) => void | Promise<void>;
}

function PetCodeFormModal({ onSubmitSuccess, onClose, ...rest }: PetCodeFormModalProps) {
  const form = useForm<PetCodeFormFields>();

  const handleClose = () => {
    form.reset();
    onClose?.();
  };

  const handleConfirm = () => {
    return form.handleSubmit(async (input) => {
      await onSubmitSuccess(input);
      handleClose();
    })();
  };

  return (
    <Modal
      isOpen
      isMaskCloseable={false}
      className="moe-w-[540px]"
      title="Add pet code"
      confirmText="Add"
      {...rest}
      onClose={handleClose}
      autoCloseOnConfirm={false}
      onConfirm={handleConfirm}
    >
      <Form form={form} footer={null}>
        <Form.Item name="color" label="Color code">
          <ColorPicker defaultValue="#5e72e4" />
        </Form.Item>
        <Form.Item name="codeNumber" label="Title" rules={{ required: true }}>
          <Input isRequired maxLength={3} autoComplete="off" description="Maximum 3 letters / numbers" />
        </Form.Item>
        <Form.Item name="description" label="Description" rules={{ required: true }}>
          <Input isRequired maxLength={20} autoComplete="off" />
        </Form.Item>
      </Form>
    </Modal>
  );
}

export function usePetCodeFormModal() {
  return useModal(PetCodeFormModal);
}
