import { FNK_PaymentMethodTypeEnum } from '@moego/finance-web-kit';
import { Modal, Spin, toast } from '@moego/ui';
import { useDispatch } from 'amos';
import { toString } from 'lodash';
import React, { forwardRef, memo, useImperativeHandle, useRef } from 'react';
import MoegoPayBadgeDark from '../../../assets/icon/moego-pay-badge-dark.svg';
import { PAY_FAILED_CODE } from '../../../container/Account/Pucharse/utils';
import { useAddCardForm } from '../../../container/PaymentFlow/shared/useAddCardForm';
import { useFloatableHost } from '../../../utils/hooks/useFloatableHost';
import { useLatestCallback } from '../../../utils/hooks/useLatestCallback';
import { usePaymentVersionInfo } from '../../../utils/hooks/usePaymentVersionInfo';
import { useSerialCallback } from '../../../utils/hooks/useSerialCallback';
import { getApiErrorMessage } from '../../../utils/utils';
import { toastApi } from '../../Toast/Toast';
import { openGlobalModal } from '../../globals/GlobalModals.store';
import { useStripeAddCardModal } from './StripeAddCardModal';
import { queryRecurringPaymentMethod } from '../../../query/payment/recurringPaymentMethod';
import { usePaymentAction } from '@moego/finance-ui';
import { FinanceKit } from '../../../service/finance-kit';

export interface MGPAddCardModalProps extends MGPAddCardFormProps {
  visible?: boolean;
  hideRequestFromClient?: boolean;
  onClose: () => void;
}

export const MGPAddCardModal = memo<MGPAddCardModalProps>(({ onClose, clientId, hideRequestFromClient }) => {
  const form = useRef<MGPAddFormRef>(null);
  const dispatch = useDispatch();

  return (
    <Modal
      isOpen
      isMaskCloseable={false}
      showTertiaryButton={!hideRequestFromClient}
      className="moe-w-[540px]"
      title={
        <>
          Add card on file
          <img width="84px" height="28px" src={MoegoPayBadgeDark} />
        </>
      }
      autoCloseOnConfirm={false}
      confirmText="Add"
      tertiaryText="Request from client"
      onTertiary={() => {
        onClose();
        dispatch(
          openGlobalModal({
            requestCof: {
              customerId: clientId,
            },
          }),
        );
      }}
      onClose={onClose}
      onConfirm={async () => {
        await form.current?.submit(onClose);
      }}
    >
      <MGPAddCardForm ref={form} clientId={clientId} />
    </Modal>
  );
});

export interface MGPAddFormRef {
  submit: (onSave?: () => void, throwError?: boolean) => Promise<void>;
}

interface MGPAddCardFormProps {
  clientId: number;
}

export const MGPAddCardForm = forwardRef<MGPAddFormRef, MGPAddCardFormProps>(
  ({ clientId }: { clientId: number }, ref) => {
    const { mutateAsync: addRecurringPaymentMethod } = queryRecurringPaymentMethod.add.useMutation();
    const { render, submit, isLoading } = useAddCardForm();
    const handleAction = usePaymentAction(FinanceKit);

    const handleSubmit = useSerialCallback(async (onSave?: () => void, throwError?: boolean) => {
      if (isLoading) {
        if (throwError) {
          throw new Error('Add card form not ready');
        }
        return;
      }
      let token, cardName;
      try {
        const result = await submit();
        if (!result) throw new Error('Card is not valid');
        token = result.token;
        cardName = result.cardName;
      } catch (error) {
        if (throwError) {
          throw error;
        }
        const message = (error as Error)?.message || toString(error);
        toast({ type: 'error', title: message });
        return;
      }

      try {
        const { channelResponse } = await addRecurringPaymentMethod({
          customerId: `${clientId}`,
          paymentMethodType: FNK_PaymentMethodTypeEnum.Card,
          detail: { card: { ...token, saveCard: true } },
          extra: { card: { alias: cardName } },
          _options: { autoToast: false },
        });
        if (channelResponse) {
          await handleAction(channelResponse);
          queryRecurringPaymentMethod.list.invalidateQueries({ customerId: clientId.toString() });
        }
      } catch (e) {
        let message;
        if ((e as any)?.data?.code === PAY_FAILED_CODE) {
          message = 'Card authentication failed.';
        } else {
          message = getApiErrorMessage(e as any);
        }
        toastApi.error(message);
        throw e;
      }
      onSave?.();
    });

    useImperativeHandle(ref, () => ({
      submit: handleSubmit,
    }));

    return (
      <>
        <Spin classNames={{ base: 'moe-w-full moe-h-full', container: 'moe-w-full moe-h-full' }} isLoading={isLoading}>
          {render()}
        </Spin>
        {/* TODO（Jim）：Payment v2 不支持 pre-auth */}
        {/* <PreAuthOnboarding
          mode="MoeGoPay"
          name="AddCard"
          className="!moe-mt-m !moe-mb-0"
          classNames={{
            hint: '!moe-font-manrope !moe-text-base-24 data-[highlight=true]:moe-text-primary data-[highlight=false]:moe-text-tertiary',
            icon: '!moe-text-[21px] moe-mt-[1.5px]',
          }}
          isFromPreAuthOpen={false}
        /> */}
      </>
    );
  },
);

export const useMGPAddCardModal = (clientId: number) => {
  const { mountModal } = useFloatableHost();
  const { isLoading: isPaymentVersionLoading, isPaymentV2 } = usePaymentVersionInfo();
  const openStripeModal = useStripeAddCardModal(clientId);

  return useLatestCallback(async (props?: Omit<MGPAddCardModalProps, 'clientId' | 'onClose'>) => {
    if (isPaymentVersionLoading) {
      return;
    }

    if (isPaymentV2) {
      const { promise, closeFloatable: closeModal } = mountModal(
        <MGPAddCardModal {...props} clientId={clientId} onClose={() => closeModal()} />,
      );
      return promise;
    }

    return openStripeModal();
  });
};
