import { Button, Heading, cn } from '@moego/ui';
import React from 'react';
import { Condition } from '../../Condition';

interface BlockContainerProps {
  title: React.ReactNode;
  titleSize?: Extract<React.ComponentProps<typeof Heading>['size'], '3' | '4'>;
  /**
   * when action is provided, actionText and onAction will be ignored
   */
  action?: React.ReactNode;
  actionText?: string;
  onAction?: () => void;
  children: React.ReactNode;
  id?: string;
  className?: string;
  classNames?: {
    base?: string;
    header?: string;
  };
  headerClassName?: string;
}

export const BlockContainer = ({
  children,
  title,
  titleSize = '3',
  action,
  actionText,
  onAction,
  id,
  className,
  classNames,
}: BlockContainerProps) => {
  return (
    <div
      data-slot="block-container"
      id={id}
      className={cn(
        'moe-flex moe-flex-col',
        {
          'moe-mb-xl': titleSize === '3',
          'moe-mb-m': titleSize === '4',
          'last:moe-mb-0': titleSize === '4',
        },
        classNames?.base,
        className,
      )}
    >
      <Condition if={title || onAction}>
        <div
          className={cn(
            'moe-flex moe-justify-between moe-items-center',
            {
              'moe-mb-m': titleSize === '3',
              'moe-mb-s': titleSize === '4',
            },
            classNames?.header,
          )}
        >
          <Heading size={titleSize}>{title}</Heading>
          <Condition if={action}>{action}</Condition>
          <Condition if={!action && actionText && onAction}>
            <Button
              align="end"
              className={cn('moe-min-w-0', {
                'moe-my-[-6px]': titleSize === '3',
                'moe-my-[-8px]': titleSize === '4',
              })}
              variant="tertiary-legacy"
              onPress={onAction}
            >
              {actionText}
            </Button>
          </Condition>
        </div>
      </Condition>

      {children}
    </div>
  );
};
