import { type AddCardFormRef, ComponentType, useKitComponents } from '@moego/finance-ui';
import { Modal, Spin } from '@moego/ui';
import { Elements } from '@stripe/react-stripe-js';
import { useDispatch, useSelector } from 'amos';
import React, { forwardRef, memo, useImperativeHandle, useRef } from 'react';
import MoegoPayBadgeDark from '../../../assets/icon/moego-pay-badge-dark.svg';
import { PAY_FAILED_CODE } from '../../../container/Account/Pucharse/utils';
import { FinanceKit } from '../../../service/finance-kit';
import { customerMapBox } from '../../../store/customer/customer.boxes';
import { addStripeCustomer } from '../../../store/stripe/actions/public/stripe.actions';
import { stripeCustomerMapBox } from '../../../store/stripe/stripe.boxes';
import { useFloatableHost } from '../../../utils/hooks/useFloatableHost';
import { useLatestCallback } from '../../../utils/hooks/useLatestCallback';
import { useSerialCallback } from '../../../utils/hooks/useSerialCallback';
import { getApiErrorMessage } from '../../../utils/utils';
import { getSystemStripe } from '../../CardStripe/CardStripe';
import { toastApi } from '../../Toast/Toast';
import { openGlobalModal } from '../../globals/GlobalModals.store';
import { queryStripePaymentMethod } from '../../../query/payment/stripePaymentMethod';

export interface StripeAddCardModalProps extends StripeAddCardFormProps {
  visible?: boolean;
  hideRequestFromClient?: boolean;
  onClose: () => void;
}

export const StripeAddCardModal = memo<StripeAddCardModalProps>(({ onClose, clientId, hideRequestFromClient }) => {
  const form = useRef<StripeAddFormRef>(null);
  const dispatch = useDispatch();

  return (
    <Modal
      isOpen
      isMaskCloseable={false}
      showTertiaryButton={!hideRequestFromClient}
      className="moe-w-[540px]"
      title={
        <>
          Add card on file
          <img width="84px" height="28px" src={MoegoPayBadgeDark} />
        </>
      }
      autoCloseOnConfirm={false}
      confirmText="Add"
      tertiaryText="Request from client"
      onTertiary={() => {
        onClose();
        dispatch(
          openGlobalModal({
            requestCof: {
              customerId: clientId,
            },
          }),
        );
      }}
      onClose={onClose}
      onConfirm={async () => {
        await form.current?.submit(onClose);
      }}
    >
      <StripeAddCardForm ref={form} clientId={clientId} />
    </Modal>
  );
});

export interface StripeAddFormRef {
  submit: (onSave?: () => void, throwError?: boolean) => Promise<void>;
}

interface StripeAddCardFormProps {
  clientId: number;
}

export const StripeAddCardForm = forwardRef<StripeAddFormRef, StripeAddCardFormProps>(
  ({ clientId }: { clientId: number }, ref) => {
    const dispatch = useDispatch();
    const addCardFormRef = useRef<AddCardFormRef>(null);
    const [stripeCustomer, customer] = useSelector(
      stripeCustomerMapBox.get(clientId),
      customerMapBox.mustGetItem(clientId),
    );
    const { mutateAsync: createCard } = queryStripePaymentMethod.create.useMutation();
    const { isLoading, Components } = useKitComponents(FinanceKit, ComponentType.Card);
    const AddCardForm = Components?.AddCardForm;

    const handleSubmit = useSerialCallback(async (onSave?: () => void, throwError?: boolean) => {
      if (!addCardFormRef.current) {
        if (throwError) {
          throw new Error('Add card form not ready');
        }
        return;
      }
      const result = await addCardFormRef.current.getCardInfo();
      if (!result) {
        if (throwError) {
          throw new Error('no card info');
        }
        return;
      }
      const { tokenError, tokenId } = result;
      if (!tokenId) {
        toastApi.error(tokenError || 'Unknown error');
        if (tokenError) {
          addCardFormRef.current.updateErrorHint(tokenError);
        }
        if (throwError) {
          throw new Error(tokenError || 'Unknown error');
        }
        return;
      }
      try {
        if (!stripeCustomer) {
          await dispatch(addStripeCustomer(clientId, tokenId, customer.fullName(), { autoToast: false }));
        } else {
          await createCard({
            customerId: clientId,
            chargeToken: tokenId,
            options: { autoToast: false },
          });
        }
      } catch (e) {
        let message;
        if ((e as any)?.data?.code === PAY_FAILED_CODE) {
          message = 'Card authentication failed.';
          addCardFormRef.current.updateErrorHint('Failed to authenticate. Please check or try another card.');
        } else {
          message = getApiErrorMessage(e as any);
        }
        toastApi.error(message);
        throw e;
      }
      onSave?.();
    });

    useImperativeHandle(ref, () => ({
      submit: handleSubmit,
    }));

    return (
      <>
        <Spin classNames={{ base: 'moe-w-full moe-h-full', container: 'moe-w-full moe-h-full' }} isLoading={isLoading}>
          {AddCardForm ? (
            <AddCardForm
              classNames={{
                nameInput: 'moe-mt-none',
                cardNumberLabel: 'moe-mt-s',
              }}
              ref={addCardFormRef}
            />
          ) : null}
        </Spin>
      </>
    );
  },
);

/**
 * @deprecated use `useMGPAddCardModal` instead
 */
export const useStripeAddCardModal = (clientId: number) => {
  const { mountModal } = useFloatableHost();

  return useLatestCallback((props?: Omit<StripeAddCardModalProps, 'clientId' | 'onClose'>) => {
    const { promise, closeFloatable: closeModal } = mountModal(
      <Elements stripe={getSystemStripe()} options={{ locale: 'en' }}>
        <StripeAddCardModal {...props} clientId={clientId} onClose={() => closeModal()} />
      </Elements>,
    );

    return promise;
  });
};
