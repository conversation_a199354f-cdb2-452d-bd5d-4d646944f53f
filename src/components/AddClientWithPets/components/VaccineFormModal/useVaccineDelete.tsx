import { AlertDialog } from '@moego/ui';
import { useDispatch } from 'amos';
import { removeVaccineBinding } from '../../../../store/pet/petVaccineBinding.actions';
import { useLatestCallback } from '../../../../utils/hooks/useLatestCallback';

export function useVaccineDelete() {
  const dispatch = useDispatch();

  return useLatestCallback((vaccineBindingId: number, { onSuccess }: { onSuccess?: () => void } = {}) => {
    AlertDialog.open({
      className: 'moe-w-[480px]',
      title: 'Delete this vaccine',
      confirmText: 'Delete',
      onConfirm: async () => dispatch(removeVaccineBinding(vaccineBindingId)).then(onSuccess),
      content: 'Are you sure to delete this vaccine?',
    });
  });
}
