import { cn } from '@moego/ui';
import React, { memo } from 'react';

export interface BaseProps {
  className?: string;
  onClick?: React.EventHandler<React.MouseEvent>;
  children?: React.ReactNode;
}

export interface BaseCardProps extends BaseProps {
  /** flex direction vertical */
  vertical?: boolean;
}

export const BaseCard = memo<BaseCardProps>(({ className, vertical, ...props }) => {
  return (
    <div
      data-slot="base-card"
      className={cn(
        'moe-group moe-flex moe-relative moe-cursor-pointer moe-gap-s moe-border moe-border-button moe-rounded-m moe-p-s moe-bg-neutral-default hover:moe-bg-neutral-sunken-0',
        {
          'moe-flex-row moe-justify-between moe-items-center': !vertical,
          'moe-flex-col': vertical,
        },
        className,
      )}
      {...props}
    ></div>
  );
});

export const IconGroup = ({ className, ...props }: BaseProps) => {
  return (
    <div
      data-slot="icon-group"
      className={cn('moe-flex moe-flex-shrink-0 moe-items-center moe-gap-xs', className)}
      {...props}
    ></div>
  );
};

/**
 * icon group in card , show when group hover
 */
export const CardIconGroup = ({ className, ...props }: BaseProps) => {
  return (
    <IconGroup
      data-slot="card-icon-group"
      className={cn('moe-invisible group-hover:moe-visible', className)}
      {...props}
    ></IconGroup>
  );
};

export interface AlignCardIconGroupProps extends BaseProps {
  align?: 'top-right' | 'bottom-right';
}

export const AlignCardIconGroup = ({ className, align, ...props }: AlignCardIconGroupProps) => {
  return (
    <CardIconGroup
      data-slot="card-align-icon-group"
      className={cn(
        'moe-absolute moe-right-s',
        {
          'moe-top-xs': align === 'top-right',
          'moe-bottom-xs': align === 'bottom-right',
        },
        className,
      )}
      {...props}
    ></CardIconGroup>
  );
};
