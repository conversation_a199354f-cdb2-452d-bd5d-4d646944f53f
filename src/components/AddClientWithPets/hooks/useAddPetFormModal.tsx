import { useLatestCallback } from '../../../utils/hooks/useLatestCallback';
import { usePetFormModal } from '../components/PetFormModal/usePetFormModal';

/**
 * 用回调函数的方式来添加pet
 * 使用：
 * const go2AddPet = useAddPetFormModal(clientId);
 * const petId = await go2AddPet(); // 返回的petId就是弹窗新增pet后的Id
 */
export function useAddPetFormModal(clientId: number) {
  const open = usePetFormModal();
  const go2AddPet = useLatestCallback(() => {
    return new Promise<number>((resolve) => {
      open({ clientId, onAdded: resolve });
    });
  });

  return go2AddPet;
}
