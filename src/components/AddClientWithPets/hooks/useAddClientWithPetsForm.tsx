import { type AddCardFormRef, ComponentType, type MGPToken, useKitComponents } from '@moego/finance-ui';
import { MajorPlusOutlined } from '@moego/icons-react';
import {
  Button,
  Checkbox,
  CheckboxGroup,
  Form,
  Input,
  LegacySelect as Select,
  Text,
  Typography,
  type UseFormHandleSubmit,
  type UseFormReset,
  type UseFormSetValue,
  useForm,
  yupResolver,
  Alert,
  useWatch,
} from '@moego/ui';
import { Elements } from '@stripe/react-stripe-js';
import { useDispatch, useSelector } from 'amos';
import { default as classNames, default as cn } from 'classnames';
import { omit, pick } from 'lodash';
import React, { useMemo, useRef, useState } from 'react';
import { useDebounce, useMount, useUpdate } from 'react-use';
import * as Yup from 'yup';
import MoegoPayBadgeDark from '../../../assets/icon/moego-pay-badge-dark.svg';
import SvgIconInfoSvg from '../../../assets/svg/icon-info.svg';
import { useAddCardForm } from '../../../container/PaymentFlow/shared/useAddCardForm';
import { FinanceKit } from '../../../service/finance-kit';
import { useHasBrandedApp } from '../../../store/branded/branded.hooks';
import { getReferralSourceList } from '../../../store/business/referralSource.actions';
import { referralSourceMapBox } from '../../../store/business/referralSource.boxes';
import { selectBusinessReferralSources } from '../../../store/business/referralSource.selectors';
import { AutoMessageKinds, UnconfirmedReminderKinds } from '../../../store/customer/customer.boxes';
import { getPetOptions } from '../../../store/pet/pet.actions';
import { filterNilValues } from '../../../utils/filterNilValues';
import { useBool } from '../../../utils/hooks/useBool';
import { useIsMoeGoPayReady } from '../../../utils/hooks/useIsMoeGoPay';
import { useLatestCallback } from '../../../utils/hooks/useLatestCallback';
import { usePaymentVersionInfo } from '../../../utils/hooks/usePaymentVersionInfo';
import { stripNonNumericInput } from '../../../utils/number';
import {
  AddressForm,
  type AddressFormFields,
  type AddressFormRef,
  addressFormKeys,
  isAddressFormField,
} from '../../AddressForm/AddressFormV2';
import { SingleLocationSelector } from '../../Business/SingleLocationSelector';
import { getSystemStripe } from '../../CardStripe/CardStripe';
import { Condition } from '../../Condition';
import { SvgIcon } from '../../Icon/Icon';
import { Switch } from '../../SwitchCase';
import { AvatarUpload } from '../../Upload/AvatarUpload';
import { PREFERRED_BUSINESS_LABEL, WithMultiLocation } from '../../WithFeature/WithMultiLocation';
import { RE_INPUT_PHONE } from '../../form/NumberInput';
import { NumberInputV2 } from '../../form/NumberInputV2';
import { transformers } from '../../form/transformers';
import { BlockContainer } from '../components/BlockContainer';
import { PetForm, type PetFormRef } from '../components/PetFormModal/PetForm';
import { type PetFormFields } from '../components/PetFormModal/usePetForm';
import { Pickup } from '../components/Pickup';
import { OptionalArea } from '../components/OptionalArea';
import { checkPhoneNumberExistedWithLead } from '../../../store/leads/leads.actions';
import { useFeatureIsOn } from '@growthbook/growthbook-react';
import { GrowthBookFeatureList } from '../../../utils/growthBook/growthBook.config';
import { useModal } from '../../Modal/useModal';
import { ExistedLeadModal } from '../components/ExistedLeadModal';
import { PATH_LEADS_DETAIL } from '../../../router/paths';
import type { Customer } from '@moego/bff-openapi/clients/client.leads';
import { selectCurrentBusiness } from '../../../store/business/business.selectors';
import { openWindow } from '../../../utils/utils';
import { type AnchorLinkBaseProps } from '@moego/ui/dist/esm/components/Anchor/AnchorLink';

const clientValidateSchema = Yup.object().shape({
  avatarPath: Yup.string(),
  firstName: Yup.string().required('First name is required'),
  lastName: Yup.string().required('Last name is required'),
  phoneNumber: Yup.string().matches(RE_INPUT_PHONE, 'Invalid phone number'),
  email: Yup.string().email('Invalid email'),
  preferredBusinessId: Yup.string().required('Preferred business is required'),
  autoMessage: Yup.array().of(Yup.number().required()).default([AutoMessageKinds.Message]),
  apptReminderByList: Yup.array().of(Yup.number().required()).default([UnconfirmedReminderKinds.Message]),
  isUnsubscribed: Yup.number().default(0),
  referralSourceId: Yup.string().nullable(),
  emergencyContact: Yup.object()
    .shape({
      firstName: Yup.string(),
      lastName: Yup.string(),
      phoneNumber: Yup.string().test(
        'unique-emergency-phone',
        'Emergency contact phone number cannot be the same as client phone number',
        function (value) {
          if (!value?.trim()) return true;
          const clientPhone = this.from![1].value.phoneNumber;
          return value !== clientPhone;
        },
      ),
    })
    .notRequired()
    .default(undefined),
  pickupContact: Yup.object()
    .shape({
      firstName: Yup.string(),
      lastName: Yup.string(),
      phoneNumber: Yup.string().test(
        'unique-pickup-phone',
        'Pickup contact phone number cannot be the same as client or emergency contact phone number',
        function (value) {
          if (!value?.trim()) return true;
          const clientPhone = this.from![1].value.phoneNumber;
          const emergencyPhone = this.from![1].value.emergencyContact?.phoneNumber;
          return value !== clientPhone && value !== emergencyPhone;
        },
      ),
    })
    .notRequired()
    .default(undefined),
});

const clientInfoFormKeys = Object.keys(clientValidateSchema.fields);

const resolver = yupResolver(clientValidateSchema);

type ClientBaseFields = Yup.InferType<typeof clientValidateSchema> & AddressFormFields;

export type ClientWithPetsFields = ClientBaseFields & {
  petList: PetFormFields[];
  card?: { cardName: string; token: MGPToken };
  chargeToken?: string;
};

export type DefaultClientWithPetsFields = Partial<Omit<ClientWithPetsFields, 'petList'>> & {
  petList?: Partial<PetFormFields>[];
};

interface AddClientWithPetsFormProps {
  lessMode?: boolean;
  defaultValues?: DefaultClientWithPetsFields;
}

interface AnchorItemType extends AnchorLinkBaseProps {
  id: string;
}

export function useAnchorItems() {
  const isMoeGoPayReady = useIsMoeGoPayReady();

  const anchorItemMap = useMemo(() => {
    const anchorItemMap = {
      clientInfo: { id: 'form-clientInfo', href: `#form-clientInfo`, title: 'Client info', key: 'clientInfo' },
      address: { id: 'form-address', href: `#form-address`, title: 'Address', key: 'address' },
      pet: { id: 'form-pet', href: `#form-pet`, title: 'Pet', key: 'pet' },
      cof: isMoeGoPayReady ? { id: 'cof', href: '#cof', title: 'Card on file', key: 'cof' } : undefined,
      preference: { id: 'form-preference', href: `#form-preference`, title: 'Preference', key: 'preference' },
    } satisfies Record<string, AnchorItemType | undefined>;
    return anchorItemMap;
  }, [isMoeGoPayReady]);

  const anchorItems = useMemo(() => Object.values(anchorItemMap).filter(Boolean) as AnchorItemType[], [anchorItemMap]);

  return {
    anchorItems,
    anchorItemMap,
  };
}

export const useAddClientWithPetsForm = ({ lessMode = true, defaultValues }: AddClientWithPetsFormProps) => {
  const update = useUpdate();
  const dispatch = useDispatch();
  const petRefs = useRef<Record<string, PetFormRef | null>>(
    generatePetRefsPlaceholder(defaultValues?.petList?.length || 1),
  );
  const addressRef = useRef<AddressFormRef>(null);
  const { anchorItemMap } = useAnchorItems();
  const { has: hasBrandedApp } = useHasBrandedApp();
  const { isLoading: isPaymentVersionLoading, isPaymentV2 } = usePaymentVersionInfo();
  // TODO（Sam）：全量切到 V2 后，重写这里的 card 渲染代码逻辑。
  // v1
  const addCardRef = useRef<AddCardFormRef>(null);
  const { isLoading: isStripeAddCardFormLoading, Components } = useKitComponents(FinanceKit, ComponentType.Card);
  const StripeAddCardForm = Components?.AddCardForm;
  // v2
  const {
    isLoading: isAddCardFormLoading,
    submit: submitCardForm,
    render: renderCardForm,
  } = useAddCardForm({ isRequired: false, incompleteErrorHint: 'Card info is incomplete. Card will not be saved.' });

  const isCardLoading = isPaymentVersionLoading || isStripeAddCardFormLoading || isAddCardFormLoading;
  const cardDom = isPaymentV2 ? (
    renderCardForm()
  ) : (
    <Elements stripe={getSystemStripe()} options={{ locale: 'en' }}>
      {StripeAddCardForm && (
        <StripeAddCardForm
          ref={addCardRef}
          classNames={{ nameInput: 'moe-mt-none', cardNumberLabel: 'moe-mt-s' }}
          isRequired={false}
          incompletePrompt="Card info is incomplete. Card will not be saved."
        />
      )}
    </Elements>
  );

  const [referralSourcesList, referralSource, business] = useSelector(
    selectBusinessReferralSources(),
    referralSourceMapBox,
    selectCurrentBusiness(),
  );

  // for Alert
  const [phoneNumberExistedInfo, setPhoneNumberExistedInfo] = useState<{
    isExisted: boolean;
    customer: Customer | null;
  } | null>(null);
  const enableLeadManagement = useFeatureIsOn(GrowthBookFeatureList.EnableLeadManagement);
  const openExistedLeadModal = useModal(ExistedLeadModal);

  // default values 仅消费 mount 时的值，不需要 deps
  const petDefaultValuesMap = useMemo(() => {
    const petRefIds = Object.keys(petRefs.current);
    return defaultValues?.petList?.reduce(
      (acc, values, index) => {
        acc[petRefIds[index]] = values;
        return acc;
      },
      {} as Record<string, Partial<PetFormFields>>,
    );
  }, []);
  const addressDefaultValues = useMemo(() => pick(defaultValues, addressFormKeys), []);
  const clientDefaultValues = useMemo(
    () => ({ ...clientValidateSchema.getDefault(), ...pick(defaultValues, clientInfoFormKeys) }),
    [],
  );

  const isAddressDefaultNotEmpty = Object.values(addressDefaultValues).some(Boolean);
  const addressVisibleStatus = useBool(isAddressDefaultNotEmpty);

  const clientForm = useForm({
    resolver,
    defaultValues: filterNilValues(clientDefaultValues) as Yup.InferType<typeof clientValidateSchema>,
    mode: 'onBlur',
  });
  const { isDirty: clientFormIsDirty } = clientForm.formState;

  const getIsDirty = useLatestCallback(() => {
    return (
      clientFormIsDirty ||
      addressRef.current?.form.formState.isDirty ||
      Object.values(petRefs.current).some((ref) => ref?.form.formState.isDirty)
    );
  });

  const buildSetPetRef = (key: string) => (ref: PetFormRef) => {
    if (key in petRefs.current) {
      petRefs.current[key] = ref;
    }
  };

  useMount(() => {
    dispatch([getPetOptions(), getReferralSourceList()]);
  });

  const handleAddPet = () => {
    petRefs.current = { ...petRefs.current, ...generatePetRefsPlaceholder() };
    update();
  };

  const handleRemovePet = (_id: string) => {
    petRefs.current = omit(petRefs.current, _id);
    update();
  };

  const goToLeadDetail = (id: string) => {
    openWindow(PATH_LEADS_DETAIL.build({ id }));
  };

  // 增加 check exist lead 逻辑
  const checkExistedLeadWhenSubmit = async (values: ClientWithPetsFields) => {
    const phoneNumber = values.phoneNumber;
    // 暂且白名单
    if (!phoneNumber || !enableLeadManagement) return;
    const { isExisted, customer } = await dispatch(checkPhoneNumberExistedWithLead({ phoneNumber }));
    setPhoneNumberExistedInfo({ isExisted, customer });
    if (!isExisted) return false;
    // open Modal
    openExistedLeadModal({
      customer,
      onConfirm: () => {
        goToLeadDetail(customer.id);
      },
    });
    return true;
  };

  const handleSubmit = useLatestCallback<UseFormHandleSubmit<ClientWithPetsFields>>((onValid, onInvalid) => {
    return async (e?: React.BaseSyntheticEvent) => {
      const petList = [] as PetFormFields[];
      const values = { petList } as ClientWithPetsFields;
      const errors = {};
      const _onValid = (payload: any) => (data: any) => Object.assign(payload, data);
      const _onInvalid = (data: any) => Object.assign(errors, data);

      // submit all forms, collect values and errors
      const petListResult = Object.values(petRefs.current)
        .filter(Boolean)
        .map((ref) => {
          const payload = {} as PetFormFields;
          petList.push(payload);

          return ref!.form.handleSubmit(_onValid(payload), _onInvalid)();
        });
      const addressResult = addressVisibleStatus.value
        ? addressRef.current?.form.handleSubmit(_onValid(values), _onInvalid)()
        : Promise.resolve();
      const clientInfoResult = clientForm.handleSubmit(_onValid(values), _onInvalid)();

      let cardResult;
      if (isPaymentV2) {
        cardResult = submitCardForm()
          ?.then((card) => {
            card && _onValid(values)({ card: { cardName: card.cardName, token: card.token } });
          })
          ?.catch(_onInvalid);
      } else {
        cardResult = addCardRef.current?.getCardInfo()?.then((cardInfo) => {
          if (cardInfo?.tokenError) {
            addCardRef.current?.getElement()?.clear();
            addCardRef.current?.getElement()?.blur();
            addCardRef.current?.updateErrorHint('');
          }
          cardInfo?.tokenId && _onValid(values)({ chargeToken: cardInfo.tokenId });
        });
      }

      await Promise.all([clientInfoResult, addressResult, ...petListResult, cardResult]);
      if (Object.keys(errors).length) {
        await onInvalid?.(errors, e);
      } else {
        // 放在最后检测，减少接口调用
        const isExisted = await checkExistedLeadWhenSubmit(values);
        if (!isExisted) {
          await onValid(values, e);
        }
      }
    };
  });

  const handleCheckPhoneNumberExistedWithLead = useLatestCallback(async (phoneNumber?: string) => {
    if (!phoneNumber || !enableLeadManagement) return;
    const { isExisted, customer } = await dispatch(checkPhoneNumberExistedWithLead({ phoneNumber }));
    setPhoneNumberExistedInfo({ isExisted, customer });
  });

  const reset = useLatestCallback<UseFormReset<ClientBaseFields>>((fieldValues, options) => {
    const addressValues = pick(fieldValues, addressFormKeys);
    const clientValues = pick(fieldValues, Object.keys(clientValidateSchema.fields));
    clientForm.reset(clientValues, options);
    addressRef.current?.form.reset(addressValues, options);
    petRefs.current = generatePetRefsPlaceholder();
    update();
  });

  const setValue = useLatestCallback<UseFormSetValue<ClientBaseFields>>((field, value, options) => {
    if (isAddressFormField(field)) {
      return addressRef.current?.form.setValue(field, value as any, options);
    }
    return clientForm.setValue(
      field as keyof Yup.InferType<typeof clientValidateSchema>,
      value as Yup.InferType<typeof clientValidateSchema>[keyof Yup.InferType<typeof clientValidateSchema>],
      options,
    );
  });

  const mergedForm = useMemo(() => {
    return {
      handleSubmit,
      setValue,
      reset,
    };
  }, [handleSubmit]);

  const titleSize = lessMode ? '4' : '3';
  const phoneNumber = useWatch({ control: clientForm.control, name: 'phoneNumber' });
  useDebounce(
    () => {
      handleCheckPhoneNumberExistedWithLead(phoneNumber);
    },
    500,
    [phoneNumber],
  );

  const dom = (
    <Form
      form={clientForm}
      footer={null}
      className={classNames('moe-w-[650px]', { 'moe-gap-l': lessMode, 'moe-gap-xl': !lessMode })}
    >
      <BlockContainer
        className="moe-mb-0"
        titleSize={titleSize}
        title={anchorItemMap.clientInfo.title}
        id={anchorItemMap.clientInfo.id}
      >
        <div className="moe-grid moe-grid-cols-2 moe-gap-x-s moe-gap-y-m">
          <div className={cn('moe-col-span-full', { 'moe-hidden': lessMode })}>
            <Form.Item name="avatarPath" valuePropName="src">
              <AvatarUpload />
            </Form.Item>
          </div>
          <Form.Item name="firstName">
            <Input.Text label="First name" isRequired placeholder="Enter first name" />
          </Form.Item>
          <Form.Item name="lastName">
            <Input.Text label="Last name" isRequired placeholder="Enter last name" />
          </Form.Item>
          <Form.Item name="phoneNumber">
            <NumberInputV2
              allowStartWithZero
              inputFormat={RE_INPUT_PHONE}
              preProcess={stripNonNumericInput}
              label="Phone"
              isRequired
              placeholder="Enter phone"
            />
          </Form.Item>
          <Form.Item name="email">
            <Input.Text label="Email" placeholder="Enter email" />
          </Form.Item>
          {phoneNumberExistedInfo?.isExisted && phoneNumberExistedInfo.customer && (
            <Alert
              color="warning"
              className="moe-col-span-full moe-mt-[-8px]"
              isRounded
              isBordered
              isCloseable={false}
              action={
                <Button variant="secondary" onPress={() => goToLeadDetail(phoneNumberExistedInfo.customer!.id)}>
                  View lead details
                </Button>
              }
            >
              {`A lead with the phone number ${business.formatPhoneNumber(phoneNumberExistedInfo.customer.phoneNumber)} already exists.`}
            </Alert>
          )}

          <WithMultiLocation scene="accessClient">
            <Form.Item name="preferredBusinessId">
              <SingleLocationSelector scene="accessClient" label={PREFERRED_BUSINESS_LABEL} isRequired />
            </Form.Item>
          </WithMultiLocation>

          <Form.Item name="referralSourceId" label="Referral source">
            <Select
              isClearable
              options={referralSourcesList.toArray().map((id) => ({
                value: id,
                label: referralSource.mustGetItem(id).sourceName,
              }))}
            />
          </Form.Item>
        </div>
        <div className="moe-flex moe-flex-col moe-gap-y-[24px]">
          <Form.Item name="emergencyContact">
            <Pickup form={clientForm} title="Emergency contact" className="moe-mt-m" />
          </Form.Item>
          <OptionalArea title="Other optional information">
            <Form.Item name="pickupContact">
              <Pickup form={clientForm} title="People authorized to pick up pet" />
            </Form.Item>
          </OptionalArea>
        </div>
      </BlockContainer>

      <Switch>
        <Switch.Case if={!addressVisibleStatus.value}>
          <BlockContainer
            titleSize={titleSize}
            title={anchorItemMap.address.title}
            id={anchorItemMap.address.id}
            className="moe-mb-0"
            actionText="Add"
            onAction={addressVisibleStatus.open}
          >
            <Typography.Text variant="small" className="moe-text-secondary">
              No address added yet.
            </Typography.Text>
          </BlockContainer>
        </Switch.Case>
        <Switch.Case else>
          <BlockContainer
            titleSize={titleSize}
            title={anchorItemMap.address.title}
            id={anchorItemMap.address.id}
            className="moe-mb-0"
            actionText="Remove"
            onAction={addressVisibleStatus.close}
          >
            <AddressForm ref={addressRef} defaultAddress={addressDefaultValues} />
          </BlockContainer>
        </Switch.Case>
      </Switch>

      <Switch>
        <Switch.Case if={Object.keys(petRefs.current).length === 0}>
          <BlockContainer
            titleSize={titleSize}
            title={anchorItemMap.pet.title}
            id={anchorItemMap.pet.id}
            className="moe-mb-0"
            actionText="Add"
            onAction={handleAddPet}
          >
            <Typography.Text variant="small" className="moe-text-secondary">
              No pet added yet.
            </Typography.Text>
          </BlockContainer>
        </Switch.Case>
        <Switch.Case else>
          {Object.keys(petRefs.current).map((key, index) => (
            <BlockContainer
              key={key}
              titleSize={titleSize}
              title={anchorItemMap.pet.title}
              id={index === 0 ? anchorItemMap.pet.id : void 0}
              className="moe-mb-0"
              actionText="Remove"
              onAction={() => handleRemovePet(key)}
            >
              <PetForm ref={buildSetPetRef(key)} lessMode={lessMode} defaultValues={petDefaultValuesMap?.[key]} />
              <Condition if={index === Object.keys(petRefs.current).length - 1}>
                <Button
                  align="start"
                  icon={<MajorPlusOutlined />}
                  variant="tertiary-legacy"
                  onPress={handleAddPet}
                  className="moe-mt-m moe-self-start"
                >
                  Add another pet
                </Button>
              </Condition>
            </BlockContainer>
          ))}
        </Switch.Case>
      </Switch>

      {!!anchorItemMap.cof && !isCardLoading && (
        <BlockContainer
          titleSize={titleSize}
          title={
            <section>
              {anchorItemMap.cof.title}
              <img width="84px" height="28px" src={MoegoPayBadgeDark} />
            </section>
          }
          className="moe-mb-0"
          id={anchorItemMap.cof.id}
        >
          {cardDom}
          <div className="!moe-flex !moe-items-start moe-mt-6">
            <SvgIcon src={SvgIconInfoSvg} size={16} color="#999" />
            <Text variant="small" data-highlight={false} className={'!moe-ml-[8px] !moe-text-[#999] !moe-text-xs'}>
              Enjoy the peace of mind knowing that all cards added will be authenticated through a $0.50 pre-auth
              (immediately dropped), guaranteeing the validity of stored cards for seamless payments!
            </Text>
          </div>
        </BlockContainer>
      )}

      <BlockContainer titleSize={titleSize} title={anchorItemMap.preference.title} id={anchorItemMap.preference.id}>
        <div className="moe-grid moe-grid-cols-1 moe-gap-y-m">
          <Form.Item name="autoMessage" transformer={transformers.numberArrayToStringArray}>
            <CheckboxGroup label="Auto message" orientation="horizontal">
              {AutoMessageKinds.values.map((value) => {
                return (
                  <Checkbox key={value} value={String(value)}>
                    {AutoMessageKinds.mapLabels[value]}
                  </Checkbox>
                );
              })}
            </CheckboxGroup>
          </Form.Item>
          <Form.Item name="apptReminderByList" transformer={transformers.numberArrayToStringArray}>
            <CheckboxGroup label="Appointment Reminder" orientation="horizontal">
              {UnconfirmedReminderKinds.values
                .filter((v) => {
                  if (v === UnconfirmedReminderKinds.DoNotSend) return false;
                  return !(!hasBrandedApp && v === UnconfirmedReminderKinds.PPA);
                })
                .map((value) => {
                  return (
                    <Checkbox key={value} value={String(value)}>
                      {UnconfirmedReminderKinds.mapLabels[value]}
                    </Checkbox>
                  );
                })}
            </CheckboxGroup>
          </Form.Item>
          <Form.Item
            name="isUnsubscribed"
            valuePropName="isSelected"
            transformer={{
              input: (value) => !value,
              output: (value) => (value ? 0 : 1),
            }}
          >
            <Checkbox label="Marketing email">Client accepts marketing emails</Checkbox>
          </Form.Item>
        </div>
      </BlockContainer>
    </Form>
  );

  return { form: mergedForm, getIsDirty, dom };
};

export function generatePetRefsPlaceholder(length = 1) {
  return Array.from({ length }).reduce<Record<string, PetFormRef | null>>((acc, _, index) => {
    acc[String(Date.now() + index)] = null;
    return acc;
  }, {});
}
