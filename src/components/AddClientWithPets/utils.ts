import ImageDefaultCardPng from '../../assets/image/default-card.png';
import ImageMasterCardPng from '../../assets/image/master-card.png';
import ImageVisaCardPng from '../../assets/image/visa-card.png';
import { createEnum } from '../../store/utils/createEnum';

export const AddClientFromApiSourceMap = createEnum({
  Abandon: [1, 'ob'],
  SmartSchedule: [2, 'manual'],
  Call: [3, 'call'],
});

export const dirtyCheckConfig = {
  title: 'Client has unsaved changes',
  content: 'Would you like to save your changes before exiting?',
  confirmText: 'Back to edit',
  cancelText: 'Discard changes',
  className: 'moe-w-[480px]',
  isMaskCloseable: false,
};

export const getCreditCardBrandImg = (brand: string) => {
  const cardImg: Record<string, string> = {
    visa: ImageVisaCardPng,
    mastercard: ImageMasterCardPng,
    unknown: ImageDefaultCardPng,
  };
  return cardImg[brand ?? ''] ?? ImageDefaultCardPng;
};
