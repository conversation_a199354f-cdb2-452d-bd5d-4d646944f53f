import { Heading, Text, cn } from '@moego/ui';
import { useSelector } from 'amos';
import React, { memo } from 'react';
import { petMapBox } from '../../store/pet/pet.boxes';
import { getPetAvatarType } from '../../utils/BusinessUtil';
import { CompressedAvatar } from '../CompressedAvatar/CompressedAvatar';
import { usePetDescription } from '../PetInfo/hooks/usePetDescription';

interface PetItemProps {
  petId: number;
  isActive: boolean;
}
export const PetItem = memo((props: PetItemProps) => {
  const { petId, isActive } = props;
  const [petMap] = useSelector(petMapBox);
  const pet = petMap.mustGetItem(petId);
  const petDescription = usePetDescription(petId);
  return (
    <div
      className={cn(
        'moe-flex moe-flex-1 moe-items-center moe-gap-x-[8px] moe-select-none moe-px-spacing-xs moe-py-[10px] moe-rounded-s',
        {
          'moe-bg-brand-subtle': isActive,
        },
      )}
    >
      <CompressedAvatar.Pet src={pet.avatarPath} type={getPetAvatarType(pet.petTypeId)} size="m" color="neutral" />
      <div className="moe-flex-1 moe-min-w-0 moe-flex moe-flex-col moe-gap-y-[4px]">
        <div className="moe-flex moe-items-center">
          <Heading size="5">{pet.petName}</Heading>
        </div>
        <Text variant="small" className="moe-text-tertiary">
          {petDescription}
        </Text>
      </div>
    </div>
  );
});
