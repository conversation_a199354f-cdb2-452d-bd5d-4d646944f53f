import { LegacySelect as Select } from '@moego/ui';
import { useSelector } from 'amos';
import React, { useMemo } from 'react';
import { getPetDetail } from '../../store/pet/pet.actions';
import { petMapBox } from '../../store/pet/pet.boxes';
import { useQuery } from '../../store/utils/useQuery';
import { memoForwardRef } from '../../utils/react';
import { PetItem } from './PetItem';
import { type PetMultipleSelectorProps, type SelectorOption } from './PetMultipleSelector.types';

/**
 * 目前的版本是折中版本
 * 等待接口支持之后才可以实现 company 级别的 pet 搜索选择
 */
export const PetMultipleSelector = memoForwardRef<any, PetMultipleSelectorProps>((props, ref) => {
  const { petIdList = [] } = props;
  const [petMap] = useSelector(petMapBox);
  const { loading } = useQuery(getPetDetail(petIdList));
  const options = useMemo(
    () =>
      petIdList.map((id) => ({
        label: petMap.mustGetItem(id).petName,
        value: id,
      })),
    [petIdList, petMap],
  );
  return (
    <Select<SelectorOption, true>
      {...props}
      ref={ref}
      isMultiple
      options={options}
      isLoading={loading}
      renderItem={(option) => <PetItem petId={option.data.value} isActive={option.isSelected} />}
      classNames={{
        option: 'moe-p-none',
        menuList: 'moe-p-y-none',
      }}
    />
  );
});
