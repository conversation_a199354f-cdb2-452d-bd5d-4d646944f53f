import { Checkbox, Heading, Radio, RadioGroup } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import React from 'react';
import {
  type StayCardSettingsChildrenKey,
  type StayCardSettingsParentKey,
  setStayPrintCardSettings,
} from '../../../../store/printCard/stayCard/stayCard.actions';
import { selectStayPrintCardSettings } from '../../../../store/printCard/stayCard/stayCard.selectors';
import { Accordion } from '../common/Accordion';

export const StayPrintCardSettings = () => {
  const dispatch = useDispatch();
  const [settings] = useSelector(selectStayPrintCardSettings());

  const handleChange = <PK extends StayCardSettingsParentKey, CK extends StayCardSettingsChildrenKey<PK>>(
    parentKey: PK,
    childrenKey: CK,
    value: boolean,
  ) => {
    dispatch(setStayPrintCardSettings({ parentKey, childrenKey, value }));
  };

  return (
    <div className="moe-m-m">
      <Heading size="4">Settings</Heading>
      <div className="moe-mt-m moe-flex moe-flex-col moe-gap-y-m">
        <Accordion
          title="General"
          isCollapsed={!settings.general.show}
          onChangeCollapsed={(isCollapsed) => {
            handleChange('general', 'show', !isCollapsed);
          }}
        >
          <div className="moe-flex moe-flex-col moe-gap-y-s">
            <Checkbox
              isSelected={settings.general.petPhoto}
              onChange={(isSelected) => {
                handleChange('general', 'petPhoto', isSelected);
              }}
            >
              Pet photo
            </Checkbox>
            <Checkbox
              isSelected={settings.general.petGender}
              onChange={(isSelected) => {
                handleChange('general', 'petGender', isSelected);
              }}
            >
              Pet gender
            </Checkbox>
            <Checkbox
              isSelected={settings.general.petWeight}
              onChange={(isSelected) => {
                handleChange('general', 'petWeight', isSelected);
              }}
            >
              Pet weight
            </Checkbox>
            <Checkbox
              isSelected={settings.general.petAge}
              onChange={(isSelected) => {
                handleChange('general', 'petAge', isSelected);
              }}
            >
              Pet age
            </Checkbox>
            <Checkbox
              isSelected={settings.general.petFixedInfo}
              onChange={(isSelected) => {
                handleChange('general', 'petFixedInfo', isSelected);
              }}
            >
              Pet fixed info
            </Checkbox>
            <Checkbox
              isSelected={settings.general.petAppearance}
              onChange={(isSelected) => {
                handleChange('general', 'petAppearance', isSelected);
              }}
            >
              Pet appearance
            </Checkbox>
          </div>
        </Accordion>
        <Accordion
          title="Appointment info"
          isCollapsed={!settings.appointmentInfo.show}
          onChangeCollapsed={(isCollapsed) => {
            handleChange('appointmentInfo', 'show', !isCollapsed);
          }}
        >
          <div className="moe-flex moe-flex-col moe-gap-y-s">
            <Checkbox
              isSelected={settings.appointmentInfo.petCodes}
              onChange={(isSelected) => {
                handleChange('appointmentInfo', 'petCodes', isSelected);
              }}
            >
              Pet codes
            </Checkbox>
            <Checkbox
              isSelected={settings.appointmentInfo.petNotes}
              onChange={(isSelected) => {
                handleChange('appointmentInfo', 'petNotes', isSelected);
              }}
            >
              Pet notes
            </Checkbox>
            <Checkbox
              isSelected={settings.appointmentInfo.alertNotes}
              onChange={(isSelected) => {
                handleChange('appointmentInfo', 'alertNotes', isSelected);
              }}
            >
              Alert notes
            </Checkbox>
            <Checkbox
              isSelected={settings.appointmentInfo.ticketComments}
              onChange={(isSelected) => {
                handleChange('appointmentInfo', 'ticketComments', isSelected);
              }}
            >
              Ticket comments
            </Checkbox>
            <Checkbox
              isSelected={settings.appointmentInfo.vaccinationStatus}
              onChange={(isSelected) => {
                handleChange('appointmentInfo', 'vaccinationStatus', isSelected);
              }}
            >
              Vaccination status
            </Checkbox>
            <Checkbox
              isSelected={settings.appointmentInfo.vetName}
              onChange={(isSelected) => {
                handleChange('appointmentInfo', 'vetName', isSelected);
              }}
            >
              Vet name
            </Checkbox>
            <Checkbox
              isSelected={settings.appointmentInfo.vetPhoneNumber}
              onChange={(isSelected) => {
                handleChange('appointmentInfo', 'vetPhoneNumber', isSelected);
              }}
            >
              Vet phone number
            </Checkbox>
            <Checkbox
              isSelected={settings.appointmentInfo.healthIssues}
              onChange={(isSelected) => {
                handleChange('appointmentInfo', 'healthIssues', isSelected);
              }}
            >
              Health issue
            </Checkbox>
            <Checkbox
              isSelected={settings.appointmentInfo.petBelongings}
              onChange={(isSelected) => {
                handleChange('appointmentInfo', 'petBelongings', isSelected);
              }}
            >
              Pet belongings
            </Checkbox>
          </div>
        </Accordion>
        <Accordion
          title="Extra services & add-ons"
          isCollapsed={!settings.extraServicesAndAddOns?.show}
          onChangeCollapsed={(isCollapsed) => {
            handleChange('extraServicesAndAddOns', 'show', !isCollapsed);
          }}
          dependencyList={[settings.extraServicesAndAddOns?.showExtraServicesAndAddOns]}
        >
          <div className="moe-flex moe-flex-col moe-gap-y-s">
            <Checkbox
              isSelected={settings.extraServicesAndAddOns?.showExtraServicesAndAddOns}
              onChange={(isSelected) => {
                handleChange('extraServicesAndAddOns', 'showExtraServicesAndAddOns', isSelected);
                if (!isSelected) {
                  handleChange('extraServicesAndAddOns', 'showScheduleTable', false);
                }
              }}
            >
              Show extra services & add-ons
            </Checkbox>
            {settings.extraServicesAndAddOns?.showExtraServicesAndAddOns && (
              <Checkbox
                className="moe-ml-l"
                isSelected={settings.extraServicesAndAddOns?.showScheduleTable}
                onChange={(isSelected) => {
                  handleChange('extraServicesAndAddOns', 'showScheduleTable', isSelected);
                }}
              >
                Show schedule table
              </Checkbox>
            )}
          </div>
        </Accordion>
        <Accordion
          title="Feeding and medication"
          isCollapsed={!settings.feedingAndMedication.show}
          onChangeCollapsed={(isCollapsed) => {
            handleChange('feedingAndMedication', 'show', !isCollapsed);
          }}
          dependencyList={[settings.feedingAndMedication.medicationTableCheckbox]}
        >
          <div className="moe-flex moe-flex-col moe-gap-y-s">
            <Checkbox
              isSelected={settings.feedingAndMedication.feedingSummaryCheckbox}
              onChange={(isSelected) => {
                handleChange('feedingAndMedication', 'feedingSummaryCheckbox', isSelected);
              }}
            >
              Feeding instruction summary
            </Checkbox>
            <Checkbox
              isSelected={settings.feedingAndMedication.feedingTableCheckbox}
              onChange={(isSelected) => {
                handleChange('feedingAndMedication', 'feedingTableCheckbox', isSelected);
              }}
            >
              Feeding schedule table
            </Checkbox>
            <Checkbox
              isSelected={settings.feedingAndMedication.medicationSummaryCheckbox}
              onChange={(isSelected) => {
                handleChange('feedingAndMedication', 'medicationSummaryCheckbox', isSelected);
              }}
            >
              Medication instruction summary
            </Checkbox>
            <Checkbox
              isSelected={settings.feedingAndMedication.medicationTableCheckbox}
              onChange={(isSelected) => {
                if (!isSelected) {
                  handleChange('feedingAndMedication', 'medicationCompactView', false);
                }

                handleChange('feedingAndMedication', 'medicationTableCheckbox', isSelected);
              }}
            >
              Medication schedule table
            </Checkbox>
            {settings.feedingAndMedication.medicationTableCheckbox && (
              <RadioGroup
                classNames={{
                  base: 'moe-ml-[28px]',
                  wrapper: 'moe-gap-[16px]',
                }}
                orientation="horizontal"
                value={settings.feedingAndMedication.medicationCompactView ? 'compact' : 'expanded'}
                onChange={(value) => {
                  handleChange('feedingAndMedication', 'medicationCompactView', value === 'compact');
                }}
              >
                <Radio value="compact">Compact view</Radio>
                <Radio value="expanded">Expanded view</Radio>
              </RadioGroup>
            )}
          </div>
        </Accordion>
      </div>
    </div>
  );
};
