import { Heading } from '@moego/ui';
import { useSelector } from 'amos';
import React, { memo } from 'react';
import { PrintCardTestIds } from '../../../../config/testIds/printCard';
import { selectBoardingDepartureSettings } from '../../../../store/printCard/boardingDeparture/boardingDeparture.selectors';
import { type BoardingPrintCardContent } from '../common/BoardingCard/BoardingCard.types';
import { BoardingCardContent } from '../common/BoardingCard/BoardingCardContent';

export interface BoardingCardContentProps {
  data: BoardingPrintCardContent[];
  date: string;
}

export const BoardingDepartureCard = memo((props: BoardingCardContentProps) => {
  const { data, date } = props;
  const [settings] = useSelector(selectBoardingDepartureSettings);

  return (
    <BoardingCardContent data={data} settings={settings}>
      <Heading size="5" className="moe-text-center" data-testid={PrintCardTestIds.BoardingDeparturePrintCardTitle}>
        Boarding departure list - {date}
      </Heading>
    </BoardingCardContent>
  );
});
