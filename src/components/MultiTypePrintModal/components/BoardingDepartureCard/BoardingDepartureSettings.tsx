import { Checkbox, Heading, Radio, RadioGroup } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import React, { memo } from 'react';
import { setBoardingDeparturePrintCardSettings } from '../../../../store/printCard/boardingDeparture/boardingDeparture.actions';
import { selectBoardingDepartureSettings } from '../../../../store/printCard/boardingDeparture/boardingDeparture.selectors';
import { AdditionalInfoEnum } from '../../../../store/printCard/boardingDeparture/boardingDeparture.utils';
import {
  BoardingCardSortEnum,
  type BoardingPrintCardSettingsChildrenKey,
  type BoardingPrintCardSettingsParentKey,
} from '../../../../store/printCard/printCard.utils';
import { StaticPrintSettingItem } from '../common/StaticPrintSettingItem';
import { UniqueCommentCheckbox } from '../common/PetCodes/UniqueCommentCheckbox';

export const BoardingDepartureSettings = memo(() => {
  const [settings] = useSelector(selectBoardingDepartureSettings);
  const dispatch = useDispatch();

  const handleChange = <
    PK extends BoardingPrintCardSettingsParentKey,
    CK extends BoardingPrintCardSettingsChildrenKey<PK>,
  >(
    parentKey: PK,
    childrenKey: CK,
    value: boolean | string,
  ) => {
    dispatch(setBoardingDeparturePrintCardSettings({ parentKey, childrenKey, value }));
  };

  return (
    <div className="moe-m-m moe-flex moe-flex-col moe-gap-m">
      <Heading size="4">Settings</Heading>
      <StaticPrintSettingItem title="Additional info">
        {AdditionalInfoEnum.values.map((value) => {
          const { label, valueKey } = AdditionalInfoEnum.mapLabels[value];
          return (
            <Checkbox
              key={value}
              isSelected={settings.additionalInfo[valueKey]}
              onChange={(isSelected) => {
                handleChange('additionalInfo', valueKey, isSelected);
              }}
            >
              {label}
            </Checkbox>
          );
        })}
      </StaticPrintSettingItem>
      <StaticPrintSettingItem title="Lodging breakdown">
        <Checkbox
          isSelected={settings.lodgingBreakdown?.showLodgingBreakdown}
          onChange={(isSelected) => {
            handleChange('lodgingBreakdown', 'showLodgingBreakdown', isSelected);
          }}
        >
          Show lodging breakdown
        </Checkbox>
      </StaticPrintSettingItem>
      <StaticPrintSettingItem title="Sort by">
        <RadioGroup value={settings.sort.sortBy} onChange={(value) => handleChange('sort', 'sortBy', value)}>
          {BoardingCardSortEnum.values.map((value) => (
            <Radio key={value} value={value}>
              {BoardingCardSortEnum.mapLabels[value]}
            </Radio>
          ))}
        </RadioGroup>
      </StaticPrintSettingItem>
      <StaticPrintSettingItem title="Pet code">
        <UniqueCommentCheckbox
          isSelected={settings.petCode?.showUniqueComment}
          onChange={(isSelected) => {
            handleChange('petCode', 'showUniqueComment', isSelected);
          }}
        />
      </StaticPrintSettingItem>
    </div>
  );
});
