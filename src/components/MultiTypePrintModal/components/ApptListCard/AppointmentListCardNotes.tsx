import { useSelector } from 'amos';
import React from 'react';
import { selectApptListSettings } from '../../../../store/printCard/appointmentList/appointmentList.selectors';
import { NoteItem } from '../common/ListCard/NoteItem';

export interface ApptListCardNotesProps {
  petNotes: string[];
  alertNotes: string;
  ticketComments: string;
}

export const AppointmentListCardNotes = (props: ApptListCardNotesProps) => {
  const { petNotes, alertNotes, ticketComments } = props;

  const [settings] = useSelector(selectApptListSettings);

  const { showPetNotes, showAlertNotes, showTicketComments } = settings?.commentsAndNotes ?? {};

  // join pet notes with new line
  const petNotesStr = petNotes.join('\n');

  return (
    <div className="moe-flex moe-flex-col moe-gap-y-xxs">
      <NoteItem visible={showPetNotes} title="Pet notes:" content={petNotesStr} />
      <NoteItem visible={showAlertNotes} title="Alert notes:" content={alertNotes} />
      <NoteItem visible={showTicketComments} title="Ticket comments:" content={ticketComments} />
    </div>
  );
};
