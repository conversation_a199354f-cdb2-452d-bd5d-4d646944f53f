import { useSelector } from 'amos';
import React from 'react';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import { type ActivityPrintCardInfo } from '../../../../store/printCard/activityCard/activityCard.actions';
import { getFeedingIntro } from '../../MultiTypePrintModal.options';
import { useActivityCardCompareFn } from '../../hooks/useActivityCardCompareFn';
import { ActivityTable } from './ActivityTable';

export const ActivityFeeding = (props: {
  data: ActivityPrintCardInfo['feedingInstructions'];
  lodgingTypes: ActivityPrintCardInfo['lodgingTypes'];
  lodgingUnits: ActivityPrintCardInfo['lodgingUnits'];
  className?: string;
}) => {
  const [business] = useSelector(selectCurrentBusiness());
  const activityCardCompareFn = useActivityCardCompareFn();
  const { data, className, lodgingTypes, lodgingUnits } = props;
  return (
    <ActivityTable
      title="Feeding"
      data={data
        .map((item) => ({
          ...item,
          instruction: item.instruction.map((feeding) => getFeedingIntro(feeding, business, { showTime: false })),
          lodgingUnitSort: lodgingUnits.find((unit) => unit.id === item.lodgingUnitId)?.sort,
          lodgingTypeSort: lodgingTypes.find((type) => type.id === item.lodgingTypeId)?.sort,
        }))
        .sort(activityCardCompareFn)}
      noDataDesc="No feeding schedule"
      className={className}
    />
  );
};
