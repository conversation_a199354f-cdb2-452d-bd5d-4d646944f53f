import { useSelector } from 'amos';
import React from 'react';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import { type ActivityPrintCardInfo } from '../../../../store/printCard/activityCard/activityCard.actions';
import { getMedicationIntro } from '../../MultiTypePrintModal.options';
import { useActivityCardCompareFn } from '../../hooks/useActivityCardCompareFn';
import { ActivityTable } from './ActivityTable';

export const ActivityMedication = (props: {
  data: ActivityPrintCardInfo['medicationInstructions'];
  lodgingTypes: ActivityPrintCardInfo['lodgingTypes'];
  lodgingUnits: ActivityPrintCardInfo['lodgingUnits'];
  className?: string;
}) => {
  const { data, className, lodgingTypes, lodgingUnits } = props;

  const [business] = useSelector(selectCurrentBusiness());
  const activityCardCompareFn = useActivityCardCompareFn();

  return (
    <ActivityTable
      title="Medication"
      data={data
        .map((item) => ({
          ...item,
          instruction: item.instruction.map((medication) =>
            getMedicationIntro(medication, business, { showTime: false }),
          ),
          lodgingUnitSort: lodgingUnits.find((unit) => unit.id === item.lodgingUnitId)?.sort,
          lodgingTypeSort: lodgingTypes.find((type) => type.id === item.lodgingTypeId)?.sort,
        }))
        .sort(activityCardCompareFn)}
      noDataDesc="No medication schedule"
      className={className}
    />
  );
};
