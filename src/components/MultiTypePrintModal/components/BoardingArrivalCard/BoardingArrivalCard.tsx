import { Heading } from '@moego/ui';
import { useSelector } from 'amos';
import React, { memo } from 'react';
import { PrintCardTestIds } from '../../../../config/testIds/printCard';
import { selectBoardingArrivalSettings } from '../../../../store/printCard/boardingArrival/boardingArrival.selectors';
import { type BoardingPrintCardContent } from '../common/BoardingCard/BoardingCard.types';
import { BoardingCardContent } from '../common/BoardingCard/BoardingCardContent';

export interface BoardingCardContentProps {
  data: BoardingPrintCardContent[];
  date: string;
}

export const BoardingArrivalCard = memo((props: BoardingCardContentProps) => {
  const { data, date } = props;
  const [settings] = useSelector(selectBoardingArrivalSettings);

  return (
    <BoardingCardContent data={data} settings={settings}>
      <Heading size="5" className="moe-text-center" data-testid={PrintCardTestIds.BoardingArrivalPrintCardTitle}>
        Boarding arrival list - {date}
      </Heading>
    </BoardingCardContent>
  );
});
