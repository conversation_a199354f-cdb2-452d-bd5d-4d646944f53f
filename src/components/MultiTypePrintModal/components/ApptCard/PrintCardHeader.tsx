import { CompressedAvatar } from '@moego/business-components';
import { Heading } from '@moego/ui';
import { useSelector } from 'amos';
import { upperFirst } from 'lodash';
import React, { memo } from 'react';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import { type PrintCardInfo } from '../../../../store/printCard/appointmentCard/appointmentCard.actions';
import { apptPrintCardSettingsBox } from '../../../../store/printCard/appointmentCard/appointmentCard.boxes';
import { getPetAvatarType } from '../../../../utils/BusinessUtil';
import { useWeightSuffix } from '../../../../utils/hooks/useWeightSuffix';
import { Condition } from '../../../Condition';
import { getFullDateTime } from '../../MultiTypePrintModal.options';

type PrintCardHeaderProps = {
  petInfo: PrintCardInfo['pet'];
  appointmentInfo: PrintCardInfo['appointment'];
  groomingId: PrintCardInfo['groomingId'];
};

export const PrintCardHeader = memo(({ petInfo, appointmentInfo, groomingId }: PrintCardHeaderProps) => {
  const [{ general: generalSettings }, business] = useSelector(apptPrintCardSettingsBox, selectCurrentBusiness());
  const weightUnit = useWeightSuffix();
  const { petGender: showPetGender, petWeight: showPetWeight, petPhoto: showPetPhoto } = generalSettings;

  const petGender = showPetGender ? ` · ${petInfo.gender}` : '';
  const petWeight = showPetWeight ? ` · ${petInfo.weight} ${weightUnit}` : '';
  const appointmentTime = getFullDateTime(
    appointmentInfo.appointmentDate,
    appointmentInfo.appointmentStartTime,
    business.dateFormat,
    business.timeFormat(),
  );

  return (
    <div className="moe-flex moe-justify-between moe-items-center">
      <Condition if={showPetPhoto}>
        <CompressedAvatar.Pet
          src={petInfo.avatarPath}
          type={getPetAvatarType(petInfo.petTypeId)}
          className="moe-mr-8px-150 moe-h-[40px] moe-w-[40px] moe-bg-neutral-sunken-0"
        />
      </Condition>

      <div className="moe-flex moe-items-center moe-justify-between moe-flex-1">
        <div>
          <Heading size={showPetPhoto ? '5' : '3'}>{upperFirst(petInfo.petName)}</Heading>
          <div className="moe-text-[#000] moe-text-[12px] moe-font-normal">
            {petInfo.breed}
            {petGender}
            {petWeight}
          </div>
        </div>
        <div>
          <div className="moe-flex moe-justify-end moe-text-xs">
            <div className="moe-font-bold">Booking ID:&nbsp;</div>
            <div className="moe-text-primary moe-font-normal">{groomingId}</div>
          </div>
          <div className="moe-flex moe-justify-end moe-text-xs">
            <div className="moe-font-bold">Time:&nbsp;</div>
            <div className="moe-text-primary moe-font-normal">{appointmentTime}</div>
          </div>
        </div>
      </div>
    </div>
  );
});
