import styled from 'styled-components';

export const StyledTableView = styled.div`
  .ant-table-thead > tr > th {
    padding: 0 12px;
  }
  .ant-table-thead {
    height: 32px;
    > tr {
      height: 32px;
      th {
        height: 32px;
        line-height: 1;
        font-size: 12px;
        color: #333;
        font-weight: bold;
      }
    }
    .ant-table-cell {
      padding-top: 0;
      padding-bottom: 0;
      background-color: #fff;
      font-weight: bold;
      color: #666;
    }
  }

  .ant-table-tbody {
    > tr {
      .ant-table-cell {
        padding: 8px 12px;
        font-size: 12px;
        color: #333;
        line-height: 16px;
        font-weight: 500;
      }
    }
  }
`;
