import { useSelector } from 'amos';
import React, { memo } from 'react';
import { type PrintCardInfo } from '../../../../store/printCard/appointmentCard/appointmentCard.actions';
import { apptPrintCardSettingsBox } from '../../../../store/printCard/appointmentCard/appointmentCard.boxes';
import { PrintCardServiceInfo } from './PrintCardServiceInfo/PrintCardServiceInfo';

export interface PrintCardServiceDetailsProps {
  serviceList: PrintCardInfo['serviceList'];
}

export const PrintCardServiceDetails = memo(({ serviceList }: PrintCardServiceDetailsProps) => {
  const [{ serviceDetails: printCardServiceDetailsSettings }] = useSelector(apptPrintCardSettingsBox);
  const showCheckBox = printCardServiceDetailsSettings.serviceStatusCheckBox;
  const showPrice = printCardServiceDetailsSettings.servicePrice;
  return (
    <div>
      {serviceList.map((serviceInfo, index) => {
        return (
          <PrintCardServiceInfo
            key={index}
            showCheckBox={showCheckBox}
            showPrice={showPrice}
            serviceInfo={serviceInfo}
          />
        );
      })}
    </div>
  );
});
