import { useSelector } from 'amos';
import React, { memo } from 'react';
import { StyledTable } from '../../../../container/settings/components/StyledTable';
import { type PrintCardInfo } from '../../../../store/printCard/appointmentCard/appointmentCard.actions';
import { apptPrintCardSettingsBox } from '../../../../store/printCard/appointmentCard/appointmentCard.boxes';
import { useHistoryColumns } from '../../hooks/useHistoryColumns';
import { StyledTableView } from './PrintCardHistoryAndNotes.style';

export interface PrintCardHistoryAndNotesProps {
  historyList: PrintCardInfo['historyList'];
}

export const PrintCardHistoryAndNotes = memo(({ historyList }: PrintCardHistoryAndNotesProps) => {
  const [{ historyAndNotes: historySettings }] = useSelector(apptPrintCardSettingsBox);
  const columns = useHistoryColumns(historySettings.groomer);
  const historyCount = historySettings.historyCount ?? 3;
  return (
    <StyledTableView>
      <StyledTable
        rowKey={(record, i) => `${record.appointmentDate}_${record.startTime}_${record.serviceNameList.join(',')}_${i}`}
        columns={columns}
        dataSource={historyList.slice(0, historyCount)}
        pagination={false}
      ></StyledTable>
    </StyledTableView>
  );
});
