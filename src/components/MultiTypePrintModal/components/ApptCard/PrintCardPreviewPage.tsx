import { useSelector } from 'amos';
import React, { memo } from 'react';
import { type PrintCardInfo } from '../../../../store/printCard/appointmentCard/appointmentCard.actions';
import { apptPrintCardSettingsBox } from '../../../../store/printCard/appointmentCard/appointmentCard.boxes';
import { PrintCardAppointmentInfoType } from '../../hooks/useAppointmentInfoRows';
import { PrintCardAppointmentInfo } from '../common/PrintCardAppointmentInfo/PrintCardAppointmentInfo';
import { PrintCardContainer } from '../common/PrintCardAppointmentInfo/PrintCardContainer';
import { PrintCardHeader } from './PrintCardHeader';
import { PrintCardHistoryAndNotes } from './PrintCardHistoryAndNotes';
import { PrintCardServiceDetails } from './PrintCardServiceDetails';

export interface PrintCardPreviewPageProps {
  cardInfo: PrintCardInfo;
  isLastPage: boolean;
}

export const PrintCardPreviewPage = memo(({ cardInfo, isLastPage }: PrintCardPreviewPageProps) => {
  const [{ historyAndNotes: historySettings, appointmentInfo }] = useSelector(apptPrintCardSettingsBox);

  return (
    <div>
      <div className="moe-w-full moe-min-h-[792px] moe-flex moe-flex-col moe-bg-white moe-relative moe-p-m moe-shadow-elevated print:moe-shadow-none">
        <div className="moe-flex-1">
          <PrintCardHeader
            petInfo={cardInfo.pet}
            appointmentInfo={cardInfo.appointment}
            groomingId={cardInfo.groomingId}
          />
          <PrintCardAppointmentInfo
            appointmentInfo={cardInfo.appointment}
            petInfo={cardInfo.pet}
            customerInfo={cardInfo.customer}
            groomingId={cardInfo.groomingId}
            settings={appointmentInfo}
            showClientName
            type={PrintCardAppointmentInfoType.APPOINTMENT_CARD}
          />
          <PrintCardContainer title="Service details">
            <PrintCardServiceDetails serviceList={cardInfo.serviceList} />
          </PrintCardContainer>
          {historySettings.historyAndNotes && (
            <PrintCardContainer title="History & Notes">
              <PrintCardHistoryAndNotes historyList={cardInfo.historyList} />
            </PrintCardContainer>
          )}
        </div>
        {/* <div className="moe-flex moe-justify-center moe-mt-m">
          <img src={ImageCareContractLogoPng} className="moe-h-[16px]" />
        </div> */}
      </div>
      {!isLastPage && <div className="moe-break-after-page"></div>}
    </div>
  );
});
