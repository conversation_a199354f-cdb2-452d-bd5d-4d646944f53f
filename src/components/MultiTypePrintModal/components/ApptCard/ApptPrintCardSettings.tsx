import { Checkbox, Heading, Input, Switch, Text } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import { isNil } from 'lodash';
import React from 'react';
import {
  type PrintCardSettingChildrenKey,
  type PrintCardSettingParentKey,
} from '../../../../store/printCard/appointmentCard/appointmentCard.actions';
import { setApptPrintCardSettings } from '../../../../store/printCard/appointmentCard/appointmentCard.actions';
import { selectApptPrintCardSettings } from '../../../../store/printCard/appointmentCard/appointmentCard.selectors';
import { Condition } from '../../../Condition';
import { Accordion } from '../common/Accordion';

export const ApptPrintCardSettings = () => {
  const dispatch = useDispatch();
  const [settings] = useSelector(selectApptPrintCardSettings());

  const handleChange = <PK extends PrintCardSettingParentKey, CK extends PrintCardSettingChildrenKey<PK>>(
    parentKey: PK,
    childrenKey: CK,
    value: boolean | number,
  ) => {
    dispatch(setApptPrintCardSettings({ parentKey, childrenKey, value }));
  };

  return (
    <div className="moe-m-m">
      <Heading size="4">Settings</Heading>
      <div className="moe-mt-m moe-flex moe-flex-col moe-gap-y-m">
        <Accordion
          title="General"
          isCollapsed={!settings.general.show}
          onChangeCollapsed={(isCollapsed) => {
            handleChange('general', 'show', !isCollapsed);
          }}
        >
          <div className="moe-flex moe-flex-col moe-gap-y-s">
            <Checkbox
              isSelected={settings.general.petGender}
              onChange={(isSelected) => {
                handleChange('general', 'petGender', isSelected);
              }}
            >
              Pet gender
            </Checkbox>
            <Checkbox
              isSelected={settings.general.petWeight}
              onChange={(isSelected) => {
                handleChange('general', 'petWeight', isSelected);
              }}
            >
              Pet weight
            </Checkbox>
            <Checkbox
              isSelected={settings.general.petPhoto}
              onChange={(isSelected) => {
                handleChange('general', 'petPhoto', isSelected);
              }}
            >
              Pet photo
            </Checkbox>
          </div>
        </Accordion>
        <Accordion
          title="Appointment info"
          isCollapsed={!settings.appointmentInfo.show}
          onChangeCollapsed={(isCollapsed) => {
            handleChange('appointmentInfo', 'show', !isCollapsed);
          }}
        >
          <div className="moe-flex moe-flex-col moe-gap-y-s">
            <Checkbox
              isSelected={settings.appointmentInfo.clientTag}
              onChange={(isSelected) => {
                handleChange('appointmentInfo', 'clientTag', isSelected);
              }}
            >
              Client tag
            </Checkbox>
            <Checkbox
              isSelected={settings.appointmentInfo.phoneNumber}
              onChange={(isSelected) => {
                handleChange('appointmentInfo', 'phoneNumber', isSelected);
              }}
            >
              Phone number
            </Checkbox>
            <Checkbox
              isSelected={settings.appointmentInfo.petCodes}
              onChange={(isSelected) => {
                handleChange('appointmentInfo', 'petCodes', isSelected);
              }}
            >
              Pet codes
            </Checkbox>
            <Checkbox
              isSelected={settings.appointmentInfo.vaccinationStatus}
              onChange={(isSelected) => {
                handleChange('appointmentInfo', 'vaccinationStatus', isSelected);
              }}
            >
              Vaccination status
            </Checkbox>
            <Checkbox
              isSelected={settings.appointmentInfo.alertNotes}
              onChange={(isSelected) => {
                handleChange('appointmentInfo', 'alertNotes', isSelected);
              }}
            >
              Alert notes
            </Checkbox>
            <Checkbox
              isSelected={settings.appointmentInfo.ticketComments}
              onChange={(isSelected) => {
                handleChange('appointmentInfo', 'ticketComments', isSelected);
              }}
            >
              Ticket comments
            </Checkbox>
            <Checkbox
              isSelected={settings.appointmentInfo.agreementStatus}
              onChange={(isSelected) => {
                handleChange('appointmentInfo', 'agreementStatus', isSelected);
              }}
            >
              Agreement status
            </Checkbox>
            <Checkbox
              isSelected={settings.appointmentInfo.clientNotes}
              onChange={(isSelected) => {
                handleChange('appointmentInfo', 'clientNotes', isSelected);
              }}
            >
              Client notes
            </Checkbox>
            <Checkbox
              isSelected={settings.appointmentInfo.petNotes}
              onChange={(isSelected) => {
                handleChange('appointmentInfo', 'petNotes', isSelected);
              }}
            >
              Pet notes
            </Checkbox>
            <Checkbox
              isSelected={settings.appointmentInfo.vetName}
              onChange={(isSelected) => {
                handleChange('appointmentInfo', 'vetName', isSelected);
              }}
            >
              Vet name
            </Checkbox>
            <Checkbox
              isSelected={settings.appointmentInfo.vetPhoneNumber}
              onChange={(isSelected) => {
                handleChange('appointmentInfo', 'vetPhoneNumber', isSelected);
              }}
            >
              Vet phone number
            </Checkbox>
            <Checkbox
              isSelected={settings.appointmentInfo.healthIssues}
              onChange={(isSelected) => {
                handleChange('appointmentInfo', 'healthIssues', isSelected);
              }}
            >
              Health issue
            </Checkbox>
          </div>
        </Accordion>
        <Accordion
          title="Service detail"
          isCollapsed={!settings.serviceDetails.show}
          onChangeCollapsed={(isCollapsed) => {
            handleChange('serviceDetails', 'show', !isCollapsed);
          }}
        >
          <div className="moe-flex moe-flex-col moe-gap-y-s">
            <Checkbox
              isSelected={settings.serviceDetails.servicePrice}
              onChange={(isSelected) => {
                handleChange('serviceDetails', 'servicePrice', isSelected);
              }}
            >
              Price
            </Checkbox>
            <Checkbox
              isSelected={settings.serviceDetails.serviceStatusCheckBox}
              onChange={(isSelected) => {
                handleChange('serviceDetails', 'serviceStatusCheckBox', isSelected);
              }}
            >
              Service status check box
            </Checkbox>
          </div>
        </Accordion>
        <div className="moe-flex moe-flex-col moe-gap-y-s">
          <Switch
            isSelected={settings.historyAndNotes.historyAndNotes}
            label={<Heading size="5">History & Notes</Heading>}
            classNames={{
              base: 'moe-flex moe-flex-row moe-justify-between',
              label: 'moe-mb-none',
            }}
            onChange={(isSelected) => {
              handleChange('historyAndNotes', 'historyAndNotes', isSelected);
            }}
          />
          <Condition if={settings.historyAndNotes.historyAndNotes}>
            <Input.Number
              step={1}
              minValue={1}
              maxValue={5}
              value={settings.historyAndNotes.historyCount}
              label={<Text variant="regular-short">Number of history</Text>}
              classNames={{ base: 'moe-flex moe-justify-between', inputWrapper: 'moe-w-[210px]' }}
              onChange={(value) => {
                if (isNil(value)) return;
                handleChange('historyAndNotes', 'historyCount', value);
              }}
            />
            <Checkbox
              isSelected={settings.historyAndNotes.groomer}
              onChange={(isSelected) => {
                handleChange('historyAndNotes', 'groomer', isSelected);
              }}
            >
              Groomer
            </Checkbox>
          </Condition>
        </div>
      </div>
    </div>
  );
};
