import { Text } from '@moego/ui';
import { useSelector } from 'amos';
import React, { memo, useMemo } from 'react';
import { selectCurrentBusiness } from '../../../../../store/business/business.selectors';
import { type PrintCardInfo } from '../../../../../store/printCard/appointmentCard/appointmentCard.actions';
import { withPl } from '../../../../../utils/calculator';
import { Condition } from '../../../../Condition';
import { getStartTime } from '../../../MultiTypePrintModal.options';
import { PrintCardServiceInfoContainer } from './PrintCardServiceInfoContainer';
import { PrintCardServiceInfoPrice } from './PrintCardServiceInfoPrice';
import { PrintCardServiceInfoServiceName } from './PrintCardServiceInfoServiceName';
import { PrintCardSimpleTable, type PrintCardSimpleTableColumnProps } from './PrintCardSimpleTable';

type PrintCardServiceInfo = PrintCardInfo['serviceList'][number]['operationList'][number];

interface PrintCardServiceInfoGroomingDogWalkingProps {
  serviceInfo: Partial<PrintCardInfo['serviceList'][number]>;
  showPrice?: boolean;
}

export const PrintCardServiceInfoGroomingDogWalking = memo(
  ({ serviceInfo, showPrice }: PrintCardServiceInfoGroomingDogWalkingProps) => {
    const { staffId, staffName, duration, enableOperation, operationList, price, serviceName } = serviceInfo;
    const notShowSet = !showPrice ? new Set(['price']) : new Set<string>();

    const [business] = useSelector(selectCurrentBusiness());

    const columns = useMemo<Array<PrintCardSimpleTableColumnProps<PrintCardServiceInfo>>>(() => {
      return [
        {
          prop: 'staffName',
          renderHeader(column, index, data) {
            const isMainStaff = data.staffId === staffId;
            return <div className="moe-text-tertiary moe-text-xs">{isMainStaff ? 'Main staff' : 'Cooperator'}</div>;
          },
        },
        {
          prop: 'price',
          title: 'Price',
          render(data) {
            return (
              <div className="moe-text-primary moe-text-[10px] moe-font-normal">
                {business.formatAmount(Number(data['price'] || 0))}
              </div>
            );
          },
        },
        {
          prop: 'duration',
          title: 'Duration',
          render(data) {
            return (
              <div className="moe-text-primary moe-text-[10px] moe-font-normal">{withPl(data.duration, 'min')} </div>
            );
          },
        },
        {
          prop: 'startTime',
          title: 'Start Time',
          render(data) {
            return (
              <div className="moe-text-primary moe-text-[10px] moe-font-normal">
                {getStartTime(data['duration'])?.format(business.timeFormat() || 'hh:mm a')}
              </div>
            );
          },
        },
        {
          prop: 'task',
          title: 'Task',
        },
      ];
    }, [business, staffId]);

    return (
      <PrintCardServiceInfoContainer
        outside={
          <Condition if={enableOperation}>
            <div className="moe-mt-xs">
              <PrintCardSimpleTable columns={columns} data={operationList || []} notShowColumns={notShowSet} />
            </div>
          </Condition>
        }
      >
        <PrintCardServiceInfoServiceName name={serviceName} />
        <PrintCardServiceInfoPrice price={price} showPrice={showPrice} />
        <Text variant="small" className="moe-text-[12px]">
          {duration && withPl(duration, 'min')}
        </Text>
        <Text variant="small" className="moe-text-[12px]">
          {enableOperation ? 'Multiple staff' : staffName}
        </Text>
      </PrintCardServiceInfoContainer>
    );
  },
);
