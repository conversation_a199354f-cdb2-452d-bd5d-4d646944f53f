import { Text } from '@moego/ui';
import React, { memo } from 'react';
import { type PrintCardInfo } from '../../../../../store/printCard/appointmentCard/appointmentCard.actions';
import { Condition } from '../../../../Condition';
import { PrintCardServiceInfoContainer } from './PrintCardServiceInfoContainer';
import { PrintCardServiceInfoPrice } from './PrintCardServiceInfoPrice';
import { PrintCardServiceInfoServiceName } from './PrintCardServiceInfoServiceName';

interface PrintCardServiceInfoBoardingDaycareProps {
  isDividUnit?: boolean;
  serviceInfo: Partial<PrintCardInfo['serviceList'][number]>;
  showPrice?: boolean;
}
export const PrintCardServiceInfoBoardingDaycare = memo(
  ({ serviceInfo, isDividUnit, showPrice }: PrintCardServiceInfoBoardingDaycareProps) => {
    const { serviceName, price, priceUnit, lodgingRoom } = serviceInfo;
    return (
      <PrintCardServiceInfoContainer>
        <PrintCardServiceInfoServiceName name={serviceName} />
        <PrintCardServiceInfoPrice
          isDividUnit={isDividUnit}
          price={price}
          priceUnit={priceUnit}
          showPrice={showPrice}
        />
        <Condition if={lodgingRoom}>
          <Text variant="small" className="moe-text-[12px]">
            {lodgingRoom}
          </Text>
        </Condition>
      </PrintCardServiceInfoContainer>
    );
  },
);
