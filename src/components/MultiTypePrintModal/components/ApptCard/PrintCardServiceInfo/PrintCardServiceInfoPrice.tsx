import { type ServicePriceUnit } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { Text } from '@moego/ui';
import { useSelector } from 'amos';
import React, { memo } from 'react';
import { selectCurrentBusiness } from '../../../../../store/business/business.selectors';
import { Condition } from '../../../../Condition';
import { getPriceUnitText } from '../../../../ServiceApplicablePicker/utils/priceUnit';

interface PrintCardServiceInfoPriceProps {
  isDividUnit?: boolean;
  price?: string;
  showPrice?: boolean;
  priceUnit?: ServicePriceUnit;
}
export const PrintCardServiceInfoPrice = memo((props: PrintCardServiceInfoPriceProps) => {
  const { isDividUnit, price, showPrice, priceUnit } = props;
  const [business] = useSelector(selectCurrentBusiness());
  const originPrice = business.formatAmount(Number(price ?? 0));
  return (
    <Condition if={showPrice}>
      <Text variant="small" className="moe-text-[12px]">
        {isDividUnit && priceUnit ? `${originPrice}/${getPriceUnitText(priceUnit)}` : originPrice}
      </Text>
    </Condition>
  );
});
