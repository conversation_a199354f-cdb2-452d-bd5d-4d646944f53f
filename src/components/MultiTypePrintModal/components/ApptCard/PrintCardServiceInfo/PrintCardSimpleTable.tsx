import React from 'react';
import { memo } from '../../../../../utils/react';

export interface PrintCardSimpleTableProps<T> {
  data: T[];
  columns: PrintCardSimpleTableColumnProps<T>[];
  notShowColumns?: Set<string>;
}

export interface PrintCardSimpleTableColumnProps<T> {
  prop: keyof T;
  title?: string;
  render?: (data: T, index: number) => React.ReactNode;
  renderHeader?: (column: PrintCardSimpleTableColumnProps<T>, index: number, data: T) => React.ReactNode;
}

interface SimpleTableDataProps {
  [propNames: string]: any;
}

export function PrintCardSimpleTableWithGeneric<T extends SimpleTableDataProps>({
  columns,
  data,
  notShowColumns,
}: PrintCardSimpleTableProps<T>) {
  return (
    <div className="moe-w-full moe-bg-[#F7F8FA] moe-rounded-[8px] moe-p-[12px] moe-gap-[12px] moe-flex moe-flex-col">
      {data.map((item: T, dataIndex) => {
        return (
          <div className="moe-flex moe-items-start moe-gap-[4px]" key={dataIndex}>
            {columns
              .filter((column) => !notShowColumns?.has(column.prop as string))
              .map((column, index) => {
                return (
                  <div key={index} className="moe-flex-1 moe-flex-wrap">
                    {column.renderHeader ? (
                      column.renderHeader(column, index, item)
                    ) : (
                      <div className="moe-text-[#999] moe-text-[10px]">{column.title}</div>
                    )}
                    {column.render ? (
                      column.render(item, index)
                    ) : (
                      <div className="moe-text-primary moe-text-[10px] moe-font-normal">{item[column.prop]}</div>
                    )}
                  </div>
                );
              })}
          </div>
        );
      })}
    </div>
  );
}

export const PrintCardSimpleTable = memo(PrintCardSimpleTableWithGeneric);
