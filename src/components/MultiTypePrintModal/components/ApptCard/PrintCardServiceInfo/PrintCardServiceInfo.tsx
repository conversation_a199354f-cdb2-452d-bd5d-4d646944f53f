import { ServiceType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { Checkbox } from '@moego/ui';
import React, { memo } from 'react';
import { ComMoegoServerGroomingDtoPrintcardPrintCardServiceInfoServiceItem as PrintCardServiceInfoServiceItem } from '../../../../../openApi/grooming-schema';
import { type PrintCardInfo } from '../../../../../store/printCard/appointmentCard/appointmentCard.actions';
import { Condition } from '../../../../Condition';
import { PrintCardServiceInfoAddon } from './PrintCardServiceInfoAddon';
import { PrintCardServiceInfoBoardingDaycare } from './PrintCardServiceInfoBoardingDaycare';
import { PrintCardServiceInfoGroomingDogWalking } from './PrintCardServiceInfoGroomingDogWalking';

interface PrintCardServiceInfoProps {
  showCheckBox: boolean;
  showPrice: boolean;
  serviceInfo: PrintCardInfo['serviceList'][number];
}

export const PrintCardServiceInfo = memo(({ serviceInfo, showCheckBox, showPrice }: PrintCardServiceInfoProps) => {
  const { serviceItem, serviceType } = serviceInfo;

  const isAddon = serviceType === ServiceType.ADDON;

  const componentMap: Partial<Record<PrintCardServiceInfoServiceItem, React.JSX.Element>> = {
    [PrintCardServiceInfoServiceItem.GROOMING]: (
      <PrintCardServiceInfoGroomingDogWalking serviceInfo={serviceInfo} showPrice={showPrice} />
    ),
    [PrintCardServiceInfoServiceItem.DAYCARE]: (
      <PrintCardServiceInfoBoardingDaycare serviceInfo={serviceInfo} showPrice={showPrice} />
    ),
    [PrintCardServiceInfoServiceItem.BOARDING]: (
      <PrintCardServiceInfoBoardingDaycare isDividUnit serviceInfo={serviceInfo} showPrice={showPrice} />
    ),
    [PrintCardServiceInfoServiceItem.DOG_WALKING]: (
      <PrintCardServiceInfoGroomingDogWalking serviceInfo={serviceInfo} showPrice={showPrice} />
    ),
  };

  const renderServiceInfo = () => {
    if (isAddon) return <PrintCardServiceInfoAddon serviceInfo={serviceInfo} showPrice={showPrice} />;

    if (componentMap[serviceItem]) return componentMap[serviceItem];

    return null;
  };

  return (
    <div className="moe-flex moe-flex-col moe-mb-xs last:moe-mb-none">
      <div className="moe-flex moe-items-center">
        <Condition if={showCheckBox}>
          <Checkbox className="moe-mr-[6px]" isSelected={false} />
        </Condition>
        <Condition if={!showCheckBox}>
          <div className="moe-w-[4px] moe-h-[4px] moe-rounded-[2px] moe-bg-[#202020] moe-mr-[6px]"></div>
        </Condition>
        {renderServiceInfo()}
      </div>
    </div>
  );
});
