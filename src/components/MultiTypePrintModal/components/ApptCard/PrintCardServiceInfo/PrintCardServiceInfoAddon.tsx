import React, { memo } from 'react';
import { type PrintCardInfo } from '../../../../../store/printCard/appointmentCard/appointmentCard.actions';
import { PrintCardServiceInfoContainer } from './PrintCardServiceInfoContainer';
import { PrintCardServiceInfoPrice } from './PrintCardServiceInfoPrice';
import { PrintCardServiceInfoServiceName } from './PrintCardServiceInfoServiceName';

interface PrintCardServiceInfoAddonProps {
  serviceInfo: Partial<PrintCardInfo['serviceList'][number]>;
  showPrice?: boolean;
}
export const PrintCardServiceInfoAddon = memo(({ serviceInfo, showPrice }: PrintCardServiceInfoAddonProps) => {
  const { price, serviceName } = serviceInfo;
  return (
    <PrintCardServiceInfoContainer>
      <PrintCardServiceInfoServiceName name={serviceName} />
      <PrintCardServiceInfoPrice price={price} showPrice={showPrice} />
    </PrintCardServiceInfoContainer>
  );
});
