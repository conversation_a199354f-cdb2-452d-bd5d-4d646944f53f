import React from 'react';
import { type PrintCardInfo } from '../../../../store/printCard/appointmentCard/appointmentCard.actions';
import { PrintCardContentView } from '../common/PrintCardContentView';
import { PrintCardPreviewPage } from './PrintCardPreviewPage';

export const ApptCardContent = (props: { data: PrintCardInfo[] }) => {
  const { data } = props;
  return (
    <PrintCardContentView>
      {data.map((card, index) => {
        return <PrintCardPreviewPage key={index} cardInfo={card} isLastPage={index === data.length - 1} />;
      })}
    </PrintCardContentView>
  );
};
