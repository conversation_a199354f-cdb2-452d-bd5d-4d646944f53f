import { type ListDailyPlaygroupCardResultPetCodeBindingsView } from '@moego/api-web/moego/api/appointment/v1/print_card_api';
import { cn } from '@moego/ui';
import { useSelector } from 'amos';
import React from 'react';
import { type PlaygroupsPrintCard } from '../../../../store/playgroups/playgroups.actions';
import { selectPlaygroupPrintCardSettings } from '../../../../store/playgroups/playgroups.selectors';
import { getServiceAreaColorPattern } from '../../../../utils/utils';
import { Condition } from '../../../Condition';
import { TagSmall } from '../../../Tag/TagSmall';

interface PlaygroupListProps {
  data: PlaygroupsPrintCard;
}
export const PlaygroupList = (props: PlaygroupListProps) => {
  const { playgroupsDetails, petPlaygroups } = props.data;
  const [settings] = useSelector(selectPlaygroupPrintCardSettings());
  const { lightThemeColor } = getServiceAreaColorPattern(playgroupsDetails?.colorCode);

  return (
    <div>
      <div
        style={{ backgroundColor: lightThemeColor }}
        className="moe-px-spacing-xs moe-text-[11px] moe-h-[24px] moe-leading-[24px]"
      >
        {playgroupsDetails?.name} ({playgroupsDetails?.petNumber}/{playgroupsDetails?.maxPetCapacity})
      </div>
      {petPlaygroups?.map((item, index) => {
        const codes: ListDailyPlaygroupCardResultPetCodeBindingsView[] = [];
        const codeWithComment: ListDailyPlaygroupCardResultPetCodeBindingsView[] = [];
        item?.petCodeBindings?.forEach((item) => {
          if (settings.petInfo.showPetCodeUniqueComment && item.uniqueComment) {
            codeWithComment.push(item);
          } else {
            codes.push(item);
          }
        });
        return (
          <div
            key={item.petPlaygroupId}
            className={cn([
              'moe-pb-spacing-xxs moe-px-spacing-xs moe-border-b-[1px] moe-border-solid moe-border-divider',
              {
                'moe-border-b-0': index === petPlaygroups.length - 1,
              },
            ])}
          >
            <div className="moe-flex moe-items-center moe-flex-wrap">
              <div className="moe-flex moe-items-center moe-mt-8px-50">
                <div
                  style={{ backgroundColor: item.petPlaygroupColor }}
                  className="moe-h-[4px] moe-w-[4px] moe-rounded-[2px] moe-flex-shrink-0"
                />
                <div className="moe-text-[11px] moe-leading-[16px] moe-ml-8px-50">
                  {item.customer?.lastName ? `${item.petName} (${item.customer?.lastName})` : item.petName}
                  <span className="moe-text-tertiary">{` ${item.petBreed}`}</span>
                </div>
              </div>

              <Condition if={codes.length > 0}>
                <div className="moe-ml-8px-100 moe-flex moe-items-center moe-gap-[2px] moe-flex-wrap moe-mt-8px-50">
                  {codes.map((item, i) => (
                    <TagSmall
                      key={i}
                      label={item.abbreviation}
                      color={item.color}
                      variant="filled"
                      className="moe-h-[16px]"
                    />
                  ))}
                </div>
              </Condition>
            </div>

            <Condition if={codeWithComment.length > 0}>
              {codeWithComment.map((item, i) => (
                <div className="moe-flex moe-items-start moe-mt-[2px]  moe-ml-8px-100" key={i}>
                  <TagSmall
                    label={item.abbreviation}
                    color={item.color}
                    variant="filled"
                    className="moe-h-[16px] moe-mr-8px-50"
                  />
                  <span className="moe-text-[10px] moe-leading-[16px]">{item.uniqueComment}</span>
                </div>
              ))}
            </Condition>
          </div>
        );
      })}
    </div>
  );
};
