import { Heading } from '@moego/ui';
import { useSelector } from 'amos';
import { chunk } from 'lodash';
import React from 'react';
import ImageCareContractLogoPng from '../../../../assets/image/care-contract-logo.png';
import { type PlaygroupsPrintCard } from '../../../../store/playgroups/playgroups.actions';
import { selectPlaygroupPrintCardSettings } from '../../../../store/playgroups/playgroups.selectors';
import { Condition } from '../../../Condition';
import { PrintCardContentView } from '../common/PrintCardContentView';
import { PlaygroupList } from './PlaygoupList';

// 一行展示的group数量
const GroupCountPerRow = 2;

type PlaygroupCardProps = {
  data: PlaygroupsPrintCard[];
  date: string;
};

export const PlaygroupCard = (props: PlaygroupCardProps) => {
  const { data, date } = props;
  const [settings] = useSelector(selectPlaygroupPrintCardSettings());
  const selectedPlaygroupIds = settings.playgroupOptionsSelected.map((item) => item.id);
  const selectedPlaygroups = data.filter((item) => selectedPlaygroupIds.includes(item.playgroupsDetails.id));

  return (
    <PrintCardContentView>
      {chunk(selectedPlaygroups, GroupCountPerRow).map((arr, index) => {
        return (
          <div
            className="moe-p-spacing-s moe-w-full moe-min-h-[792px] moe-bg-white moe-relative moe-pt-s moe-shadow-elevated print:moe-shadow-none"
            key={index}
          >
            <Condition if={index === 0}>
              <div className="moe-relative moe-mb-8px-150">
                <img src={ImageCareContractLogoPng} className="moe-absolute moe-right-0 moe-top-0 moe-h-[16px]" />
                <Heading size={6} className="moe-mb-8px-200 moe-text-center">{`Playgroups  - ${date}`}</Heading>
              </div>
            </Condition>

            <div className="moe-flex moe-gap-8px-200">
              {arr.map((item) => {
                return (
                  <div
                    key={item.playgroupsDetails.id}
                    className="moe-border-[1px] moe-border-solid moe-border-divider moe-w-[calc(50%-8px)] moe-h-fit"
                  >
                    <PlaygroupList data={item} />
                  </div>
                );
              })}
            </div>
            <div className="moe-break-after-page" />
          </div>
        );
      })}
    </PrintCardContentView>
  );
};
