import { type PlaygroupModel } from '@moego/api-web/moego/models/offering/v1/playgroup_models';
import { Checkbox, Heading, Select } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import React from 'react';
import { setPlaygroupPrintCardSettings } from '../../../../store/playgroups/playgroups.actions';
import { selectPlaygroupPrintCardSettings } from '../../../../store/playgroups/playgroups.selectors';
import { StaticPrintSettingItem } from '../common/StaticPrintSettingItem';

export const PlaygroupPrintCardSettings = () => {
  const dispatch = useDispatch();
  const [settings] = useSelector(selectPlaygroupPrintCardSettings());

  return (
    <div className="moe-m-m">
      <Heading size="4">Settings</Heading>
      <div className="moe-mt-m moe-flex moe-flex-col moe-gap-y-m">
        <StaticPrintSettingItem title="Playgroup" description="Select which playgroups to display on the card">
          <Select.Multiple<PlaygroupModel>
            filter={null}
            value={settings.playgroupOptionsSelected.map((item) => item.id)}
            onChange={(value) => {
              dispatch(
                setPlaygroupPrintCardSettings({
                  playgroupOptionsSelected: settings.playgroupOptions.filter((item) => value.includes(item.id)),
                }),
              );
            }}
          >
            {settings.playgroupOptions.map((item) => (
              <Select.Item key={item.id} title={item.name} />
            ))}
          </Select.Multiple>
        </StaticPrintSettingItem>
        <StaticPrintSettingItem title="Pet info">
          <Checkbox
            isSelected={settings.petInfo.showPetCodeUniqueComment}
            onChange={(isSelected) => {
              dispatch(setPlaygroupPrintCardSettings({ petInfo: { showPetCodeUniqueComment: isSelected } }));
            }}
          >
            Pet code unique comment
          </Checkbox>
        </StaticPrintSettingItem>
      </div>
    </div>
  );
};
