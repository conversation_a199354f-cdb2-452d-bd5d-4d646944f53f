import { Text } from '@moego/ui';
import React from 'react';

export const renderPetBelongings = (petBelongings: string[] = []) => {
  if (petBelongings.length === 0 || petBelongings[0] === '') {
    return (
      <Text variant="small" className="moe-text-[12px]">
        -
      </Text>
    );
  }
  const petBelongingsString = petBelongings[0];
  return petBelongingsString.split('\n').map((petBelonging, index) => {
    return (
      <Text variant="small" className="moe-text-[12px]" key={index}>
        {petBelonging}
      </Text>
    );
  });
};
