import { Markup, Text } from '@moego/ui';
import React, { memo } from 'react';

interface PrintCardColProps {
  title: string;
  content: string[];
  contentRender?: (content: string[]) => React.ReactNode;
  colRange?: 1 | 2 | 3 | 4;
}

const colWidthRange = {
  1: 'moe-flex-[1]',
  2: 'moe-flex-[2]',
  3: 'moe-flex-[3]',
  4: 'moe-w-full',
};

export const PrintCardCol = memo(({ title, content, colRange = 1, contentRender }: PrintCardColProps) => {
  const renderContent = () => {
    if (contentRender) {
      return contentRender(content);
    }

    if (content.length === 0) {
      return (
        <Text variant="small" className="moe-text-[12px]">
          -
        </Text>
      );
    }

    return content.map((item, index) => {
      return (
        <Text variant="small" className="moe-text-[12px]" key={index}>
          {item || '-'}
        </Text>
      );
    });
  };

  return (
    <div className={`moe-flex moe-flex-col moe-flex-shrink-0 ${colWidthRange[colRange]}`}>
      <Markup variant="caption" className="moe-text-[12px]">
        {title}
      </Markup>
      {renderContent()}
    </div>
  );
});
