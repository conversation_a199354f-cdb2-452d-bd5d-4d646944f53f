import { Tag, Text, cn } from '@moego/ui';
import React from 'react';
import type { PrintCardInfo } from '../../../../../store/printCard/appointmentCard/appointmentCard.actions';

export const renderPetCodes = (petCodes: PrintCardInfo['pet']['petCodeBindings'] = []) => {
  if (petCodes.length === 0) {
    return (
      <Text variant="small" className="moe-text-[12px]">
        -
      </Text>
    );
  }
  return (
    <div className="wrapper">
      {petCodes.map((petCode) => {
        return (
          <div
            className={cn('moe-gap-[4px] moe-mr-[8px] moe-mt-[4px] moe-items-center', {
              'moe-flex': petCode.uniqueComment,
              'moe-inline-flex': !petCode.uniqueComment,
            })}
            key={petCode.abbreviation}
          >
            <Tag
              size="s"
              fillColor={petCode.color}
              borderColor={petCode.color}
              color="white"
              label={petCode.abbreviation}
            />
            <div className="moe-text-small moe-text-[12px]">
              <span>{petCode.description}</span>
              {petCode.uniqueComment && <span>{`(${petCode.uniqueComment})`}</span>}
            </div>
          </div>
        );
      })}
    </div>
  );
};
