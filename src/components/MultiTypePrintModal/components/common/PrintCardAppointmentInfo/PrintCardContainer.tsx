import { Heading } from '@moego/ui';
import React, { memo, type ReactNode } from 'react';

export interface PrintCardPreviewContainerProps {
  title: string;
  type?: string;
  rows?: ReactNode[][];
  children?: ReactNode;
}

export const PrintCardContainer = memo(({ title, rows, children }: PrintCardPreviewContainerProps) => {
  return (
    <>
      {/* <div className="moe-h-[1px] moe-bg-[var(--moe-color-border-divider)] moe-mb-s moe-mt-m" /> */}
      <div className="moe-flex moe-flex-col moe-mt-8px-200 moe-pt-s moe-border-t-divider moe-border-t">
        <Heading size="6" className="moe-mb-s">
          {title}
        </Heading>
        {rows &&
          rows.map((cols, index) => {
            return (
              <div key={index} className="moe-flex moe-gap-8px-150 moe-w-full moe-mb-s">
                {cols.map((col, index) => {
                  return <React.Fragment key={index}>{col}</React.Fragment>;
                })}
              </div>
            );
          })}
        {children !== false && children ? children : null}
      </div>
    </>
  );
});
