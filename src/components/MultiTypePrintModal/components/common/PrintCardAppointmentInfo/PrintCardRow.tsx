import React, { memo } from 'react';
import { type AppointmentInfoItem } from './PrintCard.config';
import { PrintCardCol } from './PrintCardCol';

export interface PrintCardRowProps {
  rows: AppointmentInfoItem[][];
}

type ColRange = 1 | 2 | 3 | 4;

export const PrintCardRow = memo(({ rows }: PrintCardRowProps) => {
  return (
    <>
      {rows.map((cols, index) => {
        return (
          <div key={index} className="!moe-flex !moe-gap-[12px] !moe-w-full !moe-mb-[16px]">
            {cols.map((col, index) => {
              return (
                <React.Fragment key={index}>
                  {
                    <PrintCardCol
                      title={col.title}
                      colRange={col.colRange as ColRange}
                      content={col.content as string[]}
                      contentRender={col.contentRender}
                    />
                  }
                </React.Fragment>
              );
            })}
          </div>
        );
      })}
    </>
  );
});
