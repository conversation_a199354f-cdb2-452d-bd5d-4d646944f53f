import React, { memo } from 'react';
import { type PrintCardInfo } from '../../../../../store/printCard/appointmentCard/appointmentCard.actions';
import { type ApptPrintCardSettingsType } from '../../../../../store/printCard/appointmentCard/appointmentCard.boxes';
import { type StayPrintCardInfo } from '../../../../../store/printCard/stayCard/stayCard.actions';
import { type PrintCardAppointmentInfoType, useAppointmentInfoRows } from '../../../hooks/useAppointmentInfoRows';
import { PrintCardContainer } from './PrintCardContainer';
import { PrintCardRow } from './PrintCardRow';

interface PrintCardAppointmentInfoProps {
  appointmentInfo: Pick<PrintCardInfo['appointment'], 'alertNotes' | 'ticketComments'>;
  customerInfo: PrintCardInfo['customer'];
  petInfo: PrintCardInfo['pet'];
  groomingId: PrintCardInfo['groomingId'];
  settings: Partial<ApptPrintCardSettingsType['appointmentInfo']>;
  addOns?: string[];
  showClientName?: boolean;
  extraServiceDetails?: StayPrintCardInfo['extraServiceDetails'];
  petBelongings?: StayPrintCardInfo['petBelongings'];
  type: PrintCardAppointmentInfoType;
}

export const PrintCardAppointmentInfo = memo(
  ({
    appointmentInfo,
    customerInfo,
    petInfo,
    groomingId,
    settings,
    addOns,
    showClientName,
    extraServiceDetails,
    petBelongings,
    type,
  }: PrintCardAppointmentInfoProps) => {
    const printCardRows = useAppointmentInfoRows({
      appointmentInfo,
      customerInfo,
      petInfo,
      groomingId,
      settings,
      addOns,
      options: {
        showClientName,
        type,
      },
      extraServiceDetails,
      petBelongings,
    });

    return (
      <PrintCardContainer title="Appointment Info">
        <PrintCardRow rows={printCardRows} />
      </PrintCardContainer>
    );
  },
);
