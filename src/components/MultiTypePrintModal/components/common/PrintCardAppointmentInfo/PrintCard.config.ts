import type { PrintCardInfo } from '../../../../../store/printCard/appointmentCard/appointmentCard.actions';
import type { StayPrintCardSettingsType } from '../../../../../store/printCard/stayCard/stayCard.boxes';
import { type EnumValues, createEnum } from '../../../../../store/utils/createEnum';

export enum AppointmentInfoColRange {
  ONE_COL = 1,
  TWO_COL_SPECIAL = 2.5, // when in 1/2/3th column, use 2 columns; when in the 4th column, use 1 column
  MULTI_COL = 9, // can be 2/3/4 columns
  FULL_COL = 4, // always 4 columns
}

export type GeneralProperty = 'petGender' | 'petWeight';

export type ServiceDetailProperty = 'servicePrice' | 'serviceStatusCheckBox';

export type HistoryAndNotesProperty = 'groomer' | 'historyCount';

export type AppointmentInfoProperty = EnumValues<
  typeof AppointmentCardAppointmentInfoConfigMap | typeof StayCardAppointmentInfoConfigMap
>;

export const GeneralConfigMap = createEnum({
  PetGender: [
    'petGender',
    {
      title: 'Pet gender',
    },
  ],
  PetWeight: [
    'petWeight',
    {
      title: 'Pet weight',
    },
  ],
});

export interface AppointmentInfoItem {
  title: string;
  colRange: number;
  notShowInSettingBar?: boolean;
  property: AppointmentInfoProperty;
  content: string[] | PrintCardInfo['pet']['petCodeBindings'];
  contentRender?: (content: string[]) => React.ReactNode;
}

export const StayCardAppointmentInfoConfigMap = createEnum({
  PhoneNumber: [
    'phoneNumber',
    {
      title: 'Phone number',
      colRange: AppointmentInfoColRange.ONE_COL,
    },
  ],
  PetCodes: [
    'petCodes',
    {
      title: 'Pet codes',
      colRange: AppointmentInfoColRange.TWO_COL_SPECIAL,
    },
  ],
  PetNotes: [
    'petNotes',
    {
      title: 'Pet notes',
      colRange: AppointmentInfoColRange.MULTI_COL,
    },
  ],
  AlertNotes: [
    'alertNotes',
    {
      title: 'Alert notes',
      colRange: AppointmentInfoColRange.MULTI_COL,
    },
  ],
  TicketComments: [
    'ticketComments',
    {
      title: 'Ticket comments',
      colRange: AppointmentInfoColRange.MULTI_COL,
    },
  ],
  VaccinationStatus: [
    'vaccinationStatus',
    {
      title: 'Vaccination status',
      colRange: AppointmentInfoColRange.ONE_COL,
    },
  ],
  VetInfo: [
    'vetInfo',
    {
      title: 'Vet info',
      colRange: AppointmentInfoColRange.ONE_COL,
      dependencies: {
        fields: ['vetName', 'vetPhoneNumber'],
        validator: (settings: StayPrintCardSettingsType['appointmentInfo']) => {
          return settings.vetName || settings.vetPhoneNumber;
        },
      },
    },
  ],
  HealthIssues: [
    'healthIssues',
    {
      title: 'Health issues',
      colRange: AppointmentInfoColRange.ONE_COL,
    },
  ],
  PetBelongings: [
    'petBelongings',
    {
      title: 'Pet belongings',
      colRange: AppointmentInfoColRange.ONE_COL,
    },
  ],
} as const);

export const AppointmentCardAppointmentInfoConfigMap = createEnum({
  ClientName: [
    'clientName',
    {
      title: 'Client name',
      colRange: AppointmentInfoColRange.ONE_COL,
      notShowInSettingBar: true,
    },
  ],
  ClientTag: [
    'clientTag',
    {
      title: 'Client tag',
      colRange: AppointmentInfoColRange.ONE_COL,
    },
  ],
  PhoneNumber: [
    'phoneNumber',
    {
      title: 'Phone number',
      colRange: AppointmentInfoColRange.ONE_COL,
    },
  ],
  BookingID: [
    'bookingId',
    {
      title: 'Booking ID',
      colRange: AppointmentInfoColRange.ONE_COL,
      notShowInSettingBar: true,
    },
  ],
  PetCodes: [
    'petCodes',
    {
      title: 'Pet codes',
      colRange: AppointmentInfoColRange.ONE_COL,
    },
  ],
  VaccinationStatus: [
    'vaccinationStatus',
    {
      title: 'Vaccination status',
      colRange: AppointmentInfoColRange.ONE_COL,
    },
  ],
  AgreementStatus: [
    'agreementStatus',
    {
      title: 'Agreement status',
      colRange: AppointmentInfoColRange.TWO_COL_SPECIAL,
    },
  ],
  ClientNotes: [
    'clientNotes',
    {
      title: 'Client notes',
      colRange: AppointmentInfoColRange.MULTI_COL,
    },
  ],
  PetNotes: [
    'petNotes',
    {
      title: 'Pet notes',
      colRange: AppointmentInfoColRange.MULTI_COL,
    },
  ],
  AlertNotes: [
    'alertNotes',
    {
      title: 'Alert notes',
      colRange: AppointmentInfoColRange.MULTI_COL,
    },
  ],
  TicketComments: [
    'ticketComments',
    {
      title: 'Ticket comments',
      colRange: AppointmentInfoColRange.MULTI_COL,
    },
  ],
  VetName: [
    'vetName',
    {
      title: 'Vet name',
      colRange: AppointmentInfoColRange.ONE_COL,
    },
  ],
  VetPhoneNumber: [
    'vetPhoneNumber',
    {
      title: 'Vet phone number',
      colRange: AppointmentInfoColRange.ONE_COL,
    },
  ],
  HealthIssues: [
    'healthIssues',
    {
      title: 'Health issues',
      colRange: AppointmentInfoColRange.TWO_COL_SPECIAL,
    },
  ],
  PetBelongings: [
    'petBelongings',
    {
      title: 'Pet belongings',
      colRange: AppointmentInfoColRange.FULL_COL,
    },
  ],
  AddOns: [
    'addOn',
    {
      title: 'Extra services & add-ons',
      colRange: AppointmentInfoColRange.ONE_COL,
    },
  ],
} as const);

export const ServiceDetailConfigMap = createEnum<
  string,
  ServiceDetailProperty,
  {
    title: string;
  }
>({
  Price: [
    'servicePrice',
    {
      title: 'Price',
    },
  ],
  ServiceStatusCheckBox: [
    'serviceStatusCheckBox',
    {
      title: 'Service status check box',
    },
  ],
});
export const HistoryAndNotesConfigMap = createEnum<
  string,
  HistoryAndNotesProperty,
  {
    title: string;
    type: 'switch' | 'step-number';
  }
>({
  Groomer: [
    'groomer',
    {
      title: 'Groomer',
      type: 'switch',
    },
  ],
  HistoryCount: [
    'historyCount',
    {
      title: 'Number of history',
      type: 'step-number',
    },
  ],
});
