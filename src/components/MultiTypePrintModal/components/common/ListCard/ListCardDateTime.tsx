import { useSelector } from 'amos';
import dayjs from 'dayjs';
import { isNumber } from 'lodash/fp';
import { T_MINUTE } from 'monofile-utilities/lib/consts';
import React, { memo } from 'react';
import { selectCurrentBusiness } from '../../../../../store/business/business.selectors';
import type { ApptListPrintCardPetDetail } from '../../../../../store/printCard/appointmentList/appointmentList.actions';
import { Condition } from '../../../../Condition';
import { TextCaptionSmallRegular } from '../../../../Text/TextCaptionSmall';
interface ListCardDateTimeItemProps {
  date?: string;
  startTime?: number;
  endTime?: number;
}

const ListCardDateTimeItem = memo<ListCardDateTimeItemProps>((props) => {
  const { date, startTime, endTime } = props;

  const [business] = useSelector(selectCurrentBusiness);

  const dataFmt = date ? dayjs(date).format(business.dateFormatMD) : null;
  const timeFmt = [startTime, endTime]
    .filter((time) => isNumber(time))
    .map((time) => business.formatFixedTime(time! * T_MINUTE))
    .join(' - ');

  return (
    <div>
      <Condition if={dataFmt}>
        <TextCaptionSmallRegular>{dataFmt}</TextCaptionSmallRegular>
      </Condition>
      <TextCaptionSmallRegular>{timeFmt}</TextCaptionSmallRegular>
    </div>
  );
});

export interface ApptListCardDateTimeProps {
  petServices: ApptListPrintCardPetDetail['petServices'];
}

export const ListCardArrival = memo<ApptListCardDateTimeProps>(({ petServices }) => {
  return (
    <div>
      {petServices.map((petService, index) => {
        const { startDate, startTime } = petService;
        return <ListCardDateTimeItem key={index} date={startDate} startTime={startTime} />;
      })}
    </div>
  );
});

export const ListCardDeparture = memo<ApptListCardDateTimeProps>(({ petServices }) => {
  return (
    <div>
      {petServices.map((petService, index) => {
        const { endDate, endTime } = petService;
        return <ListCardDateTimeItem key={index} date={endDate} startTime={endTime} />;
      })}
    </div>
  );
});

export const ListCardTime = memo<ApptListCardDateTimeProps>(({ petServices }) => {
  return (
    <div>
      {petServices.map((petService, index) => {
        const { startTime, endTime } = petService;
        return <ListCardDateTimeItem key={index} startTime={startTime} endTime={endTime} />;
      })}
    </div>
  );
});
