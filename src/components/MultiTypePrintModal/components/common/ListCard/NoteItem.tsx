import React, { memo } from 'react';
import { Condition } from '../../../../Condition';
import { TextCaptionSmallBold, TextCaptionSmallRegular } from '../../../../Text/TextCaptionSmall';

export interface NoteItemProps {
  visible: boolean;
  title: string;
  content: string;
}

export const NoteItem = memo<NoteItemProps>((props) => {
  const { visible, title, content } = props;

  return (
    <Condition if={visible}>
      <div>
        <TextCaptionSmallBold>{title}</TextCaptionSmallBold>
        <TextCaptionSmallRegular className="moe-whitespace-pre-line">{content || '-'}</TextCaptionSmallRegular>
      </div>
    </Condition>
  );
});
