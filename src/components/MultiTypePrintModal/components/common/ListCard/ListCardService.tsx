import React, { memo } from 'react';
import { printFullName } from '../../../../../store/customer/customer.boxes';
import { type ApptListPrintCardPetDetail } from '../../../../../store/printCard/appointmentList/appointmentList.actions';
import { Condition } from '../../../../Condition';
import { TextCaptionSmallBold, TextCaptionSmallRegular } from '../../../../Text/TextCaptionSmall';

export interface ListCardServiceProps {
  petServices: ApptListPrintCardPetDetail['petServices'];
}

/**
 * display the service + lodging | staff
 */
export const ListCardService = memo<ListCardServiceProps>(({ petServices }) => {
  return (
    <div className="moe-flex moe-flex-col moe-gap-y-xxs">
      {petServices.map((petService, index) => {
        const { serviceName, staffFirstName, staffLastName } = petService;
        const staffName = printFullName(staffFirstName, staffLastName);
        return (
          <div key={index}>
            <TextCaptionSmallBold>{serviceName}</TextCaptionSmallBold>
            <Condition if={staffName}>
              <TextCaptionSmallRegular>{staffName}</TextCaptionSmallRegular>
            </Condition>
          </div>
        );
      })}
    </div>
  );
});
