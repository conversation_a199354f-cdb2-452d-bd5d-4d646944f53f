import React, { memo } from 'react';
import { getMergedLodgingName } from '../../../../../container/Appt/utils/lodgingDisplayHelpers';
import { type ApptListPrintCardPetDetail } from '../../../../../store/printCard/appointmentList/appointmentList.actions';
import { TextCaptionSmallBold } from '../../../../Text/TextCaptionSmall';

export interface ListCardLodgingProps {
  petServices: ApptListPrintCardPetDetail['petServices'];
}

export const ListCardLodging = memo<ListCardLodgingProps>(({ petServices }) => {
  return (
    <div className="moe-flex moe-flex-col moe-gap-y-xxs">
      {petServices.map((petService, index) => {
        const { lodgingUnitName, splitLodgings } = petService;
        const isSplitLodging = !!splitLodgings?.length;
        return (
          <div key={index}>
            <TextCaptionSmallBold>
              {isSplitLodging ? getMergedLodgingName(splitLodgings) : lodgingUnitName}
            </TextCaptionSmallBold>
          </div>
        );
      })}
    </div>
  );
});
