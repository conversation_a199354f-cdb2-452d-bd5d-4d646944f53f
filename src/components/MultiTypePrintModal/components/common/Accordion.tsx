import { MajorChevronDownOutlined } from '@moego/icons-react';
import { Heading, Text, cn } from '@moego/ui';
import React, { type PropsWithChildren, useLayoutEffect, useRef, useState } from 'react';
import { useControllableValue } from '../../../../utils/hooks/useControlledValue';
import { Condition } from '../../../Condition';

const useGetChildrenHeight = (dependencyList: unknown[] = []) => {
  const ref = useRef<HTMLDivElement>(null);
  const [height, setHeight] = useState(0);

  useLayoutEffect(() => {
    if (ref.current?.offsetHeight) {
      setHeight(ref.current.offsetHeight);
    }
  }, dependencyList);

  return [ref, height] as const;
};

export const Accordion = (
  props: PropsWithChildren<{
    title: string;
    description?: string;
    isCollapsed?: boolean;
    onChangeCollapsed?: (value: boolean) => void;
    className?: string;
    defaultCollapsed?: boolean;
    dependencyList?: unknown[];
  }>,
) => {
  const {
    children,
    title,
    description,
    isCollapsed: isCollapsedProp,
    onChangeCollapsed: onChangeCollapsedProp,
    defaultCollapsed = false,
    className,
    dependencyList = [],
  } = props;

  const [isCollapsed, changeCollapsed] = useControllableValue<boolean>({
    value: isCollapsedProp,
    onChange: onChangeCollapsedProp,
    defaultValue: defaultCollapsed,
  });

  const [ref, height] = useGetChildrenHeight(dependencyList);

  const show = !isCollapsed;

  return (
    <div className={cn('moe-flex moe-flex-col', className)}>
      <div
        className="moe-flex moe-items-center moe-justify-between moe-cursor-pointer"
        onClick={() => {
          changeCollapsed(!isCollapsed);
        }}
      >
        <Heading size="5">{title}</Heading>
        <MajorChevronDownOutlined
          className={cn('moe-transition-all moe-duration-300', {
            'moe-rotate-180': show,
            'moe-rotate-0': !show,
          })}
        />
      </div>
      <Condition if={description}>
        <Text variant="small" className="moe-mt-xxs moe-text-tertiary">
          {description}
        </Text>
      </Condition>
      <div
        className={cn('moe-transition-all moe-ease-out moe-duration-300', {
          'moe-overflow-hidden': isCollapsed,
        })}
        // 16 is the margin top of the children
        style={{ maxHeight: show ? height + 16 : 0 }}
      >
        <div className="moe-mt-s" ref={ref}>
          {children}
        </div>
      </div>
    </div>
  );
};
