import { Select, Tag } from '@moego/ui';
import { useSelector } from 'amos';
import React, { type FC, useMemo } from 'react';
import { selectBusinessPetCodes } from '../../../../../store/pet/petCode.selectors';
import { petCodeMapBox } from '../../../../../store/pet/petCode.boxes';

export interface PetCodeSelectorProps {
  value?: string[];
  onChange?: (value: string[]) => void;
}

export const PetCodeSelector: FC<PetCodeSelectorProps> = ({ value, onChange }) => {
  const [petCodeList, petCodeMap] = useSelector(selectBusinessPetCodes, petCodeMapBox);

  const petCodeOptionList = useMemo(() => {
    return petCodeList.toArray().map((id) => {
      const item = petCodeMap.mustGetItem(id);
      return {
        value: `${item.id}`,
        color: item.color,
        desc: item.description,
        codeNumber: item.codeNumber,
      };
    });
  }, [petCodeList, petCodeMap]);

  return (
    <Select.Multiple
      value={value ?? []}
      onChange={(idList) => onChange?.(idList as string[])}
      filter={null}
      formatOptionLabel={(item) => {
        const option = petCodeOptionList.find((i) => i.value === item.props.value);
        if (!option) return null;
        return (
          <div className="moe-flex moe-items-center moe-gap-2 moe-py-[2px]">
            <Tag
              label={option.codeNumber}
              variant="filled"
              size="s"
              color={option.color}
              className="moe-flex-shrink-0"
            />
            <span className="moe-font-manrope moe-text-s-20 moe-tracking-1 moe-text-primary moe-whitespace-normal">
              {option.desc}
            </span>
          </div>
        );
      }}
      placeholder="Select pet code"
    >
      {petCodeOptionList.map(({ value, color, desc, codeNumber }) => (
        <Select.Item
          key={value}
          title={desc}
          icon={<Tag label={codeNumber} variant="filled" size="s" color={color} />}
        />
      ))}
    </Select.Multiple>
  );
};
