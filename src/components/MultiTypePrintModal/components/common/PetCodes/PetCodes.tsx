import React, { useMemo, type FC } from 'react';
import { TagSmall } from '../../../../Tag/TagSmall';
import { TextCaptionSmallRegular } from '../../../../Text/TextCaptionSmall';
import { type ListAppointmentCardResultPetCodeBindingsView } from '@moego/api-web/moego/api/appointment/v1/print_card_api';

export interface PetCodeBindingView extends Omit<ListAppointmentCardResultPetCodeBindingsView, 'petCodeId'> {
  petCodeId: string | number;
}

export interface PetCodesProps {
  petCodeBindings: PetCodeBindingView[];
  petCodeIds?: string[];
  showUniqueComment?: boolean;
}

export interface PetCodeGroup {
  withUniqueComment: PetCodesProps['petCodeBindings'];
  withoutUniqueComment: PetCodesProps['petCodeBindings'];
}

export const PetCodes: FC<PetCodesProps> = (props) => {
  const { petCodeBindings, petCodeIds, showUniqueComment } = props;

  const { withUniqueComment, withoutUniqueComment } = useMemo(() => {
    const filteredPetCodeList = Array.isArray(petCodeIds)
      ? petCodeBindings.filter((petCode) => petCodeIds.includes(String(petCode.petCodeId)))
      : petCodeBindings;
    return filteredPetCodeList.reduce<PetCodeGroup>(
      (acc, item) => {
        const { uniqueComment } = item;
        if (uniqueComment && showUniqueComment) {
          acc.withUniqueComment.push(item);
        } else {
          acc.withoutUniqueComment.push(item);
        }
        return acc;
      },
      { withUniqueComment: [], withoutUniqueComment: [] },
    );
  }, [petCodeBindings, showUniqueComment, petCodeIds]);

  return (
    <div className="moe-flex moe-gap-xxs moe-flex-wrap empty:moe-hidden">
      {withUniqueComment.map(({ abbreviation, color, uniqueComment }) => (
        <div key={abbreviation} className="moe-flex moe-items-center moe-gap-x-xxs">
          <TagSmall
            variant="filled"
            label={abbreviation}
            color={color}
            className="moe-h-8px-200 moe-min-w-8px-300 moe-flex-shrink-0"
          />
          <TextCaptionSmallRegular className="moe-text-primary">{uniqueComment}</TextCaptionSmallRegular>
        </div>
      ))}
      {withoutUniqueComment.map(({ abbreviation, color }) => (
        <TagSmall
          key={abbreviation}
          variant="filled"
          label={abbreviation}
          color={color}
          className="moe-h-8px-200 moe-min-w-8px-300"
        />
      ))}
    </div>
  );
};
