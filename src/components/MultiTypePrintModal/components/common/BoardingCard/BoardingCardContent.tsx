import { Text } from '@moego/ui';
import { useSelector } from 'amos';
import React, { type PropsWithChildren } from 'react';
import ImageCareContractLogoPng from '../../../../../assets/image/care-contract-logo.png';
import { selectCurrentBusiness } from '../../../../../store/business/business.selectors';
import { PrintCardContentView } from '../../common/PrintCardContentView';
import { type BoardingPrintCardContent, type BoardingPrintCardSettings } from './BoardingCard.types';
import { BoardingCardTablePortal } from './BoardingCardTablePortal';

export interface BoardingCardContentProps extends BoardingPrintCardSettings {
  data: BoardingPrintCardContent[];
}

export const BoardingCardContent = (props: PropsWithChildren<BoardingCardContentProps>) => {
  const { data, settings, children } = props;
  const [business] = useSelector(selectCurrentBusiness);

  return (
    <PrintCardContentView>
      {data.map(({ petDetails, lodgingTypes, lodgingUnits }, index) => (
        <div
          key={index}
          className="moe-w-full moe-min-h-[792px] moe-bg-white moe-relative moe-p-m moe-pt-s moe-shadow-elevated print:moe-shadow-none"
        >
          <div className="moe-relative moe-mb-8px-150">
            <img src={ImageCareContractLogoPng} className="moe-absolute moe-right-0 moe-top-0 moe-h-[16px]" />
            <Text variant="small" className="moe-text-center">
              {business.businessName}
            </Text>
            {children}
          </div>
          <BoardingCardTablePortal
            settings={settings}
            petDetails={petDetails}
            lodgingTypes={lodgingTypes}
            lodgingUnits={lodgingUnits}
          />
          <div className="moe-break-after-page" />
        </div>
      ))}
    </PrintCardContentView>
  );
};
