import {
  type ListAppointmentCardResultAppointmentView,
  type ListAppointmentCardResultBoardingViewPetServiceView,
  type ListAppointmentCardResultLodgingTypeDayView,
  type ListAppointmentCardResultPetView,
  type ListBoardingDepartureCardResultBoardingViewPetBelonging,
} from '@moego/api-web/moego/api/appointment/v1/print_card_api';
import { type LodgingUnitModel } from '@moego/api-web/moego/models/offering/v1/lodging_unit_models';
import { type BoardingPrintCardSettingsType } from '../../../../../store/printCard/printCard.utils';

export interface BoardingPrintCardPetDetails {
  appointment: ListAppointmentCardResultAppointmentView;
  pet: ListAppointmentCardResultPetView;
  petServices: ListAppointmentCardResultBoardingViewPetServiceView[];
  petBelongings: ListBoardingDepartureCardResultBoardingViewPetBelonging[];
}

export interface BoardingPrintCardContent {
  petDetails: BoardingPrintCardPetDetails[];
  lodgingTypes: ListAppointmentCardResultLodgingTypeDayView[];
  lodgingUnits: LodgingUnitModel[];
}

export interface BoardingPrintCardSettings {
  settings: BoardingPrintCardSettingsType;
}
