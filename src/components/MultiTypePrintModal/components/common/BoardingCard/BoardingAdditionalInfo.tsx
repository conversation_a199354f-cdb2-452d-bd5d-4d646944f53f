import { type ListBoardingDepartureCardResultBoardingViewPetBelonging } from '@moego/api-web/moego/api/appointment/v1/print_card_api';
import React from 'react';
import { NoteItem } from '../../common/ListCard/NoteItem';
import { type BoardingPrintCardSettings } from './BoardingCard.types';

export interface BoardingAdditionalInfoProps extends BoardingPrintCardSettings {
  petNotes: string[];
  alertNotes: string;
  ticketComments: string;
  petBelongings: ListBoardingDepartureCardResultBoardingViewPetBelonging[];
}

export const BoardingAdditionalInfo = (props: BoardingAdditionalInfoProps) => {
  const { petNotes, alertNotes, ticketComments, petBelongings, settings } = props;

  const { showPetBelongings, showPetNotes, showAlertNotes, showTicketComments } = settings?.additionalInfo ?? {};
  const petBelongingsStr = petBelongings?.map((item) => item.name).join(', ') ?? '';

  // join pet notes with new line
  const petNotesStr = petNotes.join('\n');

  return (
    <div className="moe-flex moe-flex-col moe-gap-y-xxs">
      <NoteItem visible={showPetBelongings} title="Pet belongings:" content={petBelongingsStr} />
      <NoteItem visible={showPetNotes} title="Pet notes:" content={petNotesStr} />
      <NoteItem visible={showAlertNotes} title="Alert notes:" content={alertNotes} />
      <NoteItem visible={showTicketComments} title="Ticket comments:" content={ticketComments} />
    </div>
  );
};
