import { Table } from '@moego/ui';
import React, { memo, useMemo } from 'react';
import { type BoardingPrintCardContent, type BoardingPrintCardSettings } from './BoardingCard.types';
import { useBoardingDepartureListContentColumns } from './hooks/useBoardingDepartureColumns';
import { useBoardingDepartureListCardCompareFn } from './hooks/useBoardingDepartureListCardCompareFn';

export const BoardingCardCardTable = memo<BoardingPrintCardContent & BoardingPrintCardSettings>((props) => {
  const { petDetails, lodgingUnits, lodgingTypes, settings } = props;

  const columns = useBoardingDepartureListContentColumns(settings);

  const boardingDepartureListCardCompareFn = useBoardingDepartureListCardCompareFn(settings);

  const sortedData = useMemo(() => {
    return petDetails
      .map((item) => {
        const firstPetService = item.petServices[0];
        const lodgingUnit = lodgingUnits.find((unit) => unit.id === firstPetService.lodgingUnitId);
        const lodgingType = lodgingTypes.find((type) => type.id === firstPetService.lodgingTypeId);
        return {
          ...item,
          petServices: item.petServices.map((petService) => {
            return {
              ...petService,
              lodgingUnitSort: lodgingUnit?.sort,
              lodgingTypeSort: lodgingType?.sort,
            };
          }),
          settings,
        };
      })
      .sort(boardingDepartureListCardCompareFn);
  }, [petDetails, boardingDepartureListCardCompareFn, lodgingUnits, lodgingTypes, settings]);

  return (
    <Table
      columns={columns}
      data={sortedData}
      getRowId={(row) => `${row.appointment.id}-${row.pet.id}`}
      stickyContainer={window}
      classNames={{
        headTable: 'moe-w-full moe-table-fixed',
        bodyTable: '!moe-w-full',
        bodyRow: 'moe-h-auto',
        headCell:
          'moe-border-l moe-border-[#cbcbcb] last:moe-border-r moe-border-t moe-px-spacing-xs moe-py-spacing-xxs moe-text-[10px] moe-text-primary',
        headSortWrapper: 'moe-block moe-w-full',
        headSortInner: 'moe-block moe-w-full moe-truncate',
        bodyCell:
          'moe-border-l moe-border-[#cbcbcb] last:moe-border-r moe-px-spacing-xs moe-py-spacing-xxs moe-text-[10px] moe-text-primary group-[[data-hovered]]/body-row:moe-bg-transparent',
      }}
    />
  );
});
