import { cn } from '@moego/ui';
import React, { memo, useMemo } from 'react';
import { DEFAULT_AREA_NAME } from '../../../../../store/calendarLatest/calendar_data.utils';
import { Switch } from '../../../../SwitchCase';
import { lodgingTypeSortCompareFn } from '../../../hooks/useLodgingTypeSort';
import { LodgingTypeHeader } from '../../common/ListCard/LodgingTypeHeader';
import { type BoardingPrintCardContent, type BoardingPrintCardSettings } from './BoardingCard.types';
import { BoardingCardCardTable } from './BoardingCardTable';

export const BoardingCardTablePortal = memo<BoardingPrintCardContent & BoardingPrintCardSettings>((props) => {
  const { petDetails, lodgingTypes, lodgingUnits, settings } = props;
  const showLodgingBreakdown = settings?.lodgingBreakdown?.showLodgingBreakdown;

  const lodgingBreakdownData = useMemo(() => {
    if (!showLodgingBreakdown) {
      return [];
    }

    // Create a map to group by lodgingTypeId
    const lodgingMap = new Map();

    for (const item of petDetails) {
      const { petServices } = item;

      for (const petService of petServices) {
        const { lodgingTypeId } = petService;

        if (!lodgingMap.has(lodgingTypeId)) {
          lodgingMap.set(lodgingTypeId, []);
        }

        lodgingMap.get(lodgingTypeId).push(item);
      }
    }

    // Convert the map to the desired output format
    const lodgingBreakdownList = Array.from(lodgingMap.entries())
      .map(([lodgingTypeId, petDetails]) => ({
        lodgingTypeId,
        lodgingTypeSort: lodgingTypes?.find((item) => item.id === lodgingTypeId)?.sort,
        petDetails,
      }))
      .sort(lodgingTypeSortCompareFn);

    return lodgingBreakdownList;
  }, [petDetails, showLodgingBreakdown, lodgingTypes]);

  return (
    <Switch>
      <Switch.Case if={showLodgingBreakdown}>
        {lodgingBreakdownData.map(({ lodgingTypeId, petDetails }, index) => {
          const { name = DEFAULT_AREA_NAME } = lodgingTypes?.find((item) => item.id === lodgingTypeId) ?? {};
          return (
            <div key={lodgingTypeId}>
              <LodgingTypeHeader
                className={cn({
                  'moe-border-t-0': !!index,
                })}
                title={name}
              />
              <BoardingCardCardTable
                settings={settings}
                petDetails={petDetails}
                lodgingUnits={lodgingUnits}
                lodgingTypes={lodgingTypes}
              />
            </div>
          );
        })}
      </Switch.Case>
      <Switch.Case else>
        <BoardingCardCardTable
          settings={settings}
          petDetails={petDetails}
          lodgingUnits={lodgingUnits}
          lodgingTypes={lodgingTypes}
        />
      </Switch.Case>
    </Switch>
  );
});
