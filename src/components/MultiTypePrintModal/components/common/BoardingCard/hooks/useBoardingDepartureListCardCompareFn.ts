import { BoardingCardSortEnum } from '../../../../../../store/printCard/printCard.utils';
import { type BoardingPrintCardPetDetails, type BoardingPrintCardSettings } from '../BoardingCard.types';

type LodgingUnitSortWeight = {
  lodgingTypeSort?: number;
  lodgingUnitSort?: number;
};

const isStringZero = (str: string | undefined) => str === '0';

export const useBoardingDepartureListCardCompareFn = (settings: BoardingPrintCardSettings['settings']) => {
  return (
    a: BoardingPrintCardPetDetails & { petServices: LodgingUnitSortWeight[] },
    b: BoardingPrintCardPetDetails & { petServices: LodgingUnitSortWeight[] },
  ) => {
    const sortBy = settings.sort.sortBy;
    switch (sortBy) {
      case BoardingCardSortEnum.ClientLastName:
        return a.pet.ownerLastName.toLowerCase().localeCompare(b.pet.ownerLastName.toLowerCase());
      case BoardingCardSortEnum.PetName:
        return a.pet.name.toLowerCase().localeCompare(b.pet.name.toLowerCase());
      case BoardingCardSortEnum.Lodging: {
        if (isStringZero(a.petServices[0].lodgingUnitId) && isStringZero(b.petServices[0].lodgingUnitId)) return 0;
        if (isStringZero(a.petServices[0].lodgingUnitId)) return 1;
        if (isStringZero(b.petServices[0].lodgingUnitId)) return -1;
        return (
          a.petServices[0].lodgingTypeSort! - b.petServices[0].lodgingTypeSort! ||
          a.petServices[0].lodgingUnitSort! - b.petServices[0].lodgingUnitSort!
        );
      }
      default:
        return 0; // Keep original order if sort by not recognized
    }
  };
};
