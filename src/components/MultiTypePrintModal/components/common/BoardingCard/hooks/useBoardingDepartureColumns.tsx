import { createColumnHelper } from '@moego/ui';
import React, { useMemo } from 'react';
import { ListCardArrival, ListCardDeparture } from '../../ListCard/ListCardDateTime';
import { ListCardLodging } from '../../ListCard/ListCardLodging';
import { ListCardPet } from '../../ListCard/ListCardPet';
import { ListCardService } from '../../ListCard/ListCardService';
import { BoardingAdditionalInfo } from '../BoardingAdditionalInfo';
import { type BoardingPrintCardPetDetails, type BoardingPrintCardSettings } from '../BoardingCard.types';

interface UseAllColumnsProps {
  showAdditionalInfo: boolean;
  showPetCodeUniqueComment: boolean;
}

const useAllColumns = ({ showAdditionalInfo, showPetCodeUniqueComment }: UseAllColumnsProps) => {
  const columnHelper = createColumnHelper<
    BoardingPrintCardPetDetails & { settings: BoardingPrintCardSettings['settings'] }
  >();

  return useMemo(() => {
    const sizeMap = showAdditionalInfo
      ? {
          pet: 110,
          service: 80,
          lodging: 80,
          arrival: 67,
          departure: 67,
          additionalInfo: 158,
        }
      : {
          pet: 160,
          service: 140,
          lodging: 80,
          arrival: 90,
          departure: 90,
          additionalInfo: 238,
        };

    return {
      Pet: columnHelper.display({
        id: 'pet',
        header: 'Pet',
        size: sizeMap.pet,
        cell: (props) => (
          <ListCardPet
            pet={props.row.original.pet}
            verticalLayout={showAdditionalInfo}
            showUniqueComment={showPetCodeUniqueComment}
          />
        ),
      }),
      Service: columnHelper.display({
        id: 'service',
        header: 'Service',
        size: sizeMap.service,
        cell: (props) => <ListCardService petServices={props.row.original.petServices} />,
      }),
      Lodging: columnHelper.display({
        id: 'lodging',
        header: 'Lodging',
        size: sizeMap.lodging,
        cell: (props) => <ListCardLodging petServices={props.row.original.petServices} />,
      }),
      Arrival: columnHelper.display({
        id: 'arrival',
        header: 'Arrival',
        size: sizeMap.arrival,
        cell: (props) => <ListCardArrival petServices={props.row.original.petServices} />,
      }),
      Departure: columnHelper.display({
        id: 'departure',
        header: 'Departure',
        size: sizeMap.departure,
        cell: (props) => <ListCardDeparture petServices={props.row.original.petServices} />,
      }),
      AdditionalInfo: columnHelper.display({
        id: 'additionalInfo',
        header: 'Additional info',
        size: sizeMap.additionalInfo,
        cell: (props) => {
          const { pet, appointment, petBelongings, settings } = props.row.original;
          const { petNotes } = pet;
          const { alert, comment } = appointment;
          return (
            <BoardingAdditionalInfo
              settings={settings}
              petBelongings={petBelongings}
              petNotes={petNotes}
              alertNotes={alert}
              ticketComments={comment}
            />
          );
        },
      }),
    };
  }, [showAdditionalInfo, showPetCodeUniqueComment, columnHelper]);
};

export const useBoardingDepartureListContentColumns = (settings: BoardingPrintCardSettings['settings']) => {
  const showAdditionalInfo = useMemo(() => {
    return Object.values(settings.additionalInfo).some((value) => value);
  }, [settings]);
  const showPetCodeUniqueComment = settings.petCode?.showUniqueComment;

  const { Pet, Service, Lodging, Arrival, Departure, AdditionalInfo } = useAllColumns({
    showAdditionalInfo,
    showPetCodeUniqueComment,
  });

  const columns = [Pet, Service, Lodging, Arrival, Departure];

  if (showAdditionalInfo) {
    columns.push(AdditionalInfo);
  }

  return columns;
};
