import { Heading, Text } from '@moego/ui';
import React, { type PropsWithChildren } from 'react';
import { Condition } from '../../../Condition';

export interface StaticPrintSettingItemProps {
  title: string;
  description?: string;
  note?: string;
}
export const StaticPrintSettingItem = (props: PropsWithChildren<StaticPrintSettingItemProps>) => {
  const { title, description, note, children } = props;

  return (
    <div className="moe-flex moe-flex-col moe-gap-y-s">
      <div>
        <Heading size="5">{title}</Heading>
        <Condition if={description}>
          <Text variant="small" className="moe-text-tertiary moe-mt-xxs">
            {description}
          </Text>
        </Condition>
      </div>
      {children}
      <Condition if={note}>
        <Text variant="small" className="moe-text-tertiary moe-mt-xxs">
          {note}
        </Text>
      </Condition>
    </div>
  );
};
