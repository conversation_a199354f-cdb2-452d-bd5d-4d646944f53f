import { createGlobalStyle } from 'styled-components';

export const MultiTypePrintModalGlobalStyle = createGlobalStyle`
  @media print {
    table {
      page-break-inside: auto;
    }
    /** avoid page break inside table row */
    tr {
      page-break-inside: avoid;
    }
    /** every page should have a header */
    thead {
      display: table-header-group; 
    }
  }
  @page {
    margin-top: 16px;
  }

  /* Fix for first page margin */
  @page :first {
    margin-top: 0px;
  }
`;
