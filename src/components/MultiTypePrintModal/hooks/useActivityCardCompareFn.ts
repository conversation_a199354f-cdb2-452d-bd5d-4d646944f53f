import { useSelector } from 'amos';
import { isNil, isNumber } from 'lodash';
import { selectActivityPrintCardSettings } from '../../../store/printCard/activityCard/activityCard.selectors';
import { type ActivityTableDataItem } from '../components/ActivityCard/Activity.type';
import { ActivityCardSortEnum } from '../components/ActivityCard/ActivityCard.config';

type LodgingUnitSortWeight = {
  lodgingTypeSort?: number;
  lodgingUnitSort?: number;
};

export const useActivityCardCompareFn = () => {
  const [settings] = useSelector(selectActivityPrintCardSettings());

  return (a: ActivityTableDataItem & LodgingUnitSortWeight, b: ActivityTableDataItem & LodgingUnitSortWeight) => {
    // Compare time first (ascending order)
    if (isNumber(a.time) && isNumber(b.time) && a.time !== b.time) {
      return a.time - b.time;
    }
    const sortBy = settings.sort.sortBy;
    switch (sortBy) {
      case ActivityCardSortEnum.ClientLastName:
        return a.clientLastName.toLowerCase().localeCompare(b.clientLastName.toLowerCase());
      case ActivityCardSortEnum.PetName:
        return a.petName.toLowerCase().localeCompare(b.petName.toLowerCase());
      case ActivityCardSortEnum.Lodging: {
        if (isNil(a.lodgingUnitId) && isNil(b.lodgingUnitId)) return 0;
        if (isNil(a.lodgingUnitId)) return 1;
        if (isNil(b.lodgingUnitId)) return -1;
        return a.lodgingTypeSort! - b.lodgingTypeSort! || a.lodgingUnitSort! - b.lodgingUnitSort!;
      }
      default:
        return 0; // Keep original order if sort by not recognized
    }
  };
};
