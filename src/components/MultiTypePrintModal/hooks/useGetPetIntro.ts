import { useCallback } from 'react';
import { useWeightSuffix } from '../../../utils/hooks/useWeightSuffix';

export const useGetPetIntro = ({ showAppearance = true }: { showAppearance?: boolean } = {}) => {
  const unit = useWeightSuffix();

  const getPetIntro = useCallback(
    (pet: {
      breed: string;
      gender: string;
      weight: string;
      petFixed?: string;
      petAppearance?: string;
      petAppearanceColor?: string;
      petAppearanceNotes?: string;
    }) => {
      const intro = [];
      intro.push(pet.breed);
      intro.push(pet.gender);
      if (pet.weight) {
        intro.push(`${pet.weight} ${unit}`);
      }
      intro.push(pet.petFixed);
      if (showAppearance) {
        intro.push([pet.petAppearanceColor, pet.petAppearanceNotes].filter(Boolean).join('/'));
      }
      return intro.filter(Boolean).join(' · ');
    },
    [unit],
  );

  return getPetIntro;
};
