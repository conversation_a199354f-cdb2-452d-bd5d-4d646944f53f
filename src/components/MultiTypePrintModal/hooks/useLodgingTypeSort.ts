import { isNormal } from '../../../store/utils/identifier';

type LodgingBreakdownSort = {
  lodgingTypeId?: string;
  lodgingTypeSort?: number;
};

export const lodgingTypeSortCompareFn = (a: LodgingBreakdownSort, b: LodgingBreakdownSort) => {
  if (!isNormal(a.lodgingTypeId) && !isNormal(b.lodgingTypeId)) return 0;
  if (!isNormal(a.lodgingTypeId)) return 1;
  if (!isNormal(b.lodgingTypeId)) return -1;
  return a.lodgingTypeSort! - b.lodgingTypeSort!;
};
