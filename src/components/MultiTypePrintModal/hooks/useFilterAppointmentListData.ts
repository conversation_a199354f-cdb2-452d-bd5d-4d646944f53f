/* eslint-disable sonarjs/no-nested-functions */
import { useSelector } from 'amos';
import { selectApptListSettings } from '../../../store/printCard/appointmentList/appointmentList.selectors';
import { useCallback } from 'react';
import { type ApptListPrintCardInfo } from '../../../store/printCard/appointmentList/appointmentList.actions';

export const useFilterAppointmentListData = () => {
  const [settings] = useSelector(selectApptListSettings);
  const {
    petCodeFilter: { petCodeIdList },
  } = settings;

  return useCallback(
    (data: ApptListPrintCardInfo[]) => {
      if (!petCodeIdList?.length) {
        return data;
      }
      return data
        .map((item) => {
          const filteredPetDetailsByCareType = item.petDetailsByCareType.filter((petDetail) =>
            petDetail.pet.petCodeIds.some((petCodeId) => petCodeIdList.includes(petCodeId)),
          );
          return {
            ...item,
            petDetailsByCareType: filteredPetDetailsByCareType,
          };
        })
        .filter(({ petDetailsByCareType }) => petDetailsByCareType.length > 0);
    },
    [petCodeIdList],
  );
};
