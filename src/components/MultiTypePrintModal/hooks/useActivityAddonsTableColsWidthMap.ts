import { useSelector } from 'amos';
import { selectActivityPrintCardSettings } from '../../../store/printCard/activityCard/activityCard.selectors';
import { useMemo } from 'react';

export const useActivityAddonsTableColsWidthMap = (isAddon?: boolean) => {
  const [settings] = useSelector(selectActivityPrintCardSettings);

  const showTicketComments = settings.type.showTicketComments;
  const colWidthMap = useMemo(() => {
    return showTicketComments && isAddon
      ? {
          pet: 'moe-w-[116px]',
          lodging: 'moe-w-[60px]',
          service: 'moe-w-[60px]',
          instruction: 'moe-w-[36px]',
          ticketComments: 'moe-w-[146px]',
          time: 'moe-w-[58px]',
          staff: 'moe-w-[58px]',
          action: 'moe-w-[36px]',
        }
      : {
          pet: 'moe-w-[133px]',
          lodging: 'moe-w-[107px]',
          service: 'moe-w-[107px]',
          instruction: isAddon ? 'moe-w-[36px]' : 'moe-w-[230px]',
          ticketComments: '',
          time: 'moe-w-[58px]',
          staff: 'moe-w-[58px]',
          action: 'moe-w-[36px]',
        };
  }, [showTicketComments, isAddon]);

  return colWidthMap;
};
