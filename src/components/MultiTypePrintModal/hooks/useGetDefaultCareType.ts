import { useStore } from 'amos';
import { isNormal } from '../../../store/utils/identifier';
import { selectSceneCareType } from '../../../store/careType/careType.selectors';
import { AllCareTypeValue, Scene } from '../../../store/service/scene.enum';

export const useGetDefaultCareType = () => {
  const store = useStore();

  return (serviceItemTypes: number[] = []) => {
    const careTypeList = store.select(selectSceneCareType(Scene.PrintStayOrActivityCard));
    const [firstType] = serviceItemTypes ?? [];

    if (serviceItemTypes?.length <= 1 && isNormal(firstType) && careTypeList.includes(firstType)) {
      return firstType;
    }

    return AllCareTypeValue;
  };
};
