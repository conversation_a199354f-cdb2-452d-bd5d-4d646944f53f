import { useSelector } from 'amos';
import { isNormal } from '../../../store/utils/identifier';

import { selectApptListSettings } from '../../../store/printCard/appointmentList/appointmentList.selectors';
import type {
  ApptListPrintCardPetDetail,
  ApptListPrintCardPetServiceView,
} from '../../../store/printCard/appointmentList/appointmentList.actions';
import { AppointmentListCardSortEnum } from '../components/ApptListCard/AppointmentListCardConfig';
import dayjs from 'dayjs';

type LodgingUnitSortWeight = {
  lodgingTypeSort?: number;
  lodgingUnitSort?: number;
  showLodging?: boolean;
};

const getMinStartTimeAndStartDate = (petServices: ApptListPrintCardPetServiceView[]) => {
  let minStartTime = Number.MAX_SAFE_INTEGER;
  let minStartDate = Number.MAX_SAFE_INTEGER;

  for (let i = 0; i < petServices.length; i++) {
    const service = petServices[i];
    if (service.startTime) {
      minStartTime = Math.min(minStartTime, service.startTime!);
    }

    if (service.startDate) {
      minStartDate = Math.min(minStartDate, dayjs(service.startDate!).valueOf());
    }
  }

  return {
    minStartTime,
    minStartDate,
  };
};

export const useAppointmentListCardCompareFn = () => {
  const [settings] = useSelector(selectApptListSettings());

  return (
    a: ApptListPrintCardPetDetail & { petServices: LodgingUnitSortWeight[] },
    b: ApptListPrintCardPetDetail & { petServices: LodgingUnitSortWeight[] },
  ) => {
    const sortBy = settings.sort.sortBy;
    switch (sortBy) {
      case AppointmentListCardSortEnum.ClientLastName:
        return a.pet.ownerLastName.toLowerCase().localeCompare(b.pet.ownerLastName.toLowerCase());
      case AppointmentListCardSortEnum.PetName:
        return a.pet.name.toLowerCase().localeCompare(b.pet.name.toLowerCase());
      case AppointmentListCardSortEnum.Lodging: {
        if (!a.petServices[0].showLodging || !b.petServices[0].showLodging) return 0;
        if (!isNormal(a.petServices[0].lodgingUnitId) && !isNormal(b.petServices[0].lodgingUnitId)) return 0;
        if (!isNormal(a.petServices[0].lodgingUnitId)) return 1;
        if (!isNormal(b.petServices[0].lodgingUnitId)) return -1;
        return (
          a.petServices[0].lodgingTypeSort! - b.petServices[0].lodgingTypeSort! ||
          a.petServices[0].lodgingUnitSort! - b.petServices[0].lodgingUnitSort!
        );
      }
      case AppointmentListCardSortEnum.StartTime: {
        const { minStartTime: aMinStartTime, minStartDate: aMinStartDate } = getMinStartTimeAndStartDate(a.petServices);
        const { minStartTime: bMinStartTime, minStartDate: bMinStartDate } = getMinStartTimeAndStartDate(b.petServices);

        if (aMinStartDate !== bMinStartDate) return aMinStartDate - bMinStartDate;
        if (aMinStartTime !== bMinStartTime) return aMinStartTime - bMinStartTime;
        return a.pet.ownerLastName.toLowerCase().localeCompare(b.pet.ownerLastName.toLowerCase());
      }
      default:
        return 0; // Keep original order if sort by not recognized
    }
  };
};
