import { useSelector } from 'amos';
import dayjs from 'dayjs';
import flatMapDepth from 'lodash/flatMapDepth';
import { T_MINUTE } from 'monofile-utilities/lib/consts';
import React, { useMemo } from 'react';
import { selectCurrentBusiness } from '../../../store/business/business.selectors';
import { type StayPrintCardInfo } from '../../../store/printCard/stayCard/stayCard.actions';
import {
  getMedicationIntro,
  getSortedTimeLabels,
  transformInstructionsWithDates,
} from '../MultiTypePrintModal.options';
import { type CheckTableColumn } from '../components/StayCard/CheckTables';

import chunk from 'lodash/chunk';
import { DATE_FORMAT_EXCHANGE } from '@moego/reporting';
import { truly } from '../../../store/utils/utils';

const MAX_ITEM_COUNT_ONE_ROW = 7;
type MedicationInstructions = StayPrintCardInfo['medicationInstructions'];

export const useMedicationCheckTable = ({
  startDate,
  days,
  medicationInstructions,
}: { startDate: string; days: number; medicationInstructions: MedicationInstructions }) => {
  const [business] = useSelector(selectCurrentBusiness());

  // calculate medication dates by date type
  const { instructionsWithDates, fullMedicationDates } = transformInstructionsWithDates({
    days,
    startDate,
    instructions: medicationInstructions,
  });

  const columnsGroup: CheckTableColumn[][] = useMemo(() => {
    if (!instructionsWithDates.length) {
      return [];
    }

    return Array.from({ length: days })
      .map((_, i) => {
        const day = dayjs(startDate).add(i, 'day');
        const weekday = day.format('ddd');
        const formattedDay = day.format(business.dateFormatMD);

        const date = day.format(DATE_FORMAT_EXCHANGE);
        const isNeedMedicationDate = fullMedicationDates.has(date);

        if (!isNeedMedicationDate) {
          return null;
        }

        return [
          {
            title: `${weekday} ${formattedDay}`,
            dataIndex: 'schedule',
            // use date as key to filter medication instructions
            date,
            width: 100,
            className: 'moe-h-[24px]',
          },
          {
            title: 'Instructions',
            dataIndex: 'instructions',
            className: 'moe-px-8px-150 moe-text-wrap',
          },
          {
            title: '',
            dataIndex: 'signature',
            width: 120,
          },
        ];
      })
      .filter(truly);
  }, [startDate, days, business.dateFormatMD, fullMedicationDates]);

  const dataGroup = useMemo(
    () =>
      columnsGroup.map((columns) => {
        const date = columns.find((item) => item.dataIndex === 'schedule')?.date;
        // filter medication instructions only belong to current date
        const medicationInstructionsInDate = instructionsWithDates.filter((item) =>
          date ? item.dates.includes(date) : false,
        );
        return flatMapDepth(
          medicationInstructionsInDate.map((medication) => {
            return medication.timeList.map((time, index) => ({
              time,
              timeLabel: `${medication.timeLabelList[index]} (${business?.formatFixedTime(time * T_MINUTE)})`,
              instructions: getMedicationIntro(medication, business, { showTime: false }),
            }));
          }),
        )
          .sort((a, b) => a.time - b.time)
          .map((item) => ({
            schedule: item.timeLabel,
            instructions: item.instructions,
            signature: '',
          }));
      }),
    [columnsGroup, instructionsWithDates, business],
  );

  return {
    columnsGroup,
    dataGroup,
  };
};

export const useMedicationCompactViewCheckTable = ({
  days,
  startDate,
  medicationInstructions,
}: { days: number; startDate: string; medicationInstructions: MedicationInstructions }) => {
  const [business] = useSelector(selectCurrentBusiness());

  const medicationScheduleList = useMemo(
    () => [...new Set(getSortedTimeLabels(medicationInstructions))],
    [medicationInstructions],
  );

  // calculate medication dates by date type
  const { fullMedicationDates } = transformInstructionsWithDates({
    days,
    startDate,
    instructions: medicationInstructions,
  });

  const columns: CheckTableColumn[] = useMemo(() => {
    if (!medicationInstructions.length) {
      return [];
    }

    return Array.from({ length: days })
      .map((_, i) => {
        const day = dayjs(startDate).add(i, 'day');
        const weekday = day.format('ddd');
        const formattedDay = day.format(business.dateFormatMD);
        const isNeedMedicationDate = fullMedicationDates.has(day.format(DATE_FORMAT_EXCHANGE));

        if (!isNeedMedicationDate) {
          return null;
        }

        return {
          title: (
            <div className="moe-flex moe-flex-col moe-items-center moe-justify-center moe-py-spacing-xxs">
              <span>
                {weekday} {formattedDay}
              </span>
            </div>
          ),
          dataIndex: formattedDay,
          className: 'moe-min-w-[74px]',
        };
      })
      .filter(truly);
  }, [days, startDate, business.dateFormatMD, fullMedicationDates]);

  const columnsGroup = useMemo(
    () =>
      chunk(columns, MAX_ITEM_COUNT_ONE_ROW).map((item) => {
        item.unshift({
          title: '',
          dataIndex: 'schedule',
          width: 47,
          className: 'moe-max-w-[47px] moe-min-w-[47px] moe-text-wrap',
        });

        return item;
      }),
    [columns],
  );

  const dataGroup = columnsGroup.map((item) =>
    medicationScheduleList.map((schedule) => {
      return {
        ...Object.fromEntries(item.map(({ dataIndex }) => [dataIndex, ''])),
        schedule,
      };
    }),
  );

  return {
    columnsGroup,
    dataGroup,
  };
};
