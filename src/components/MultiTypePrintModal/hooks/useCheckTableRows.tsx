import { useSelector } from 'amos';
import dayjs from 'dayjs';
import React, { type ReactNode } from 'react';
import { selectCurrentBusiness } from '../../../store/business/business.selectors';

const MAX_ITEM_COUNT_ONE_ROW = 10;

export const useCheckTableRows = ({ days, startDate }: { days: number; startDate: string }) => {
  const [business] = useSelector(selectCurrentBusiness());
  const nextRows: { title: ReactNode }[][] = [];
  let rowIndex = 0;

  for (let i = 0; i < days; i++) {
    const day = dayjs(startDate).add(i, 'day');
    const weekday = day.format('ddd');
    const formattedDay = day.format(business.dateFormatMD);
    if (!nextRows[rowIndex]) {
      nextRows[rowIndex] = [];
    }
    nextRows[rowIndex].push({
      title: (
        <div className="moe-flex moe-flex-col moe-items-center moe-justify-center moe-text-[10px] moe-font-bold moe-leading-[12px] moe-py-spacing-xxs">
          <span>{weekday}</span>
          <span>{formattedDay}</span>
        </div>
      ),
    });
    if (nextRows[rowIndex].length === MAX_ITEM_COUNT_ONE_ROW) {
      rowIndex++;
    }
  }

  return nextRows;
};
