import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { createColumnHelper } from '@moego/ui';
import React, { useMemo } from 'react';
import type { ApptListPrintCardPetDetail } from '../../../store/printCard/appointmentList/appointmentList.actions';
import { AppointmentListCardNotes } from '../components/ApptListCard/AppointmentListCardNotes';
import { AppointmentListCardPet } from '../components/ApptListCard/AppointmentListCardPet';
import { ListCardArrival, ListCardDeparture, ListCardTime } from '../components/common/ListCard/ListCardDateTime';
import { ListCardLodging } from '../components/common/ListCard/ListCardLodging';
import { ListCardService } from '../components/common/ListCard/ListCardService';

const useAllColumns = (showCommentsNotes: boolean) => {
  const columnHelper = createColumnHelper<ApptListPrintCardPetDetail>();

  return useMemo(() => {
    const sizeMap = showCommentsNotes
      ? {
          pet: 110,
          service: 80,
          lodging: 80,
          staff: 80,
          arrival: 67,
          departure: 67,
          time: 134,
          comments: 154,
        }
      : {
          pet: 140,
          service: 140,
          lodging: 80,
          staff: 140,
          arrival: 100,
          departure: 100,
          time: 134,
          comments: 236,
        };
    return {
      Pet: columnHelper.display({
        id: 'pet',
        header: 'Pet',
        size: sizeMap.pet,
        cell: (props) => <AppointmentListCardPet pet={props.row.original.pet} />,
      }),
      Service: columnHelper.display({
        id: 'service',
        header: 'Service',
        size: sizeMap.service,
        cell: (props) => <ListCardService petServices={props.row.original.petServices} />,
      }),
      Lodging: columnHelper.display({
        id: 'lodging',
        header: 'Lodging',
        size: sizeMap.lodging,
        cell: (props) => <ListCardLodging petServices={props.row.original.petServices} />,
      }),
      ServiceStaff: columnHelper.display({
        id: 'staff',
        header: 'Service & Staff',
        size: sizeMap.staff,
        cell: (props) => <ListCardService petServices={props.row.original.petServices} />,
      }),
      Arrival: columnHelper.display({
        id: 'arrival',
        header: 'Arrival',
        size: sizeMap.arrival,
        cell: (props) => <ListCardArrival petServices={props.row.original.petServices} />,
      }),
      Departure: columnHelper.display({
        id: 'departure',
        header: 'Departure',
        size: sizeMap.departure,
        cell: (props) => <ListCardDeparture petServices={props.row.original.petServices} />,
      }),
      Time: columnHelper.display({
        id: 'time',
        header: 'Time',
        size: sizeMap.time,
        cell: (props) => <ListCardTime petServices={props.row.original.petServices} />,
      }),
      CommentsNotes: columnHelper.display({
        id: 'comments',
        header: 'Comments & Notes',
        size: sizeMap.comments,
        cell: (props) => {
          const { pet, appointment } = props.row.original;
          const { petNotes } = pet;
          const { alert, comment } = appointment;

          return <AppointmentListCardNotes petNotes={petNotes} alertNotes={alert} ticketComments={comment} />;
        },
      }),
    };
  }, [showCommentsNotes]);
};

export const useApptListContentColumns = (careType: ServiceItemType, showCommentsNotes: boolean) => {
  const { Pet, Service, Lodging, ServiceStaff, Arrival, Departure, Time, CommentsNotes } =
    useAllColumns(showCommentsNotes);

  let columns = [];

  switch (careType) {
    case ServiceItemType.BOARDING:
      columns = [Pet, Service, Lodging, Arrival, Departure];
      break;
    case ServiceItemType.DAYCARE:
    case ServiceItemType.EVALUATION:
      columns = [Pet, Service, Lodging, Time];
      break;
    default:
      columns = [Pet, ServiceStaff, Time];
  }
  if (showCommentsNotes) {
    columns = [...columns, CommentsNotes];
  }

  return columns;
};
