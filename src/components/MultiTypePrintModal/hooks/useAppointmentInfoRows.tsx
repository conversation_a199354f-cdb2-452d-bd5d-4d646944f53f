import { useSelector } from 'amos';
import dayjs from 'dayjs';
import { sumBy } from 'lodash';
import type { ComMoegoServerGroomingDtoPrintcardStayCardAppointmentMainServiceItemType as MainServiceItemType } from '../../../openApi/grooming-schema';
import { selectCurrentBusiness } from '../../../store/business/business.selectors';
import { type PrintCardInfo } from '../../../store/printCard/appointmentCard/appointmentCard.actions';
import { type ApptPrintCardSettingsType } from '../../../store/printCard/appointmentCard/appointmentCard.boxes';
import { type StayPrintCardInfo } from '../../../store/printCard/stayCard/stayCard.actions';
import type { StayPrintCardSettingsType } from '../../../store/printCard/stayCard/stayCard.boxes';
import { renderPetBelongings } from '../components/common/PrintCardAppointmentInfo/ApptCardPetBelongingsContent';
import { renderPetCodes } from '../components/common/PrintCardAppointmentInfo/ApptCardPetCodesContent';
import {
  AppointmentCardAppointmentInfoConfigMap,
  AppointmentInfoColRange,
  type AppointmentInfoItem,
  StayCardAppointmentInfoConfigMap,
} from '../components/common/PrintCardAppointmentInfo/PrintCard.config';

export enum PrintCardAppointmentInfoType {
  STAY_CARD = 'STAY_CARD',
  APPOINTMENT_CARD = 'APPOINTMENT_CARD',
}

export interface PrintCardAppointmentInfoProps {
  addOns?: string[];
  extraServiceDetails?: StayPrintCardInfo['extraServiceDetails'];
  appointmentInfo: Pick<PrintCardInfo['appointment'], 'alertNotes' | 'ticketComments'> & {
    mainServiceItemType?: MainServiceItemType;
  };
  customerInfo: PrintCardInfo['customer'];
  petInfo: PrintCardInfo['pet'];
  groomingId: PrintCardInfo['groomingId'];
  settings: Partial<
    ApptPrintCardSettingsType['appointmentInfo'] &
      StayPrintCardSettingsType['appointmentInfo'] & {
        addOn?: boolean;
      }
  >;
  options?: {
    showClientName?: boolean;
    type?: PrintCardAppointmentInfoType;
  };
  petBelongings?: StayPrintCardInfo['petBelongings'];
}

type PrintCardAppointmentInfoSettingsKeyType = Exclude<
  keyof ApptPrintCardSettingsType['appointmentInfo'] | 'addOn' | keyof StayPrintCardSettingsType['appointmentInfo'],
  'show' | 'clientAndPetNotes'
>;

const changeMapKeyAsValue = (obj: Record<string, string | undefined>, format: string) => {
  return Object.keys(obj).reduce((acc, key) => {
    const value = obj[key];
    acc.push(`${key}, ${dayjs(value).format(format)}`);
    return acc;
  }, [] as string[]);
};

const splitNewlineStringArray = (arr: string[] = []): string[] => {
  return arr.reduce((acc, str) => {
    if (str === null) {
      return acc;
    }
    const splitArr = str.split('\n');
    acc.push(...splitArr);
    return acc;
  }, [] as string[]);
};

const splitNewlineString = (str: string = ''): string[] => {
  if (str === null) {
    return [];
  }
  return str.split('\n');
};

const getPetBelongings = (petBelongings: StayPrintCardInfo['petBelongings']) => {
  return petBelongings.reduce((acc, item, index) => {
    const stringify = `${item.name} (${item.area})`;
    if (index === 0) {
      return stringify;
    }
    return acc + `\n${stringify}`;
  }, '');
};

export const useAppointmentInfoRows = ({
  appointmentInfo,
  customerInfo,
  petInfo,
  settings: appointmentInfoSettings,
  addOns,
  petBelongings,
  options: { showClientName = false, type = PrintCardAppointmentInfoType.STAY_CARD } = {},
}: PrintCardAppointmentInfoProps) => {
  const isStayCardType = type === PrintCardAppointmentInfoType.STAY_CARD;
  const [business] = useSelector(selectCurrentBusiness());

  const defaultValue: Record<
    PrintCardAppointmentInfoSettingsKeyType,
    string[] | PrintCardInfo['pet']['petCodeBindings']
  > = {
    clientTag: [customerInfo.tagList.join(', ')],
    phoneNumber: [customerInfo.phone],
    alertNotes: splitNewlineString(appointmentInfo.alertNotes),
    ticketComments: splitNewlineString(appointmentInfo.ticketComments),
    petCodes: isStayCardType ? petInfo.petCodeBindings : [petInfo.petCodeList.join('. ')],
    vaccinationStatus: changeMapKeyAsValue(petInfo.vaccineMap, business.dateFormat),
    agreementStatus: changeMapKeyAsValue(customerInfo.agreementMap, business.dateFormat),
    clientNotes: splitNewlineStringArray(customerInfo.clientNotes),
    petNotes: splitNewlineStringArray(petInfo.petNotes),
    vetName: [petInfo.vetName],
    vetPhoneNumber: [petInfo.vetPhoneNumber],
    vetInfo: [
      appointmentInfoSettings.vetName ? petInfo.vetName : '',
      appointmentInfoSettings.vetPhoneNumber ? petInfo.vetPhoneNumber : '',
    ].filter(Boolean),
    healthIssues: [petInfo.healthIssues],
    addOn: addOns ?? [],
    petBelongings: petBelongings ? [getPetBelongings(petBelongings)] : [],
  };

  const validateItemDependencies = (value: any, settings: PrintCardAppointmentInfoProps['settings']): boolean => {
    if (!value.dependencies) {
      return true;
    }

    if (value.dependencies.validator) {
      return value.dependencies.validator(settings, defaultValue);
    }

    if (value.dependencies.fields?.length) {
      const allFieldsEnabled = value.dependencies.fields.every(
        (field: PrintCardAppointmentInfoSettingsKeyType) => !!settings[field],
      );

      if (!allFieldsEnabled) {
        return false;
      }
    }

    return true;
  };

  const rows: AppointmentInfoItem[][] = [
    [
      showClientName
        ? {
            title: 'Client name',
            content: [customerInfo.name],
            property: 'clientName',
            colRange: AppointmentInfoColRange.ONE_COL,
          }
        : null,
      // {
      //   title: 'Booking ID',
      //   content: [`#${groomingId}`],
      //   property: 'bookingId',
      //   colRange: AppointmentInfoColRange.ONE_COL,
      // },
    ].filter(Boolean) as AppointmentInfoItem[],
  ];
  let currentRowIndex = 0;
  // notice this is not the same as the length of the array
  let currentRowRange = sumBy(rows[0], (col) => col.colRange);

  const AppointmentInfoConfigMap = isStayCardType
    ? StayCardAppointmentInfoConfigMap
    : AppointmentCardAppointmentInfoConfigMap;

  AppointmentInfoConfigMap.values.forEach((item, index) => {
    const value = AppointmentInfoConfigMap.mapLabels[item as keyof typeof AppointmentInfoConfigMap.mapLabels] as Pick<
      AppointmentInfoItem,
      'notShowInSettingBar' | 'colRange' | 'title'
    >;

    if (value.notShowInSettingBar) {
      return;
    }

    if (!appointmentInfoSettings[item as PrintCardAppointmentInfoSettingsKeyType]) {
      return;
    }

    if (!validateItemDependencies(value, appointmentInfoSettings)) {
      return;
    }

    let currentRow = rows[currentRowIndex];
    const currentItemRange = value.colRange as AppointmentInfoColRange;
    const currentRowData: AppointmentInfoItem = {
      title: value.title,
      content: defaultValue[item as PrintCardAppointmentInfoSettingsKeyType],
      property: item,
      colRange: currentItemRange,
    };

    if (isStayCardType && item === AppointmentInfoConfigMap.PetCodes) {
      currentRowData.contentRender = () =>
        renderPetCodes(currentRowData.content as PrintCardInfo['pet']['petCodeBindings']);
    }

    if (item === AppointmentInfoConfigMap.PetBelongings) {
      currentRowData.contentRender = () => renderPetBelongings(currentRowData.content as string[]);
    }

    switch (currentItemRange) {
      case AppointmentInfoColRange.ONE_COL:
        if (currentRowRange + currentItemRange <= 4) {
          currentRowRange += 1;
        }
        break;
      case AppointmentInfoColRange.TWO_COL_SPECIAL: {
        if (currentRowRange <= 2) {
          currentRowData.colRange = 2;
          currentRowRange += 2;
        } else if (currentRowRange === 3) {
          currentRowData.colRange = 1;
          currentRowRange += 1;
        }
        break;
      }
      case AppointmentInfoColRange.MULTI_COL: {
        currentRowData.colRange = 4;
        if (currentRowRange === 0) {
          currentRowData.colRange = 4;
        } else if (currentRowRange <= 2) {
          currentRowData.colRange = 4 - currentRowRange;
        } else if (currentRowRange >= 3) {
          currentRowData.colRange = 4;
          currentRowIndex++;
          rows.push([]);
          currentRow = rows[currentRowIndex];
        }
        currentRowRange = 4;
        break;
      }
      case AppointmentInfoColRange.FULL_COL: {
        currentRowData.colRange = 4;
        currentRowIndex++;
        rows.push([]);
        currentRow = rows[currentRowIndex];
        break;
      }
    }
    currentRow.push(currentRowData);
    // if not the last row, and the current row is full, then create a new row
    if (currentRowRange === 4 && index !== AppointmentInfoConfigMap.values.length - 1) {
      rows.push([]);
      currentRowRange = 0;
      currentRowIndex++;
    }
  });
  return rows;
};
