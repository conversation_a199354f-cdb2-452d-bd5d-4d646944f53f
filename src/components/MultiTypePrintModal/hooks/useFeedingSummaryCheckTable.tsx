import React, { useMemo } from 'react';

import flatMapDepth from 'lodash/flatMapDepth';
import type { StayPrintCardInfo } from '../../../store/printCard/stayCard/stayCard.actions';

export const useFeedingSummaryCheckTable = ({
  feedingInstructions,
}: {
  feedingInstructions: StayPrintCardInfo['feedingInstructions'];
}) => {
  const feedingScheduleList = useMemo(() => {
    return flatMapDepth(
      feedingInstructions.map((feeding) => {
        return feeding.timeList.map((time, index) => ({
          time,
          timeLabel: feeding.timeLabelList[index],
        }));
      }),
    )
      .sort((a, b) => a.time - b.time)
      .map((item) => ({ label: item.timeLabel, time: item.time }));
  }, [feedingInstructions]);

  const dataGroup = useMemo(() => {
    return [
      feedingInstructions.map((item) => {
        const row: Record<string, string | React.ReactNode> = {
          foodTypeAndSource: `${item.feedingType} (${item.feedingSource})`,
          amountAndUnit: `${item.feedingAmount} ${item.feedingUnit}`,
          instruction: item.feedingInstruction,
          notes: item.feedingNote,
        };

        feedingScheduleList.forEach(({ label, time }) => {
          row[label] = item.timeList.includes(time) ? '' : <div className="moe-text-center">x</div>;
        });

        return row;
      }),
    ];
  }, [feedingInstructions, feedingScheduleList]);

  const columnsGroup = [
    [
      {
        title: 'Food type & source',
        dataIndex: 'foodTypeAndSource',
        className: 'moe-w-[200px]',
      },
      {
        title: 'Amount & Unit',
        dataIndex: 'amountAndUnit',
      },
      ...feedingScheduleList.map(({ label }) => ({
        title: label,
        dataIndex: label,
      })),
      {
        title: 'Instruction',
        dataIndex: 'instruction',
        className: 'moe-w-[100px]',
      },
      {
        title: 'Notes',
        dataIndex: 'notes',
        className: 'moe-w-[100px]',
      },
    ],
  ];

  return {
    dataGroup,
    columnsGroup,
  };
};
