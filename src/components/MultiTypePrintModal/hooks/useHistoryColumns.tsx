import { Text } from '@moego/ui';
import { useSelector } from 'amos';
import type { ColumnsType } from 'antd/es/table';
import React, { useMemo } from 'react';
import { selectCurrentBusiness } from '../../../store/business/business.selectors';
import { type PrintCardInfo } from '../../../store/printCard/appointmentCard/appointmentCard.actions';
import { getStartTime } from '../MultiTypePrintModal.options';

type PrintCardHistoryAndNotes = PrintCardInfo['historyList'][number];
type SingleColumn = ColumnsType<PrintCardHistoryAndNotes>[number];

export const useHistoryColumns = (showGroomer: boolean) => {
  const [business] = useSelector(selectCurrentBusiness());
  const columns = useMemo(() => {
    const columns: ColumnsType<PrintCardHistoryAndNotes> = [];
    const columnDate: SingleColumn = {
      title: (
        <Text variant="caption" className="moe-text-[12px] moe-text-primary">
          Date
        </Text>
      ),
      dataIndex: 'appointmentDate',
      width: '100px',
      render: (_, { appointmentDate, startTime }) => {
        return (
          <div>
            <Text variant="small" className="moe-text-[12px]">
              {business.formatDate(appointmentDate)}
            </Text>
            <Text variant="small" className="moe-text-[12px]">
              {getStartTime(startTime)?.format(business.timeFormat())}
            </Text>
          </div>
        );
      },
    };
    const columnService: SingleColumn = {
      title: (
        <Text variant="caption" className="moe-text-[12px] moe-text-primary">
          Services
        </Text>
      ),
      dataIndex: 'serviceNameList',
      width: '120px',
      render: (res) => {
        return res.map((item: string) => {
          return (
            <Text variant="small" key={item} className="moe-text-[12px]">
              {item}
            </Text>
          );
        });
      },
    };
    const columnGroomer: SingleColumn = {
      title: (
        <Text variant="caption" className="moe-text-[12px] moe-text-primary">
          Groomer
        </Text>
      ),
      dataIndex: 'staffNameList',
      width: '80px',
      render: (res) => {
        return res.map((item: string) => {
          return (
            <Text variant="small" key={item} className="moe-text-[12px]">
              {item}
            </Text>
          );
        });
      },
    };

    const columnTicketComments: SingleColumn = {
      title: (
        <Text variant="caption" className="moe-text-[12px] moe-text-primary">
          Ticket comments
        </Text>
      ),
      dataIndex: 'ticketComment',
      width: '262px',
      render: (res) => {
        return (
          <Text variant="small" className="moe-text-[12px]">
            {res || '-'}
          </Text>
        );
      },
    };

    columns.push(columnDate, columnService);

    if (showGroomer) {
      columns.push(columnGroomer);
    }

    columns.push(columnTicketComments);
    return columns;
  }, [showGroomer]);
  return columns;
};
