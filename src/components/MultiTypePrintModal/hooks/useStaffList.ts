import { useState } from 'react';
import { ALL_STAFF_ID, type Staff } from '../MultiTypePrintModal.options';

export const useStaffList = (staffListParam?: Staff[]) => {
  const [staff, setStaff] = useState(ALL_STAFF_ID);
  const staffList = (staffListParam ?? []).concat([{ name: 'All staff', id: ALL_STAFF_ID }]);
  return {
    selectedStaff: staff,
    setSelectedStaff: setStaff,
    staffList,
  };
};
