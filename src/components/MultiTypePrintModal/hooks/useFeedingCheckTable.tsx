import { useSelector } from 'amos';
import dayjs from 'dayjs';
import chunk from 'lodash/chunk';
import React, { useMemo } from 'react';
import { selectCurrentBusiness } from '../../../store/business/business.selectors';
import { type StayPrintCardInfo } from '../../../store/printCard/stayCard/stayCard.actions';
import { getSortedTimeLabels } from '../MultiTypePrintModal.options';
import { type CheckTableColumn } from '../components/StayCard/CheckTables';

type FeedingInstructions = StayPrintCardInfo['feedingInstructions'];

const MAX_ITEM_COUNT_ONE_ROW = 7;

export const useFeedingCheckTable = ({
  days,
  startDate,
  feedingInstructions,
}: { days: number; startDate: string; feedingInstructions: FeedingInstructions }) => {
  const [business] = useSelector(selectCurrentBusiness());

  const feedingScheduleList = useMemo(
    () => [...new Set(getSortedTimeLabels(feedingInstructions))],
    [feedingInstructions],
  );

  const columns: CheckTableColumn[] = useMemo(() => {
    if (!feedingInstructions.length) {
      return [];
    }

    return Array.from({ length: days }).map((_, i) => {
      const day = dayjs(startDate).add(i, 'day');
      const weekday = day.format('ddd');
      const formattedDay = day.format(business.dateFormatMD);

      return {
        title: (
          <div className="moe-flex moe-flex-col moe-items-center moe-justify-center moe-py-spacing-xxs">
            <span>
              {weekday} {formattedDay}
            </span>
          </div>
        ),
        dataIndex: formattedDay,
        className: 'moe-min-w-[74px]',
      };
    });
  }, [days, startDate, business.dateFormatMD]);

  const columnsGroup = useMemo(
    () =>
      chunk(columns, MAX_ITEM_COUNT_ONE_ROW).map((item) => {
        item.unshift({
          title: '',
          dataIndex: 'schedule',
          width: 47,
          className: 'moe-max-w-[47px] moe-min-w-[47px] moe-text-wrap',
        });

        return item;
      }),
    [columns],
  );

  const dataGroup = columnsGroup.map((item) =>
    feedingScheduleList.map((schedule) => {
      return {
        ...Object.fromEntries(item.map(({ dataIndex }) => [dataIndex, ''])),
        schedule,
      };
    }),
  );

  return {
    columnsGroup,
    dataGroup,
  };
};
