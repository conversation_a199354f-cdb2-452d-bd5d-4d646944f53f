/*
 * @since 2021-01-11 18:10:33
 * <AUTHOR> <<EMAIL>>
 */
/** @type {import('@commitlint/types').UserConfig} */
export default {
  extends: ['@commitlint/config-angular'],
  rules: {
    'references-empty': [2, 'never'],
    'header-max-length': [2, 'always', 110],
  },
  parserPreset: {
    parserOpts: {
      issuePrefixes: [
        'GROOM-',
        'OBV-',
        'APP-',
        'CS-',
        'MOE-',
        'TECH-',
        'ERP-',
        'FDN-',
        'MER-',
        'FIN-',
        'IFRFE-',
        'CA-',
        'CRM-',
        'DATA-',
        'EN-',
        'IFRBE-',
        'ENT-',
        'GRM-',
      ],
    },
  },
};
