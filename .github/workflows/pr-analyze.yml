name: PR Analyze

on:
  pull_request:
    types: [opened, synchronize, reopened]

jobs:
  analyze:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Install pnpm
        uses: pnpm/action-setup@v4

      - name: Set node
        uses: actions/setup-node@v4
        with:
          node-version: 22.x
          cache: pnpm

      - name: Setup npm
        uses: MoeGolibrary/moego-actions-tool/.github/actions/setup-npm/@production
        with:
          NPM_PUBLISHER_USR: ${{ vars.NPM_PUBLISHER_USR }}
          NPM_PUBLISHER_PSW: ${{ secrets.NPM_PUBLISHER_PSW }}

      - name: Install dependencies
        run: pnpm install

      - name: Run Effect
        run: pnpm tsx ./ci/effect.ts

      - name: Run Analyze
        run: pnpm tsx ./ci/pr-analyze-symbol.ts
        env:
          REPO_NAME: ${{ github.event.repository.name }}
          OWNER_NAME: ${{ github.event.repository.owner.login }}
          PR_NUMBER: ${{ github.event.pull_request.number }}
          ADMIN_TOKEN_GITHUB: ${{ secrets.ADMIN_TOKEN_GITHUB }}
